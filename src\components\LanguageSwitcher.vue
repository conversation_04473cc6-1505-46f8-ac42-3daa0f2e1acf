<template>
    <div class="language-switcher">
        <el-select v-model="currentLanguage" @change="changeLanguage" class="language-select" placeholder="选择语言">
            <el-option value="zh" :label="$t('language.chinese')">
                {{ $t('language.chinese') }}
            </el-option>
            <el-option value="en" :label="$t('language.english')">
                {{ $t('language.english') }}
            </el-option>
        </el-select>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()
const currentLanguage = ref('zh')

onMounted(() => {
    // 从localStorage读取保存的语言设置
    const savedLanguage = localStorage.getItem('language') || 'zh'
    currentLanguage.value = savedLanguage
    locale.value = savedLanguage
})

const changeLanguage = (lang) => {
    locale.value = lang
    localStorage.setItem('language', lang)
    console.log('语言已切换到:', lang, '当前locale:', locale.value)
}
</script>

<style scoped>
.language-switcher {
    display: inline-block;
}

.language-select {
    width: 120px;
}

:deep(.el-select .el-input__inner) {
    background-color: rgba(12, 16, 24, 0.7);
    border: 1px solid rgba(163, 210, 235, 0.5);
    color: #a3d2eb;
}

:deep(.el-select-dropdown) {
    background-color: rgba(12, 16, 24, 0.9);
    border: 1px solid rgba(163, 210, 235, 0.5);
}

:deep(.el-select-dropdown__item) {
    color: #a3d2eb;
}

:deep(.el-select-dropdown__item.selected) {
    background-color: rgba(163, 210, 235, 0.2);
    color: #ffffff;
}

:deep(.el-select-dropdown__item:hover) {
    background-color: rgba(163, 210, 235, 0.1);
}
</style>