/**
 * 绘制3d图
 * @param pieData 总数据
 * @param internalDiameterRatio:透明的空心占比
 * @param distance 视角到主体的距离
 * @param alpha 旋转角度
 * @param pieHeight 立体的高度
 * @param opacity 饼或者环的透明度
 */
import { formatNumber } from "@/utils/method";

const colorList = ['#0080ff', '#00aaff', '#ffaa00', '#00ffff'];

const getPie3D = (
  pieData,
  internalDiameterRatio,
  distance,
  alpha,
  pieHeight,
  opacity = 1,
  showIndex = 0,
  unit = ""
) => {
  const series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  let legendData = [];
  let legendBfb = [];
  const k = 1 - internalDiameterRatio;

  // 给每个 pieData 添加颜色（如果未指定）
  pieData = pieData.map((item, index) => ({
    ...item,
    itemStyle: {
      ...(item.itemStyle || {}),
      color: colorList[index % colorList.length],
    },
  }));
  pieData.sort((a, b) => b.value - a.value);

  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;
    const seriesItem = {
      name: typeof pieData[i].name === "undefined" ? `series${i}` : pieData[i].name,
      type: "surface",
      parametric: true,
      wireframe: { show: false },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
      itemStyle: {
        color: pieData[i].itemStyle.color,
        opacity:
          typeof pieData[i].itemStyle?.opacity !== "undefined"
            ? pieData[i].itemStyle.opacity
            : opacity,
      },
    };
    series.push(seriesItem);
  }

  legendData = [];
  legendBfb = [];
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.value
    );
    startValue = endValue;
    const bfb = fomatFloat(series[i].pieData.value / sumValue, 4);
    legendData.push({
      name: series[i].name,
      value: bfb,
    });
    legendBfb.push({
      name: series[i].name,
      value: bfb,
    });
  }

  const boxHeight = pieHeight / 25;

  const option = {
    legend: {
      show: false,
      data: legendData,
      orient: "vertical",
      right: "35%",
      top: "center",
      itemGap: 10,
      icon: "circle",
      formatter: function (param) {
        const item = legendBfb.filter((item) => item.name === param)[0];
        return ` ${item.name}`;
      },
      textStyle: {
        color: "#fff",
      },
    },
    labelLine: {
      show: true,
      lineStyle: {
        color: "#ffffff",
        width: 1,
      },
    },
    label: {
      show: true,
      position: "outside",
      formatter: "{b} \n{c} {d}%",
      fontSize: 14,
      color: "#ffffff",
    },
    xAxis3D: { min: -2, max: 2 },
    yAxis3D: { min: -2, max: 2 },
    zAxis3D: { min: -1.5, max: 1.5 },
    grid3D: {
      show: false,
      boxHeight: boxHeight,
      viewControl: {
        alpha,
        distance,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        panSensitivity: 0,
        autoRotate: false,
      },
      light: {
        main: {
          intensity: 1.2,
          shadow: true,
        },
        ambient: {
          intensity: 0.3,
        },
      },
    },
    series: [
      ...(showIndex === 0
        ? [
          {
            type: "pie",
            radius: ["0%", "40%"],
            center: ["50%", "50%"],
            data: pieData.map((item) => ({
              name: item.name,
              value: item.value,
            })),
            label: {
              show: true,
              position: "outside",
              alignTo: "labelLine",
              margin: 12,
              formatter: function (params) {
                if (params.name !== "") {
                  return (
                    `{a|${params.name}}\n` +
                    `{f|${unit}} \n` +
                    `{d|${formatNumber(params.value)}}`
                  );
                } else {
                  return "";
                }
              },
              rich: {
                a: {
                  fontSize: 13,
                  color: "#fff",
                  fontWeight: 400,
                  padding: [0, 0, 4, 0],
                },
                d: {
                  fontSize: 16,
                  fontWeight: 600,
                  color: "#00d5ff",
                  padding: [2, 0, 0, 0],
                },
                f: {
                  fontSize: 12,
                  color: "#cccccc",
                  padding: [0, 0, 0, 0],
                },
              },
            },
            labelLine: {
              show: true,
              length: 20,
              length2: 30,
              lineStyle: {
                color: "#aaa",
                width: 1,
              },
            },
            itemStyle: {
              color: "transparent",
            },
            zlevel: 2,
            startAngle: 350 - alpha,
          },
        ]
        : []),
      ...series.map((item, index) => {
        return { ...item };
      }),
    ],
  };

  return option;
};

const getParametricEquation = (
  startRatio,
  endRatio,
  isSelected,
  isHovered,
  k,
  h
) => {
  startRatio = startRatio !== undefined ? startRatio : 0;
  endRatio = endRatio !== undefined ? endRatio : 1;

  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;

  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }

  k = typeof k !== "undefined" ? k : 1 / 3;

  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  const hoverRate = isHovered ? 1.05 : 1;

  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },
    x: function (u, v) {
      if (u < startRadian) {
        return (
          offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u, v) {
      if (u < startRadian) {
        return (
          offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u, v) {
      const height = 5.5;
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * height;
      }
      return Math.sin(v) > 0 ? height : -1;
    },
  };
};

const getHeight3D = (series, height) => {
  series.sort((a, b) => b.pieData.value - a.pieData.value);
  return (height * 25) / series[0].pieData.value;
};

const fomatFloat = (num, n) => {
  let f = parseFloat(num);
  if (isNaN(f)) {
    return false;
  }
  f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n);
  let s = f.toString();
  let rs = s.indexOf(".");
  if (rs < 0) {
    rs = s.length;
    s += ".";
  }
  while (s.length <= rs + n) {
    s += "0";
  }
  return s;
};

export { getPie3D, getParametricEquation };
