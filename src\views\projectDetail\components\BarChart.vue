<template>
  <div class="BarChart" ref="barChartRef"></div>
</template>

<script setup>
import { nextTick, ref, watch, markRaw, onMounted } from "vue";
import {
  getImg,
  numberToThousands,
  getPercent,
  formatNumber,
} from "@/utils/method";
import * as echarts from "echarts";
const props = defineProps({
  color: {
    type: String,
    default: "0, 255, 136",
  },
  data: {
    type: Array,
    default: () => [],
  },
  topColor: {
    type: String,
    default: "104,226,158",
  },
  chartData: {
    type: Object,
    default: () => ({ name: "", data: [], percentages: [] }),
  }
});
const barChartRef = ref(null);
const barChart = ref(null);
let xData = ["1月", "2月", "3月", "4月", "5月"];
let yData = [80, 87, 51, 81, 90];
let percentages = ["0", "0", "0", "0", "0"];

onMounted(() => {
  initChart();
});

watch(
  () => props.chartData,
  (newData) => {
    if (newData) {
      if (newData.data && newData.data.length > 0) {
        yData = newData.data;
      }
      if (newData.percentages && newData.percentages.length > 0) {
        percentages = newData.percentages;
      }
      initChart();
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => props.data,
  (newData) => {
    if (newData && newData.length > 0) {
      yData = newData;
      initChart();
    }
  },
  { deep: true }
);

let bgValues = new Array(5).fill(100);
function initChart() {
  if (!barChartRef.value) return;

  if (!barChart.value)
    barChart.value = markRaw(echarts.init(barChartRef.value));
  else barChart.value.clear();

  // 确保数据有效
  let displayData = Array.isArray(yData) && yData.length > 0
    ? yData
    : props.chartData && Array.isArray(props.chartData.data) && props.chartData.data.length > 0
      ? props.chartData.data
      : [0, 0, 0, 0, 0];

  // 确保百分比数据有效
  let displayPercentages = Array.isArray(percentages) && percentages.length > 0
    ? percentages
    : props.chartData && Array.isArray(props.chartData.percentages) && props.chartData.percentages.length > 0
      ? props.chartData.percentages
      : ["0.00", "0.00", "0.00", "0.00", "0.00"];

  // 确保背景值数组与数据长度一致
  bgValues = new Array(displayData.length).fill(100);

  let option = {
    grid: {
      top: "80%", // 如果不需要整体缩放 grid，可保持不变
      bottom: "90%",
    },
    xAxis: {
      data: xData,
      axisTick: { show: false },
      axisLine: { show: true },
      // 如有需要可开启标签配置
      axisLabel: {
        interval: 0,
        textStyle: { color: "#beceff", fontSize: 12 },
      },
    },
    yAxis: [
      {
        type: "value",
        gridIndex: 0,
        splitLine: {
          show: true,
          lineStyle: {
            color: "#4D5359",
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: "#0c3b71",
          },
        },
        axisLabel: {
          color: "rgb(170,170,170)",
          formatter: "{value}",
        },
      },
    ],
    series: [
      // 内部柱顶部（pictorialBar）
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [12, 8],
        symbolOffset: [14.8, -5],
        z: 12,
        barGap: "0%",
        data: displayData.map(function (val) {
          return {
            value: val,
            symbolPosition: "end",
            itemStyle: {
              normal: {
                color: "rgba(28, 197, 227, 1)",
              },
            },
          };
        }),
      },
      // 内部柱底部（pictorialBar）
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [12, 10],
        symbolOffset: [-1.5, 6],
        z: 12,
        barGap: "0%",
        data: displayData.map(function (val) {
          return {
            value: val,
            itemStyle: {
              normal: {
                color: "rgba(28, 197, 227, 1)",
              },
            },
          };
        }),
      },
      // 内部柱主体（bar）
      {
        type: "bar",
        barWidth: 12,
        z: 10,
        barGap: "0%",
        data: displayData.map(function (val, index) {
          return {
            value: val,
            label: {
              normal: {
                show: true,
                formatter: function () {
                  // 只显示百分比
                  const percentage = displayPercentages[index];
                  return `{b|${percentage}%}`;
                },
                position: "top",
                rich: {
                  a: {
                    color: "#fff",
                    fontSize: 12,
                    fontWeight: "bold",
                    lineHeight: 20
                  },
                  b: {
                    color: "#8EE3F1",
                    fontSize: 12,
                    lineHeight: 20
                  }
                }
              },
            },
            itemStyle: {
              normal: {
                color: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  type: "linear",
                  global: false,
                  colorStops: [
                    { offset: 0, color: "#1CC5E3" },
                    { offset: 1, color: "#1CC5E3" },
                  ],
                },
              },
            },
          };
        }),
      },
      // 内部外圆圈（pictorialBar）
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [18, 12],
        symbolOffset: [-18, 8],
        z: 11,
        barGap: "0%",
        data: bgValues.map(function (val) {
          return {
            value: val,
            itemStyle: {
              normal: {
                color: "rgba(28, 197, 227, 0.6)",
              },
            },
          };
        }),
      },
      // 最底层渐变条（bar）
      {
        type: "bar",
        barWidth: 18,
        barGap: "-125%",
        z: -1,
        data: bgValues.map(function (val) {
          return {
            value: 100,
            label: { normal: { show: false } },
            itemStyle: {
              normal: {
                color: {
                  x: 1,
                  y: 1,
                  x2: 1,
                  y2: 0,
                  type: "linear",
                  global: false,
                  colorStops: [
                    { offset: 0, color: "rgba(28, 197, 227, 0.2)" },
                    { offset: 0.5, color: "rgba(28, 197, 227, 0.15)" },
                    { offset: 0.9, color: "rgba(28, 197, 227, 0.1)" },
                    { offset: 1, color: "rgba(28, 197, 227, 0)" },
                  ],
                },
              },
            },
          };
        }),
      },
    ],
  };
  barChart.value.setOption(option);
}
</script>

<style lang="scss" scoped>
.BarChart {
  width: 100%;
  height: 100%;
}
</style>
