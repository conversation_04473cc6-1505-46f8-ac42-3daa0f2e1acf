import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

export function useLanguage() {
    const { t, locale } = useI18n()

    const currentLanguage = ref(localStorage.getItem('language') || 'zh')

    // 切换语言
    const changeLanguage = (lang) => {
        currentLanguage.value = lang
        locale.value = lang
        localStorage.setItem('language', lang)
    }

    // 获取当前语言标识
    const isZh = computed(() => currentLanguage.value === 'zh')
    const isEn = computed(() => currentLanguage.value === 'en')

    // 格式化文本（根据语言切换显示）
    const formatText = (zhText, enText) => {
        return isZh.value ? zhText : enText
    }

    return {
        t,
        currentLanguage,
        changeLanguage,
        isZh,
        isEn,
        formatText
    }
} 