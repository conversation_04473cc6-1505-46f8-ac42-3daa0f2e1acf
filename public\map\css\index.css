body {
  position: relative;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
}
#map {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0);
  pointer-events: all;
  overflow: hidden;
  filter: hue-rotate(64deg) saturate(1.4);
}
.country_point {
  display: flex;
  pointer-events: all;
  /* margin-top: -35px;
  margin-left: 20px; */
  margin-top: -24px;
  margin-left: -70px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.country_point_title {
  height: 20px;
  /* background-image: url("../img/title-bg.png");
  background-size: 100% 100%; */
  background-repeat: no-repeat;
  line-height: 20px;
  font-size: 16px;
  color: #a7ffe2;
  font-weight: 900;
  margin-left: -10px;
  padding-left: 10px;
  min-width: 120px;
  pointer-events: all;
  cursor: pointer;
  text-align: center;
  text-shadow: 1px 1px 5px rgba(0, 0, 0, 0.6),
    /* 主投影层 */ 3px 3px 5px rgba(0, 0, 0, 0.3); /* 延伸投影层 */
}
.country_point_title > span {
  font-weight: 500;
}
.country_point_title.type2 {
  color: #fff;
}
.country_point_title .num0 {
  display: none;
}
.country_point_imgs {
  width: 100%;
  display: flex;
  justify-content: center;
}
.country_point_img {
  width: 30px;
  height: 44px;
  background: url("../img/0.png") no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}
.country_point_img1 {
  background: url("../img/1.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img2 {
  background: url("../img/2.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img3 {
  background: url("../img/3.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img4 {
  background: url("../img/4.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img5 {
  background: url("../img/5.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img6 {
  background: url("../img/6.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img7 {
  background: url("../img/7.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img8 {
  background: url("../img/8.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img9 {
  background: url("../img/9.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img10 {
  width: 49px;
  height: 49px;
  background: url("../img/10.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img11 {
  width: 49px;
  height: 49px;
  background: url("../img/11.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img12 {
  width: 49px;
  height: 49px;
  background: url("../img/12.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img13 {
  width: 49px;
  height: 49px;
  background: url("../img/13.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img14 {
  width: 49px;
  height: 49px;
  background: url("../img/14.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img15 {
  width: 49px;
  height: 49px;
  background: url("../img/15.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img33 {
  width: 49px;
  height: 49px;
  background: url("../img/33.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_img76 {
  width: 49px;
  height: 49px;
  background: url("../img/76.png") no-repeat;
  background-size: 100% 100%;
}
.country_point_title:hover {
  font-size: 17px;
  font-weight: 900;
}
.country_point_num {
  font-size: 20px;
  color: #9ffcd5;
  font-weight: 900;
  margin-left: 5px;
}

.project-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  transform-style: preserve-3d;
  margin-top: -120px;
  margin-left: -30px;
  pointer-events: all;
  cursor: pointer;
}

/* 添加光晕效果 */
.project-halo {
  position: absolute;
  bottom: -125px; /* 位于名称上方 */
  width: 30px;
  height: 30px;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.2) 30%,
    rgba(255, 255, 255, 0.1) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  border-radius: 50%;
  z-index: -1; /* 确保在名称后面 */
}

.project-percent {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
  text-shadow: 1px 3px 4px rgba(0, 0, 0, 0.8);
}

.project-column {
  position: relative;
  width: 12px;
  transform-style: preserve-3d;
  transform: rotateX(-20deg);
}

/* 内部柱体样式 */
.project-column-inner {
  position: absolute;
  bottom: -106px;
  /* position: relative; */
  width: 100%;
  transform-style: preserve-3d;
}

.project-column-inner-front {
  position: absolute;
  width: 100%;
  bottom: 0;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.3),
    rgba(0, 0, 0, 0.1)
  );
}

.project-column-inner-top {
  position: absolute;
  width: 100%;
  height: 8px;
  top: 2px;
  border-radius: 50%;
  transform: translateY(-6px);
  background: rgba(255, 255, 255, 0.3);
  z-index: 2;
}

/* 外部包裹柱体样式 */
.project-column-outer {
  position: absolute;
  width: 16px;
  height: 100px;
  transform-style: preserve-3d;
  margin-left: -2px;
}

.project-column-outer-front {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.3) 100%
  );
}

.project-column-outer-top {
  position: absolute;
  width: 100%;
  height: 8px;
  top: 2px;
  border-radius: 50%;
  transform: translateY(-6px);
  z-index: 1;
}

.project-column-outer-bottom {
  position: absolute;
  width: 100%;
  height: 10px;
  bottom: -3.5px;
  border-radius: 0 0 50% 50%;
  transform: translateY(6px);
  z-index: 1;
}

.project-name {
  position: absolute;
  bottom: -125px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  text-shadow: 0px 6px 4px rgba(0, 0, 0, 0.8);
  font-weight: 500;
}

.project2-container {
  cursor: pointer;
  position: relative;
  width: 284px;
  height: 135px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  color: #fff;
}

.project2-name {
  position: absolute;
  top: 12px;
  left: 90px;
}

.guojia {
  display: none;
}

.china {
  display: none;
}