var map = null;
var countryMarkers = {};
var projectMarkers = {};
var chinaCountryMarker = {};
var jiangsuCountryMarker = {};
var isChina = false;
var PointMap = bad;
var country = gjxm2;
var hideCountry = ['刚果（金）', '安哥拉', '纳米比亚', '莫桑比克', '肯尼亚', '南苏丹', '肯尼亚', '加纳', '几内亚', '贝宁', '中非', '以色列', '乌兹比克斯坦', '土耳其', '科特迪瓦', '津巴布韦', '博茨瓦纳', '坦桑尼亚', '科威特', '印度尼西亚', '乌干达', '孟加拉', '沙特']
var canHide = false

var isPingPu = false;

let nanjing = {
  '六合区': [-0.10892328794625428, 51.520422024829344],
  '浦口区': [-0.11690498296513852, 51.51457359222999],
  '鼓楼区': [-0.11102062850557104, 51.51474551748581],
  '栖霞区': [-0.10741867406659367, 51.515710289892894],
  '玄武区': [-0.10977348865890235, 51.514621998435615],
  '秦淮区': [-0.11040108161398621, 51.51381077040443],
  '建邺区': [-0.11299996013326563, 51.51379073989992],
  '雨花台区': [-0.11363828117305275, 51.51272577206012],
  '江宁区': [-0.11046008607991098, 51.511049816138296],
  '溧水区': [-0.10394009260176063, 51.507666000096236],
  '高淳区': [-0.10522746276603812, 51.503418763330586],
}

$(function () {

  let maps = {};

  country.forEach((item) => {
    if (item["国家"]) {
      item["区域"] = item["国家"].split("：")[0];
      item["国家"] = item["国家"].split("：")[1];
    }
  });

  gjxm2.forEach((item) => {
    maps[item.项目名称] = item.项目名称简称;
  });

  for (const key in PointMap[0]) {
    for (const key2 in PointMap[0][key].project) {
      if (maps[key2]) {
        let data = PointMap[0][key].project[key2];
        delete PointMap[0][key].project[key2];
        PointMap[0][key].project[maps[key2]] = data;
      }
    }
  }

  // console.log(PointMap[0]);
  initMap();

  window.addEventListener("message", function (event) {
    if (event.data.type === "clickCountry") {
      // console.log(1);
      clickCountry(event.data.data.name, event.data.data.num);
    }
    if (event.data.type === "initMap") {
      initMapLayer();
    }
    if (event.data.type === "showMap") {
      localStorage.removeItem('isChina')
      map.chinaLayer.hide();
      hideChinaProvice();
      canHide = true;
      if (event.data.data) {
        isPingPu = true;
        map.mapLayer.show();
        for (const key in countryMarkers) {
          countryMarkers[key].show();
        }
        document.querySelectorAll('.guojia').forEach(element => {
          if (!hideCountry.includes(element.id)) {
            element.style.display = 'block';
          }
        });

      } else {
        isPingPu = false;
        map.mapLayer.hide();
        for (const key in countryMarkers) {
          countryMarkers[key].hide();
        }
        document.querySelectorAll('.guojia').forEach(element => {
          element.style.display = 'none';
        });
      }
    }
  });
});

function initMap() {
  map = new ztmapgl.Map("map", {
    center: [-0.11339229871043699, 51.512704071829575],
    minZoom: 15,
    zoom: 15,
    maxZoom: 17.5,
    // draggable: false,        //disable drag
    // dragPan: false,          //disable drag panning
    dragRotate: false, //disable drag rotation
    dragPitch: false, //disable drag pitch
    // scrollWheelZoom: false,  //disable wheel zoom
    touchZoom: false, //disable touchzoom
    doubleClickZoom: false, //disable doubleclick zoom
    // baseLayer: new ztmapgl.TileLayer('base', {
    //   urlTemplate: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png',
    //   subdomains: ['a','b','c','d']
    // })
  });

  window.parent.postMessage({ type: "mapok" }, "*");

  $(".ztmapgl-attribution").hide();

  map.setMaxExtent(
    JSON.parse(
      // '{"xmin":-0.11431261699033257,"ymin":51.50513172956684,"xmax":-0.08146628539395806,"ymax":51.51594897610673}'
      '{"xmin":-0.14354588104640698,"ymin":51.503348640533744,"xmax":-0.07785321785365795,"ymax":51.52498141188809}'
    )
  );

  map.on("click", function (e) {
    console.log('[' + e.coordinate.x + "," + e.coordinate.y + ']');
  });

  map.on("zooming", function (e) {
    if (e.to < 15.8) {
      for (let index = 0; index < hideCountry.length; index++) {
        if (document.getElementById(hideCountry[index])) {
          document.getElementById(hideCountry[index]).style.display = 'none';
        }
      }
    } else {
      if (canHide) {
        for (let index = 0; index < hideCountry.length; index++) {
          if (document.getElementById(hideCountry[index])) {
            document.getElementById(hideCountry[index]).style.display = 'block';
          }
        }
      }
    }
  })

  map.nanjingName = new ztmapgl.VectorLayer("nanjingName").addTo(map);
  map.nanjingName.hide()
  map.nanjingName.setZIndex(100);
  for (const key in nanjing) {
    let label = new ztmapgl.Marker(
      nanjing[key],
      {
        'properties': {
          'name': key
        },
        'symbol': {
          'textFaceName': 'sans-serif',
          'textName': '{name}',          //value from name in geometry's properties
          'textWeight': 'normal', //'bold', 'bolder'
          'textStyle': 'normal', //'italic', 'oblique'
          'textSize': 14,
          'textFont': null,     //same as CanvasRenderingContext2D.font, override textName, textWeight and textStyle
          'textFill': '#fff',
        }
      }
    ).addTo(map.nanjingName)
  }

  map.mapLayer = new ztmapgl.ImageLayer("mapLayer", [
    {
      url: "img/map5.png",
      extent: [
        -0.1478964578795967, 51.49993586734465, -0.07253711339717483,
        51.52474777943152,
      ],
      opacity: 1,
    },
  ]).addTo(map);
  map.mapLayer.hide();

  map.chinaLayer = new ztmapgl.ImageLayer("chinaLayer", [
    {
      url: "img/china.png",
      extent: [
        -0.1478964578795967, 51.49993586734465, -0.07253711339717483,
        51.52474777943152,
      ],
      opacity: 1,
    },
  ]).addTo(map);
  map.chinaLayer.hide();

  map.countrylayer = new ztmapgl.VectorLayer("country").addTo(map);


  let markers = [
    {
      name: "赞比亚",
      center: [-0.10710610535693377, 51.50689137636763],
      projectNum: 0,
    },
    {
      name: "津巴布韦",
      center: [-0.10622083268060578, 51.50650572904087],
      projectNum: 0,
    },
    {
      name: "毛里求斯",
      center: [-0.10064361482022832, 51.506460653166215],
      projectNum: 0,
    },
    {
      name: "刚果（布）",
      center: [-0.10921937173213792, 51.50788970363598],
      projectNum: 0,
    },
    {
      name: "科特迪瓦",
      center: [-0.11343436982326693, 51.50872128105485],
      projectNum: 0,
    },
    {
      name: "安哥拉",
      center: [-0.10883899984014533, 51.506813122803436],
      projectNum: 0,
    },
    {
      name: "南苏丹",
      center: [-0.10619927767822901, 51.508691231937945],
      projectNum: 0,
    },
    {
      name: "埃塞俄比亚",
      center: [-0.10434825299148542, 51.508826452807995],
      projectNum: 0,
    },
    {
      name: "加纳",
      center: [-0.11251690541325843, 51.50876635469305],
      projectNum: 0,
    },
    {
      name: "乌干达",
      center: [-0.10592057273152022, 51.50817183563865],
      projectNum: 0,
    },
    {
      name: "阿联酋",
      center: [-0.10124635006184235, 51.51039711144321],
      projectNum: 0,
    },
    {
      name: "伊拉克",
      center: [-0.1035587749260003, 51.51143709429894],
      projectNum: 0,
    },
    {
      name: "沙特",
      center: [-0.10346757968591191, 51.51045386874446],
      projectNum: 0,
    },
    {
      name: "科威特",
      center: [-0.10270569398335283, 51.511001406136614],
      projectNum: 0,
    },
    {
      name: "新加坡",
      center: [-0.09139566488317996, 51.5081200837453],
      projectNum: 0,
    },
    {
      name: "蒙古",
      center: [-0.0916844124238878, 51.51317338163258],
      projectNum: 0,
    },
    {
      name: "亚美尼亚",
      center: [-0.10324409189706785, 51.5121198003616],
      projectNum: 0,
    },
    {
      name: "尼泊尔",
      center: [-0.09547060183842859, 51.51086452240557],
      projectNum: 0,
    },
    {
      name: "巴新",
      center: [-0.08340790188537994, 51.50739221217961],
      projectNum: 0,
    },
    {
      name: "瓦努阿图",
      center: [-0.0786585066208545, 51.506460310271535],
      projectNum: 0,
    },
    {
      name: "帕劳",
      center: [-0.08513709302428651, 51.508618893774354],
      projectNum: 0,
    },
    {
      name: "汤加",
      center: [-0.07500506515566485, 51.5058134875903],
      projectNum: 0,
    },
    {
      name: "博茨瓦纳",
      center: [-0.10761829213583951, 51.50604495133169],
      projectNum: 0,
    },
    {
      name: "纳米比亚",
      center: [-0.1086642803943505, 51.50592641718194],
      projectNum: 0,
    },
    {
      name: "中国",
      center: [-0.09105949339686958, 51.51146213372195],
      projectNum: 0,
    },

    //
    {
      name: "莫桑比克",
      center: [-0.10524976415263154, 51.50653077117417],
      projectNum: 0,
    },
    {
      name: "南非",
      center: [-0.10756756897762898, 51.50543892138066],
      projectNum: 0,
    },
    {
      name: "刚果（金）",
      center: [-0.10740661030922638, 51.507983191357056],
      projectNum: 0,
    },
    {
      name: "几内亚",
      center: [-0.11435641893183401, 51.509103359666625],
      projectNum: 0,
    },
    {
      name: "马里",
      center: [-0.11285145538215602, 51.50986959902579],
      projectNum: 0,
    },
    {
      name: "贝宁",
      center: [-0.1118535116380599, 51.50905828636181],
      projectNum: 0,
    },
    {
      name: "尼日尔",
      center: [-0.10871213495954635, 51.510692582502],
      projectNum: 0,
    },
    {
      name: "中非",
      center: [-0.10819706722065803, 51.508836257950264],
      projectNum: 0,
    },
    {
      name: "苏丹",
      center: [-0.10631921608921857, 51.50963088095611],
      projectNum: 0,
    },
    {
      name: "以色列",
      center: [-0.1052246971439672, 51.511079863699564],
      projectNum: 0,
    },
    {
      name: "坦桑尼亚",
      center: [-0.10528908061132825, 51.50764763632398],
      projectNum: 0,
    },
    {
      name: "肯尼亚",
      center: [-0.10460232362618171, 51.50820856275308],
      projectNum: 0,
    },
    {
      name: "哈萨克斯坦",
      center: [-0.09833566613610856, 51.51308298975303],
      projectNum: 0,
    },
    {
      name: "乌兹比克斯坦",
      center: [-0.10036374535798132, 51.511987958424555],
      projectNum: 0,
    },
    {
      name: "塔吉克斯坦",
      center: [-0.09720895545717667, 51.51225504166345],
      projectNum: 0,
    },
    {
      name: "孟加拉",
      center: [-0.09405416555637203, 51.510398780776626],
      projectNum: 0,
    },
    {
      name: "印度尼西亚",
      center: [-0.0876694717094324, 51.50800155499397],
      projectNum: 0,
    },
    {
      name: "日本",
      center: [-0.08407472811495609, 51.51164742502394],
      projectNum: 0,
    },
    {
      name: "美国",
      center: [-0.13135135134348275, 51.511975768077576],
      projectNum: 0,
    },
    {
      name: "秘鲁",
      center: [-0.1282735059018023, 51.50806922454271],
      projectNum: 0,
    },
    {
      name: "牙买加",
      center: [-0.12801597203235815, 51.50989887072748],
      projectNum: 0,
    },
    {
      name: "特多",
      center: [-0.12538750973885726, 51.50892018864627],
      projectNum: 0,
    },
    {
      name: "土耳其",
      center: [-0.10542863485602538, 51.51197840398308],
      projectNum: 0,
    },
  ];
  markers = markers.map((item) => {
    const campProjects = gjxm2.filter(
      (ele) => ele.类型 === "营地" && ele.国家.includes(item.name)
    );

    return {
      ...item,
      type: campProjects.length ? 1 : 2,
    };
  });

  const countryProjects = gjxm2
    .filter((item) => item.类型 == "项目")
    .reduce((acc, project) => {
      const country = project.国家;
      if (country) {
        // 只统计有国家数据的项目
        acc[country] = (acc[country] || 0) + 1;
      }
      return acc;
    }, {});

  countryProjects["中国"] = china.length;

  for (let i = 0; i < markers.length; i++) {
    if (countryProjects[markers[i].name]) {
      markers[i].projectNum = countryProjects[markers[i].name];
    }
  }

  for (let i = 0; i < markers.length; i++) {
    let item = markers[i];
    let html = `<div class="country_point guojia" id="${item.name}">
                <div class="country_point_imgs">
                </div>
            <div class="country_point_title type${item.type}" onclick="clickCountry('${item.name}','${item.projectNum}')">${item.name}<span class="num${item.projectNum}">(${item.projectNum})</span></div>
        </div>`;
    // <div class="country_point_img country_point_img${item.projectNum}"> </div>
    var countryMarker = new ztmapgl.ui.UIMarker(item.center, {
      single: false,
      content: html,
    });
    countryMarker.addTo(map)
    countryMarkers[item.name] = countryMarker;
  }

  for (const key in countryMarkers) {
    countryMarkers[key].hide();
  }

  initChinaProvice();
  initCountryProjectData();
  initJiangSuProvice();

  if (localStorage.getItem("clickCountry")) {
    clickCountry(localStorage.getItem("clickCountry"));
  } else {
    initMapLayer();
  }
}

function clickCountry(name, num) {
  if (num == 0) return;
  canHide = false

  if (name == '南京') {
    map.nanjingName.show()
  } else {
    map.nanjingName.hide()
  }

  console.log(isPingPu)
  if (isPingPu) {
    document.querySelectorAll('.guojia').forEach(element => {
      element.style.display = 'none';
    });
    map.setZoom(14.5);
    map.mapLayer.hide();
  }

  if (name === "中国") {
    showChina();
    window.parent.postMessage({ type: "isChina" }, "*");
    return;
  }

  // 给上级发消息
  window.parent.postMessage({ type: "clickCountry", data: name }, "*");

  for (const key in countryMarkers) {
    countryMarkers[key].hide();
  }

  map.chinaLayer.hide();
  hideChinaProvice();

  let jiangsu = ["南京", "无锡", "徐州", "常州", "苏州", "宿迁"];
  if (jiangsu.includes(name)) {
    hideJiangSu();
    // return;
  }

  setTimeout(() => {
    map[`${name}Layer`].show();
    map[`${name}ProjectLayer`].show();

    map.setCenter(map[`${name}Layer`].attr.center);
    map.setZoom(map[`${name}Layer`].attr.zoom);
    if (name === "江苏") {
      console.log(123)
      showJiangSu();
    }
  }, 200);
}

// function addProjectMarker() {
//   let projectInfo = [
//     {
//       name: "华为刚果布代表处项目",
//       type: "xm",
//       center: [-0.10481596502381763, 51.51087453830232],
//     },
//     {
//       name: "营地",
//       type: "yd",
//       center: [-0.10372170038408512, 51.514920780428014],
//     },
//   ];

//   projectInfo.forEach((item) => {
//     const height = item.percent * 0.8; // 将百分比转换为像素高度

//     // let html = `
//     //         <div class="project-marker" onclick="clickProject('${item.name}')">
//     //             <div class="project-percent" style="color: ${item.color}">${item.percent}%</div>
//     //             <div class="project-column">
//     //                 <div class="project-column-outer">
//     //                     <div class="project-column-outer-front" style="background: linear-gradient(180deg,
//     //                         ${item.color}10 0%,
//     //                         ${item.color}30 50%,
//     //                         ${item.color}50 100%
//     //                     )"></div>
//     //                     <div class="project-column-outer-top" style="background-color: ${item.color}40"></div>
//     //                     <div class="project-column-outer-bottom" style="background-color: ${item.color}50"></div>
//     //                 </div>
//     //                 <div class="project-column-inner" style="height: ${height}px;">
//     //                     <div class="project-column-inner-front" style="height: 100%; background-color: ${item.color}"></div>
//     //                     <div class="project-column-inner-top" style="background-color: ${item.color}"></div>
//     //                 </div>
//     //             </div>
//     //             <div class="project-halo" style="background: radial-gradient(circle at center,
//     //                 ${item.color}28 0%,
//     //                 ${item.color}20 30%,
//     //                 ${item.color}00 60%,
//     //                 ${item.color}00 100%
//     //             )"></div>
//     //             <div class="project-name">${item.name}</div>
//     //         </div>
//     //     `;

//     //  背景

//     // 设置背景图片

//     let bgStyle =
//       item.type === "xm"
//         ? `background-image: url("./img/xm.png");`
//         : `background-image: url("./img/yd.png");`;

//     let html = `
//     <div class="project2-container" style='${bgStyle}' onclick="clickProject('${item.name}', '${item.type}')">
//       <div class="project2-name">${item.name}</div>
//     </div>
//     `;

//     var projectMarker = new ztmapgl.ui.UIMarker(item.center, {
//       single: false,
//       content: html,
//     });
//     projectMarker.addTo(map.projectlayer).hide();
//     projectMarkers[item.name] = projectMarker;
//   });
// }

function initMapLayer() {
  for (const key in projectMarkers) {
    projectMarkers[key].hide();
  }

  map.nanjingName.hide()

  if (localStorage.getItem("isChina")) {
    canHide = false
    hideAllCountryLayer();
    hideJiangSu();
    showChina();
  } else {

    hideAllCountryLayer();
    map.chinaLayer.hide();
    if (!isPingPu) {
      window.parent.postMessage({ type: "showEarth", data: true }, "*");
    }
    hideChinaProvice();
    setTimeout(() => {
      map.setZoom(15);

      map.setCenter([-0.11128186667511363, 51.51382579327705]);

      if (isPingPu) {
        map.mapLayer.show();
        for (const key in countryMarkers) {
          countryMarkers[key].show();
        }
        canHide = true
        document.querySelectorAll('.guojia').forEach(element => {
          if (!hideCountry.includes(element.id)) {
            element.style.display = 'block';
          }
        });
      }


    }, 200);
  }
}

function hideAllCountryLayer() {
  Object.keys(PointMap[0]).forEach((country) => {
    // 默认隐藏图层
    if (map[`${country}Layer`]) {
      map[`${country}Layer`].hide();
      map[`${country}ProjectLayer`].hide();
    }
  });
}

function clickProject(name, type) {
  let item = country.find((item) => item.项目名称简称 == name);

  // if (item) {
  //   window.parent.postMessage({ type: "clickCountry", data: item.国家 }, "*");
  // }

  // 给上级发消息
  window.parent.postMessage(
    { type: "clickProject", data: { name, type, attr: item } },
    "*"
  );
}

function initCountryProjectData() {
  let data = PointMap;
  Object.keys(data[0]).forEach((country) => {
    const countryData = data[0][country];

    // 创建国家底图图层
    map[`${country}Layer`] = new ztmapgl.ImageLayer(country, [
      {
        url: countryData.img,
        extent: [
          -0.1478964578795967, 51.49993586734465, -0.07253711339717483,
          51.52474777943152,
        ],
        opacity: 1,
      },
    ]).addTo(map);
    map[`${country}Layer`].attr = countryData;

    // 创建国家项目标记图层
    map[`${country}ProjectLayer`] = new ztmapgl.VectorLayer(
      `${country}Project`
    ).addTo(map);

    // 添加项目标记
    if (Object.keys(countryData.project).length > 0) {
      Object.keys(countryData.project).forEach((project) => {
        var label = new ztmapgl.Label(project, countryData.project[project], {
          textSymbol: {
            textFill: "#fff",
            textSize: 12,
            textDx: 80,
            textDy: -3,
          },
        });
        label.addTo(map[`${country}ProjectLayer`]);

        // 国家项目
        label.addEventListener("click", (e) => {
          clickProject(e.target._content);
          // console.log(e);
          // window.parent.postMessage({ type: "clickProject", data: { name: e.target._content} }, "*");
        });
      });
    }

    // 默认隐藏图层
    map[`${country}Layer`].hide();
    map[`${country}ProjectLayer`].hide();
  });
}

function showChina() {
  // for (const key in countryMarkers) {
  //   countryMarkers[key].hide();
  // }
  document.querySelectorAll('.guojia').forEach(element => {
    element.style.display = 'none';
  });

  setTimeout(() => {
    // map.mapLayer.hide();
    map.chinaLayer.show();
    map.setCenter([-0.10873630994933592, 51.5126773638394]);
    map.setZoom(15);

    showChinaProvice();
  }, 200);
}

function showChinaProvice() {
  // for (const key in chinaCountryMarker) {
  //   chinaCountryMarker[key].show();
  // }

  document.querySelectorAll('.china').forEach(element => {
    element.style.display = 'block';
  });

}

function hideChinaProvice() {
  document.querySelectorAll('.china').forEach(element => {
    element.style.display = 'none';
  });
}

function initChinaProvice() {
  let markers = [
    {
      name: "新疆",
      center: [-0.13390823209670089, 51.514827306942635],
      projectNum: 0,
    },
    {
      name: "四川",
      center: [-0.11285972990958726, 51.50753578409714],
      projectNum: 0,
    },
    {
      name: "陕西",
      center: [-0.10526424593990669, 51.509899647365586],
      projectNum: 0,
    },
    {
      name: "山西",
      center: [-0.10095155588930993, 51.51162238570279],
      projectNum: 0,
    },
    {
      name: "河北",
      center: [-0.09734691942912832, 51.512583885750445],
      projectNum: 0,
    },
    {
      name: "山东",
      center: [-0.0944932488981749, 51.511261817953],
      projectNum: 0,
    },
    {
      name: "广东",
      center: [-0.10011476528245566, 51.50367587932894],
      projectNum: 0,
    },
    {
      name: "广西",
      center: [-0.10549949540700254, 51.50375601908391],
      projectNum: 0,
    },
    {
      name: "湖北",
      center: [-0.10096075045510133, 51.50808335655958],
      projectNum: 0,
    },
    {
      name: "天津",
      center: [-0.09499593536020257, 51.51339847407772],
      projectNum: 0,
    },
    {
      name: "安徽",
      center: [-0.09482428600495041, 51.50840388372794],
      projectNum: 0,
    },
    {
      name: "重庆",
      center: [-0.10598149409599955, 51.507629272544335],
      projectNum: 0,
    },
    {
      name: "浙江",
      center: [-0.09152003591657376, 51.50669437943961],
      projectNum: 0,
    },
    {
      name: "上海",
      center: [-0.0900932006510402, 51.50841723897767],
      projectNum: 0,
    },
    {
      name: "江苏",
      center: [-0.09193919634253689, 51.50901822116461],
      projectNum: 0,
    },
  ];

  const countryProjects = china.reduce((acc, project) => {
    const country = project.省份;
    if (country) {
      // 只统计有国家数据的项目
      acc[country] = (acc[country] || 0) + 1;
    }
    return acc;
  }, {});

  for (let i = 0; i < markers.length; i++) {
    if (countryProjects[markers[i].name]) {
      markers[i].projectNum = countryProjects[markers[i].name];
    }
  }

  for (let i = 0; i < markers.length; i++) {
    let item = markers[i];
    let html = `<div class="country_point china">
                <div class="country_point_imgs">
                </div>
             <div class="country_point_title type${item.type}" onclick="clickCountry('${item.name}','${item.projectNum}')">${item.name}<span class="num${item.projectNum}">(${item.projectNum})</span></div>
        </div>`;

    var marker = new ztmapgl.ui.UIMarker(item.center, {
      single: false,
      content: html,
    });
    marker.addTo(map)
    chinaCountryMarker[item.name] = marker;
  }
}

function initJiangSuProvice() {
  let markers = [
    {
      name: "徐州",
      center: [-0.1215336763139542, 51.51811879341054],
      projectNum: 0,
    },
    {
      name: "南京",
      center: [-0.11211145292750635, 51.50759588383525],
      projectNum: 0,
    },
    {
      name: "无锡",
      center: [-0.09817529545847119, 51.505539092176576],
      projectNum: 0,
    },
    {
      name: "苏州",
      center: [-0.09700055512980252, 51.5030989843364],
      projectNum: 0,
    },
    {
      name: "常州",
      center: [-0.10300828256340064, 51.50559319702255],
      projectNum: 0,
    },
    {
      name: "宿迁",
      center: [-0.11500605566074453, 51.515795415888846],
      projectNum: 0,
    },
  ];

  // 筛选江苏的项目
  let jiangsuProjects = china.filter((item) => item.省份 === "江苏");
  // 根据项目地址，将包含name的projectNum+1
  jiangsuProjects.forEach((item) => {
    // 判断item.项目地址是否包含markers的name 包含则将markers的projectNum+1
    markers.forEach((marker) => {
      if (item.项目地址.includes(marker.name)) {
        marker.projectNum++;
      }
    });
  });

  for (let i = 0; i < markers.length; i++) {
    if (jiangsuProjects[markers[i].name]) {
      markers[i].projectNum = jiangsuProjects[markers[i].name];
    }
  }

  for (let i = 0; i < markers.length; i++) {
    let item = markers[i];
    let html = `<div class="country_point">
                <div class="country_point_imgs">
                </div>
            <div class="country_point_title type${item.type}" onclick="clickCountry('${item.name}','${item.projectNum}')">${item.name}<span class="num${item.projectNum}">(${item.projectNum})</span></div>
        </div>`;

    var marker = new ztmapgl.ui.UIMarker(item.center, {
      single: false,
      content: html,
    });
    marker.addTo(map).hide();
    jiangsuCountryMarker[item.name] = marker;
  }
}

function showJiangSu() {
  for (const key in jiangsuCountryMarker) {
    jiangsuCountryMarker[key].show();
  }
}

function hideJiangSu() {
  for (const key in jiangsuCountryMarker) {
    jiangsuCountryMarker[key].hide();
  }
  map[`江苏Layer`].hide();
  map[`江苏ProjectLayer`].hide();
}
