<template>
    <div class="chart-container">
        <div class="dangerTotal">{{ totalValue }}</div>
        <div ref="chartRef" class="chart3d"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'
import centerImage from '@/assets/images/yibuyiping/safety-l-1.png'

const props = defineProps({
    width: {
        type: String,
        default: '100%'
    },
    height: {
        type: String,
        default: '200px'
    },
    data: {
        type: Array,
        default: () => [
            { name: '日常检查', value: 45, itemStyle: { color: '#0085FF' } },
            { name: '专项检查', value: 30, itemStyle: { color: '#87DBFF' } },
            { name: '突击检查', value: 25, itemStyle: { color: '#EEC01B' } }
        ]
    }
})

const chartRef = ref(null)
let chart = null

// 计算总值
const totalValue = computed(() => {
    return props.data.reduce((sum, item) => sum + item.value, 0)
})

// 获取参数方程
function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height) {
    // 计算
    let midRatio = (startRatio + endRatio) / 2;
    let startRadian = startRatio * Math.PI * 2;
    let endRadian = endRatio * Math.PI * 2;
    let midRadian = midRatio * Math.PI * 2;

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
        isSelected = false;
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    k = typeof k !== 'undefined' ? k : 1 / 3;

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
    let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    let hoverRate = isHovered ? 1.05 : 1;

    // 返回曲面参数方程
    return {
        u: {
            min: -Math.PI,
            max: Math.PI * 3,
            step: Math.PI / 32
        },
        v: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20
        },
        x: function (u, v) {
            if (u < startRadian) {
                return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
                return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        y: function (u, v) {
            if (u < startRadian) {
                return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            if (u > endRadian) {
                return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
            }
            return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
        },
        z: function (u, v) {
            if (u < -Math.PI * 0.5) {
                return Math.sin(u);
            }
            if (u > Math.PI * 2.5) {
                return Math.sin(u) * height;
            }
            // 创建柱状图效果：顶部平面在height高度，底部平面在0高度
            return Math.sin(v) > 0 ? height : 0;
        }
    };
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData, internalDiameterRatio) {
    let series = [];
    let sumValue = 0;
    let startValue = 0;
    let endValue = 0;
    let legendData = [];
    let k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3;

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value;

        let seriesItem = {
            name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
            type: 'surface',
            parametric: true,
            wireframe: {
                show: false
            },
            pieData: pieData[i],
            pieStatus: {
                selected: false,
                hovered: false,
                k: k
            },
            emphasis: {
                itemStyle: {
                    opacity: 1
                }
            }
        };

        if (typeof pieData[i].itemStyle != 'undefined') {
            let itemStyle = {};
            typeof pieData[i].itemStyle.color != 'undefined' ? itemStyle.color = pieData[i].itemStyle.color : null;
            typeof pieData[i].itemStyle.opacity != 'undefined' ? itemStyle.opacity = pieData[i].itemStyle.opacity : null;
            seriesItem.itemStyle = itemStyle;
        }
        series.push(seriesItem);
    }

    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value;
        series[i].pieData.startRatio = startValue / sumValue;
        series[i].pieData.endRatio = endValue / sumValue;
        series[i].parametricEquation = getParametricEquation(
            series[i].pieData.startRatio,
            series[i].pieData.endRatio,
            false,
            false,
            k,
            0.8 // 增加高度，让3D柱状图效果更明显
        );
        startValue = endValue;
        legendData.push(series[i].name);
    }

    return series;
}

const initChart = () => {
    if (!chartRef.value) return

    chart = echarts.init(chartRef.value)
    updateChart()
}

const updateChart = () => {
    // 准备饼图数据
    const optionsData = props.data.map(item => ({
        name: item.name,
        value: item.value,
        itemStyle: {
            color: item.itemStyle.color,
            opacity: 1
        }
    }));

    // 获取3D饼图系列
    const series = getPie3D(optionsData, 0.65);

    // 图表配置
    const option = {
        tooltip: {
            formatter: params => {
                if (params.seriesName !== 'mouseoutSeries') {
                    return `${params.seriesName}: ${params.seriesIndex >= 0 ? series[params.seriesIndex].pieData.value : ''}次`;
                }
            },
            textStyle: {
                fontSize: 14
            },
        },
        xAxis3D: {
            min: -1.2,
            max: 1.2,
        },
        yAxis3D: {
            min: -1.2,
            max: 1.2,
        },
        zAxis3D: {
            min: -0.5,
            max: 1.6,
        },
        grid3D: {
            show: false,
            boxWidth: 160, // 增加宽度
            boxHeight: 30, // 增加高度
            top: '-6%',
            bottom: '15%',
            viewControl: {
                distance: 190,
                alpha: 35, // 调高视角
                beta: 0,
            },
            postEffect: {
                enable: true,
                SSAO: {
                    enable: true,
                    radius: 12,
                    intensity: 1.5
                },
                bloom: {
                    enable: true,
                    bloomIntensity: 0.2
                }
            },
            temporalSuperSampling: {
                enable: true
            },
            environment: 'none'
        },
        series: series
    };

    chart.setOption(option);
}

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

watch(() => props.data, () => {
    updateChart()
}, { deep: true })

onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
    }
    window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.chart-container {
    width: 100%;
    height: 100%;
    position: relative;
}

// 添加3D图表的样式
.chart3d {
    width: 100%;
    height: 100%;
    position: relative;
}

.dangerTotal {
    background: url('@/assets/images/yibuyiping/circle.png') no-repeat center/cover;
    background-size: 100% 100%;
    width: 90px;
    height: 90px;
    position: absolute;
    left: 39%;
    top: 0%;
    font-family: TCloudNumber, TCloudNumber;
    font-weight: bold;
    font-size: 32px;
    color: #FFFFFF;
    text-align: center;
    font-style: normal;
    text-transform: none;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}
</style>