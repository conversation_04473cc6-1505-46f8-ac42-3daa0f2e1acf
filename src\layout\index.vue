<template>
  <div class="app-content" :style="style">
    <Header v-show="showHeader"></Header>
    <AppMain></AppMain>
  </div>
  <div class="map" v-show="showIframe">
    <iframe src="./zjsj-yydp/index.html" id="iframe" class="w-full h-full iframe-container"></iframe>
  </div>
</template>

<script setup>
import { Header, AppMain } from "./components";
import { ref, watch, onMounted, onBeforeUnmount } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const showIframe = ref(false);
const showHeader = ref(false);
const iframeSrc = ref("http://*************:8080/zjsj-yydp/index.html");

// 添加页面刷新监听
const handleBeforeUnload = () => {
  console.log('执行了')

  // 清除本地存储中可能影响地球视图的数据
  localStorage.removeItem("isChina");
  sessionStorage.removeItem("countryLevel");
  sessionStorage.removeItem("countryTitle");
  sessionStorage.removeItem("clickCountry");
  sessionStorage.removeItem("lastProjectRoute");
  sessionStorage.removeItem("lastProjectState");
  // 如果当前页面是/project/country，则在页面刷新时导航回/project/index
  if (route.path.includes("/project/country")) {
    localStorage.setItem("redirectToIndex", "true");
  }
};

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    console.log(newPath)
    if (newPath.includes("/projectDetail")) {
      showIframe.value = false;
      showHeader.value = false;
    } else if (newPath.includes("/yingdi")) {
      showIframe.value = false;
      showHeader.value = false;
    } else if (newPath.includes("/project")) {
      showIframe.value = true;
      showHeader.value = true;
    } else {
      showIframe.value = false;
      showHeader.value = true;
    }
  },
  { immediate: true }
);

onMounted(() => {
  // 添加页面刷新或关闭前的事件监听
  window.addEventListener("beforeunload", handleBeforeUnload);

  // 检查是否需要重定向到/project/index
  if (localStorage.getItem("redirectToIndex") === "true") {
    localStorage.removeItem("redirectToIndex");
    if (route.path.includes("/project/country")) {
      window.location.href = "/#/project/index";
    }
  }

  window.addEventListener("message", (e) => {
    console.log(e)
  });
});

onBeforeUnmount(() => {
  // 移除事件监听
  window.removeEventListener("beforeunload", handleBeforeUnload);
});
</script>

<style lang="scss" scoped>
.app-content {
  width: 100%;
  height: 100%;
  transform-origin: 0 0;
  overflow: hidden;
  background: #0a0f1a;
  // z-index: 2;
  position: relative;
  pointer-events: none;
}

.map {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: all;
  background: url("@/assets/images/xiangmu/wangge.png") no-repeat center center / 100% 100%;

  #iframe1 {
    width: 100%;
    height: 100%;
    resize: none;
  }
}
</style>
