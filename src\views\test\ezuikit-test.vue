<template>
    <div class="ezuikit-test">
        <h2>EZUIKit 错误修复测试页面</h2>

        <div class="test-info">
            <p>这个页面用于测试EZUIKit的错误修复功能</p>
            <p>请打开浏览器开发者工具的Console面板，然后:</p>
            <ol>
                <li>点击"初始化播放器"按钮</li>
                <li>等待播放器加载完成</li>
                <li>点击"销毁播放器"按钮，或刷新页面</li>
                <li>检查Console中是否还有 "Cannot read properties of null (reading 'JS_HideWnd')" 错误</li>
            </ol>
        </div>

        <div class="test-controls">
            <button @click="initPlayer" :disabled="playerInitialized">初始化播放器</button>
            <button @click="destroyPlayer" :disabled="!playerInitialized">销毁播放器</button>
            <button @click="testPageUnload">模拟页面卸载</button>
        </div>

        <div class="video-container" v-show="playerInitialized">
            <div id="testVideoPlayer"></div>
        </div>

        <div class="test-log">
            <h3>测试日志:</h3>
            <div class="log-content" ref="logContainer">
                <p v-for="(log, index) in logs" :key="index" :class="log.type">
                    {{ log.timestamp }} - {{ log.message }}
                </p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import {
    fixEZUIKitUnloadError,
    safeDestroyEZUIKit,
    safeInitEZUIKit,
    cleanupEZUIKitResources
} from '@/utils/ezuikitFix.js';

const playerInitialized = ref(false);
const logs = ref([]);
const logContainer = ref(null);
let testPlayer = null;

// 添加日志
const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    logs.value.push({ timestamp, message, type });

    // 自动滚动到底部
    setTimeout(() => {
        if (logContainer.value) {
            logContainer.value.scrollTop = logContainer.value.scrollHeight;
        }
    }, 10);
};

// 初始化播放器
const initPlayer = async () => {
    try {
        addLog('开始初始化测试播放器...', 'info');

        testPlayer = await safeInitEZUIKit({
            staticPath: "/ezuikit_static/v65",
            id: "testVideoPlayer",
            accessToken: 'ra.dq0xeeg89b777p3f8l21kj43arsb10jz-8adigi1jgw-1p19y2h-pyng4dizu',
            url: `ezopen://open.ys7.com/*********/2.live`,
            quality: 6,
            audio: false,
            useHardDev: true,
            width: 640,
            height: 480
        });

        playerInitialized.value = true;
        addLog('测试播放器初始化成功', 'success');
    } catch (error) {
        addLog(`播放器初始化失败: ${error.message}`, 'error');
    }
};

// 销毁播放器
const destroyPlayer = () => {
    if (testPlayer) {
        addLog('开始销毁测试播放器...', 'info');
        safeDestroyEZUIKit(testPlayer);
        testPlayer = null;
        playerInitialized.value = false;
        addLog('测试播放器已销毁', 'success');
    }
};

// 模拟页面卸载
const testPageUnload = () => {
    addLog('模拟页面卸载事件...', 'info');

    // 触发页面隐藏事件
    const event = new Event('pagehide');
    window.dispatchEvent(event);

    // 触发页面卸载事件
    const unloadEvent = new Event('unload');
    window.dispatchEvent(unloadEvent);

    addLog('页面卸载事件已触发，检查控制台是否有错误', 'info');
};

onMounted(() => {
    // 应用修复
    fixEZUIKitUnloadError();
    addLog('EZUIKit错误修复已应用', 'success');

    // 监听全局错误
    window.addEventListener('error', (event) => {
        if (event.message.includes('JS_HideWnd')) {
            addLog(`检测到JS_HideWnd错误: ${event.message}`, 'error');
        }
    });

    addLog('测试页面已加载，准备就绪', 'info');
});

onUnmounted(() => {
    destroyPlayer();
    cleanupEZUIKitResources();
    addLog('页面卸载，资源已清理', 'info');
});
</script>

<style scoped>
.ezuikit-test {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

h2 {
    color: #333;
    margin-bottom: 20px;
}

.test-info {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.test-info ol {
    margin: 10px 0;
    padding-left: 20px;
}

.test-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.test-controls button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background: #1890ff;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s;
}

.test-controls button:hover:not(:disabled) {
    background: #40a9ff;
}

.test-controls button:disabled {
    background: #d9d9d9;
    cursor: not-allowed;
}

.video-container {
    width: 640px;
    height: 480px;
    border: 2px solid #ddd;
    border-radius: 8px;
    margin-bottom: 20px;
    background: #000;
}

#testVideoPlayer {
    width: 100%;
    height: 100%;
}

.test-log {
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 15px;
}

.test-log h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.log-content {
    max-height: 300px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 10px;
}

.log-content p {
    margin: 5px 0;
    font-family: monospace;
    font-size: 12px;
}

.log-content p.info {
    color: #666;
}

.log-content p.success {
    color: #52c41a;
}

.log-content p.error {
    color: #ff4d4f;
    font-weight: bold;
}
</style>