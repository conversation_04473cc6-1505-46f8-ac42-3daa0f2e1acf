<template>
  <div
    class="relative w-full h-full origin-[0_0] overflow-hidden bg-[url('@/assets/images/layout/bg.png')] bg-no-repeat bg-[size:100%_100%] bg-[right_bottom] z-[2] relative">
    <div class="content relative">
      <div
        class="absolute w-1/2 text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title z-10 top-0 left-2/4 transform -translate-x-1/2">
        {{ title }}
      </div>
      <div class="bg"></div>
      <div class="topBg"></div>
      <div class="leftBg"></div>
      <div class="bottomBg"></div>
      <div class="map" :style="backgroundStyle" v-if="btnIndex == 1"></div>
      <div class="map" v-if="btnIndex == 0">
        <video :src="videoSrc" controls autoplay muted style="width: 100%; height: 100%; object-fit: contain;"></video>
      </div>
      <div class="left">
        <TitleBar title="营地概览" style="z-index: 3"></TitleBar>
        <div class="container">
          <div class="bar" v-for="(item, index) in bars" :key="index">
            <div class="icon"><img :src="item.icon" alt="" /></div>
            <div class="title">{{ item.name }}</div>
          </div>
          <div class="text">
            <el-scrollbar height="100%">
              <div style="height: 100%">{{ text }}</div>
            </el-scrollbar>
          </div>
        </div>
      </div>
      <div :class="['bottom', btnIndex == 1 && 'active']">
        <div class="btns">
          <div class="btn" v-for="(item, index) in btns" :key="index" @click="changeBtn(index)">
            {{ item.name }}
          </div>
        </div>
        <div class="imgs" v-show="btnIndex == 1">
          <div :class="['img', index == imgIndex && 'active']" v-for="(item, index) in images" :key="index"
            @click="changeImg(index)">
            <img :src="item" alt="" />
          </div>
        </div>
        <div class="imgs" v-show="btnIndex == 0">
          <div :class="['img', index == videoIndex && 'active']" v-for="(item, index) in videos" :key="index"
            @click="changeVideo(index)">
            <video :src="item" controls style="width: 100%; height: 100%; object-fit: contain;"></video>
          </div>
        </div>
      </div>
      <img class="pointer-events-auto cursor-pointer absolute top-[30px] left-[60px]"
        src="@/assets/images/xiangmu/back.png" alt="" srcset="" @click="back" style="z-index: 3" />
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getImg } from "@/utils/method";
import request from "@/utils/request";

const router = useRouter();
const route = useRoute();
const bars = ref([
  {
    id: 1,
    name: "xxxx营地名称",
    icon: getImg("yingdi/162.png"),
  },
  { id: 2, name: "xxxx营地地点", icon: getImg("yingdi/161.png") },
]);
const videoSrc = computed(() => {
  return videos.value[videoIndex.value];
});
const btns = ref([
  { id: 1, name: "营地视频" },
  { id: 2, name: "营地照片" },
]);
const text = ref("");
const btnIndex = ref(1);
const changeBtn = (val) => {
  btnIndex.value = val;
};
const back = () => {
  router.replace("/project/country")
};
const title = ref("");
const longTitle = ref("");
const images = ref([]);
const imgIndex = ref(0);
const changeImg = (val) => {
  imgIndex.value = val;
};
const videos = ref([]);
const videoIndex = ref(0);
const changeVideo = (val) => {
  videoIndex.value = val;
  console.log(videos.value[videoIndex.value]);
};

function generateImageUrls(pathStr, options = {}) {
  // 参数校验
  if (typeof pathStr !== "string") {
    throw new TypeError("路径参数必须是字符串类型");
  }

  // 合并配置选项
  const {
    separator = ";",
    prefix = "https://vr.ztmapinfo.com/yydpdatamedia.php?path=",
    allowedExtensions = [".jpg", ".jpeg", ".png", ".webp"],
  } = options;

  // 处理路径
  return pathStr
    .split(separator)
    .map((path) => path.trim())
    .filter((path) => {
      // 空路径过滤
      if (!path) return false;

      // 扩展名校验
      const extension = path.slice(path.lastIndexOf(".")).toLowerCase();
      return allowedExtensions.includes(extension);
    })
    .map((path) => {
      // 编码处理
      const encodedPath = encodeURIComponent(path);
      return `${prefix}${encodedPath}`;
    });
}
function generateVideoUrls(pathStr, options = {}) {
  // 参数校验
  if (typeof pathStr !== "string") {
    throw new TypeError("路径参数必须是字符串类型");
  }

  // 合并配置选项
  const {
    separator = ";",
    prefix = "https://vr.ztmapinfo.com/yydpdatamedia.php?path=",
    allowedExtensions = [".mp4", ".mov", ".avi", ".mkv"],
  } = options;

  // 路径处理流程
  return pathStr
    .split(separator) // 分割原始字符串
    .map((path) => path.trim()) // 去除两端空格
    .filter((path) => {
      // 过滤非法路径
      if (!path) return false;

      // 获取小写扩展名并验证
      const lastDotIndex = path.lastIndexOf(".");
      if (lastDotIndex === -1) return false;

      const extension = path.slice(lastDotIndex).toLowerCase();
      return allowedExtensions.includes(extension);
    })
    .map((path) => {
      // 生成编码后的URL
      const encodedPath = encodeURIComponent(path);
      return `${prefix}${encodedPath}`;
    });
}

// 获取营地照片
async function getYdPhotos(photoIds) {
  if (!photoIds) return;

  try {
    // 先获取token
    const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");

    if (tokenRes.code !== 0 || !tokenRes.data) {
      console.error("获取token失败:", tokenRes);
      return;
    }

    const token = tokenRes.data;
    const userId = "941981453197164545"; // 固定的userId

    // 处理照片ID列表
    const photoIdArray = photoIds.split(',').filter(id => id.trim());
    const photoUrls = [];

    // 限制处理最多5张图片
    const maxPhotos = Math.min(photoIdArray.length, 5);

    for (let i = 0; i < maxPhotos; i++) {
      const photoId = photoIdArray[i].trim();
      if (photoId) {
        // 构建照片URL
        photoUrls.push(`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${photoId}?access_token=${token}&userid=${userId}`);
      }
    }

    if (photoUrls.length > 0) {
      images.value = photoUrls;
    }
  } catch (err) {
    console.error("处理照片失败:", err);
  }
}

// 获取营地视频
async function getYdVideos(videoIds) {
  if (!videoIds) return;

  try {
    // 先获取token
    const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");

    if (tokenRes.code !== 0 || !tokenRes.data) {
      console.error("获取token失败:", tokenRes);
      return;
    }

    const token = tokenRes.data;
    const userId = "941981453197164545"; // 固定的userId

    // 处理视频ID列表
    const videoIdArray = videoIds.split(',').filter(id => id.trim());
    const videoUrls = [];

    for (let i = 0; i < videoIdArray.length; i++) {
      const videoId = videoIdArray[i].trim();
      if (videoId) {
        // 构建视频URL
        videoUrls.push(`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${videoId}?access_token=${token}&userid=${userId}`);
      }
    }

    if (videoUrls.length > 0) {
      videos.value = videoUrls;
    }
  } catch (err) {
    console.error("处理视频失败:", err);
  }
}

// 获取营地数据
async function fetchYdData() {
  try {
    // 从sessionStorage获取数据而不是URL参数
    let queryData = {};
    try {
      queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    } catch (e) {
      console.error("解析营地数据失败", e);
    }

    // 获取营地code
    const ydId = queryData.code || "";
    if (!ydId) {
      console.error("没有找到营地ID");
      return;
    }

    // 请求营地信息
    const res = await request.get("/globalManage/zjmanage/largescreen/getYdxxV2", {
      params: { id: ydId }
    });

    if (res.code === 0 && res.data && res.data.length > 0) {
      const data = res.data[0];

      // 更新营地信息
      title.value = data.营地名称 || "";
      longTitle.value = data.营地名称 || "";
      text.value = data.营地介绍 || "";

      // 更新营地基本信息
      bars.value = [
        {
          id: 1,
          name: data.营地名称 || "暂无名称",
          icon: getImg("yingdi/161.png"),
        },
        {
          id: 2,
          name: `${data.所属国家 || ""} ${data.所属大洲 || ""}`,
          icon: getImg("yingdi/161.png")
        },
      ];

      // 获取照片
      if (data.营地照片) {
        await getYdPhotos(data.营地照片);
      }

      // 获取视频
      if (data.营地视频) {
        await getYdVideos(data.营地视频);
      }
    } else {
      console.error("获取营地数据失败:", res);
    }
  } catch (err) {
    console.error("请求营地信息失败:", err);
  }
}

const backgroundStyle = computed(() => {
  return {
    backgroundImage: `url(${images.value[imgIndex.value]})`,
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center",
    backgroundSize: "100% 100%",
  };
});

onMounted(() => {
  // 获取营地数据
  fetchYdData();
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  pointer-events: all;
  position: relative;
  z-index: 2;
  display: flex;
  align-items: flex-end;

  // &::after {
  //   content: "";
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   background: url("@/assets/images/project/bg.png") no-repeat;
  //   background-position: 0 -36px;
  //   background-size: 100% 1116px;
  //   pointer-events: none;
  //   z-index: 0;
  // }
  .map {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
  }

  .bg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: url("@/assets/images/project/bg.png") no-repeat;
    background-position: 0 -36px;
    background-size: 100% 1116px;
    pointer-events: none;
    z-index: 3;
  }

  .leftBg {
    width: 370px;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background: url("@/assets/images/yingdi/right.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 2;
  }

  .topBg {
    width: 100%;
    height: 240px;
    position: absolute;
    top: 0;
    right: 0;
    background: url("@/assets/images/yingdi/top.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 2;
  }

  .bottomBg {
    width: 100%;
    height: 160px;
    position: absolute;
    left: 0;
    bottom: 0;
    background: url("@/assets/images/yingdi/bottom.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 2;
  }

  .left {
    width: 370px;
    height: 100%;
    padding: 120px 20px 20px 50px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 2;

    &::after {
      content: "";
      width: 370px;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: url("@/assets/images/yingdi/left.png") no-repeat;
      background-size: 100% 100%;
      pointer-events: none;
      z-index: 0;
    }

    .container {
      width: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
      z-index: 3;

      .bar {
        display: flex;
        align-items: center;

        .icon {
          width: 90px;
          height: 80px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .title {
          flex: 1;
          padding-top: 20px;
          padding-left: 10px;
          font-size: 22px;
          color: #fff;
          font-family: "宋体";
        }
      }

      .text {
        height: 700px;
        margin-top: 30px;
        text-indent: 2em;
        line-height: 25px;
        font-family: "宋体";
      }
    }
  }

  .bottom {
    width: 1462px;
    height: 292px;
    background: url("@/assets/images/yingdi/163.png") no-repeat;
    background-size: 100% 100%;
    position: relative;
    box-sizing: border-box;
    padding: 0 30px 30px 30px;
    display: flex;
    align-items: flex-end;

    &.active {
      background: url("@/assets/images/yingdi/159.png") no-repeat;
      background-size: 100% 100%;
    }

    .btns {
      position: absolute;
      top: 27px;
      right: 33px;
      width: 292px;
      height: 35px;
      display: flex;

      .btn {
        width: 50%;
        height: 100%;
        text-align: center;
        line-height: 33px;
        cursor: pointer;
      }
    }

    .imgs {
      width: 100%;
      height: 182px;
      display: flex;
      justify-content: space-around;

      .img {
        width: 240px;
        height: 160px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        padding: 11px;
        border: 1px solid #07483a;

        &.active {
          background: url("@/assets/images/yingdi/160.png") no-repeat;
          background-size: 100% 100%;
        }

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
