import router from './router'
import { clearTokenRefresh, isRelogin } from '@/utils/request'
import { ElMessageBox } from 'element-plus'
import request from '@/utils/request'
import { setupTokenRefresh } from '@/utils/request'
import axios from 'axios'

// 白名单路由
const whiteList = ['/login']

// 自动登录触发路径
const autoLoginTriggerPath = '/noAuth'

// 检查是否精确匹配路径
const isExactPath = (path, target) => {
    return path === target ||
        path === target + '/' ||
        path === '#' + target;
}

// 处理未授权的情况
const handleAuthorized = () => {
    if (!isRelogin.show) {
        if (window.location.href.includes('login?redirect=')) {
            return
        }
        isRelogin.show = true
        ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
            showCancelButton: false,
            closeOnClickModal: false,
            showClose: false,
            confirmButtonText: '重新登录',
            type: 'warning'
        }).then(() => {
            clearTokenRefresh()
            isRelogin.show = false
            router.push('/login')
        })
    }
    return Promise.reject('登录状态已过期')
}

// 自动登录功能
const autoLogin = async () => {
    try {
        console.log('开始自动登录...');
        // 调用登录接口
        const response = await axios.post(`${window.baseEnv.baseURL}/system/auth/login`, {
            username: 'admin',
            password: 'Admin258369'
        }, {
            headers: {
                'Content-Type': 'application/json',
                'tenant-id': '1'
            }
        });

        if (response.data.code === 0) {
            // 登录成功，设置token
            setupTokenRefresh(response.data.data.accessToken, response.data.data.refreshToken);
            localStorage.setItem("currentActiveTab", '业务布局');
            console.log('路由自动登录成功，即将跳转到项目首页');
            return true;
        } else {
            console.error('路由自动登录失败:', response.data.msg);
            return false;
        }
    } catch (error) {
        console.error('路由自动登录异常:', error);
        return false;
    }
};

// 路由守卫 - 跳过所有权限校验，直接放行
router.beforeEach(async (to, from, next) => {
    console.log('路由跳转:', from.path, '->', to.path);

    // 如果访问根路径，重定向到项目首页
    if (to.path === '/' || to.path === '') {
        next({ path: '/project/index' });
        return;
    }

    // 所有其他路由直接放行，不进行权限校验
    next();
})