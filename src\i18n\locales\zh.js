export default {
    // 导航菜单
    nav: {
        businessLayout: '业务布局',
        groupManagement: '集团经营',
        groupAssets: '集团资产',
        oneOfficeOneScreen: '一部一屏',
        onSiteVideo: '现场视频',
        oneProfitFiveRates: '一利五率'
    },

    // 用户界面
    user: {
        admin: '管理员',
        logout: '退出登录',
        logoutConfirm: '确定要退出登录吗？',
        confirm: '确定',
        cancel: '取消',
        warning: '提示'
    },

    // 语言切换
    language: {
        chinese: '中文',
        english: 'English'
    },

    // 登录页面
    login: {
        title: '中江国际全球经营管理数字化平台',
        subtitle: '- CJI Business Management Digital Platform -',
        username: '请输入用户名',
        password: '请输入密码',
        captcha: '请输入验证码',
        rememberPassword: '记住密码',
        forgotPassword: '忘记密码?',
        loginButton: '登 录',
        loginSuccess: '登录成功',
        loginFailed: '登录失败',
        usernameRequired: '请输入账号',
        passwordRequired: '请输入密码',
        captchaRequired: '请输入验证码',
        captchaError: '验证码错误',
        usernameLengthError: '账号长度应在 3 到 20 个字符之间',
        passwordLengthError: '密码长度应在 6 到 20 个字符之间',
        loginError: '登录失败，请检查账号密码',
        agree: '我已阅读并同意',
        serviceAgreement: '服务协议',
        privacyAgreement: '隐私协议',
        agreeRequired: '请同意协议'
    },

    // 项目页面
    project: {
        totalProjects: '在建项目总数',
        totalAmount: '在建项目合同总额',
        annualProjectRevenue: '年度项目收款总额',
        annualProjectOutput: '年度项目完成产值',
        annualContractValue: '年度项目签约额',
        revenue: '工程款收入',
        budgetThisYear: '本年预算',
        completion: '完成度',
        yearOnYearGrowth: '同比增长',
        totalStaff: '人员总数',
        safetyQualityInspection: '在建项目安全质量检查',
        inspectionRecords: '排查记录',
        hazardRecords: '隐患记录',
        qualityProblemsTotal: '质量问题总数',
        rectificationRate: '整改率',
        timelyRectificationRate: '整改及时率',
        qualityProblemsTrend: '年质量问题及整改趋势',
        chinese: '中方',
        foreign: '外籍',
        formalEmployees: '正式员工',
        otherEmployment: '其他形式用工',
        people: '人',
        overview: '在建项目总览',
        // 人员统计相关
        overseasLocalStaff: '海外属地员工',
        laborDispatch: '劳务派遣',
        otherEmploymentForm: '其他用工形式人员',
        partnerStaff: '合作单位人员',
        // 质量问题相关
        pendingRectification: '待整改',
        qualified: '合格',
        pendingVerification: '待核验',
        rectified: '已整改',
        problemCount: '问题数',
        rectificationCount: '整改数',
        // 月份
        march: '3月',
        april: '4月',
        may: '5月',
        june: '6月',
        july: '7月',
        august: '8月',
        september: '9月',
        october: '10月',
        november: '11月',
        december: '12月',
        january: '1月',
        february: '2月',
        // 地区
        domestic: '国内',
        international: '国际',
        // 业务数据相关
        overseasBusinessCountries: '境外业务布局国家',
        overseasProjects: '境外在建项目',
        contractAmount: '合同总额',
        cumulativeOutput: '累计产值',
        cumulativeEngineering: '累收工程款',
        overseasPersonnel: '海外人员数量',
        domesticBusinessProvinces: '境内业务省份',
        domesticProjects: '国内在建项目(含设计)',
        units: {
            project: '个',
            tenThousandYuan: '万元',
            tenThousandUSD: '万美元',
            hundredMillionYuan: '亿元',
            hundredMillionUSD: '亿美元',
            person: '人',
            record: '条'
        },
        // 数据字段翻译
        dataFields: {
            overseasBusinessCountries: '境外业务布局国家',
            overseasProjects: '境外在建项目',
            contractAmount: '合同总额',
            cumulativeOutput: '累计产值',
            cumulativeEngineering: '累收工程款',
            overseasPersonnel: '海外人员数量',
            domesticBusinessProvinces: '境内业务省份',
            domesticProjectsWithDesign: '国内在建项目(含设计)',
            projectTotalPersonnel: '项目总人数',
            formalEmployees: '正式员工',
            otherEmploymentForms: '其他形式用工',
            currentYearCollectionRate: '本年回款率',
        },
        // 员工类型
        staffTypes: {
            formalEmployees: '正式员工',
            overseasLocalStaff: '海外属地员工',
            laborDispatch: '劳务派遣',
            otherEmploymentForms: '其他用工形式人员',
            partnerStaff: '合作单位人员'
        },
        // 质量问题状态
        qualityStatus: {
            pendingRectification: '待整改',
            qualified: '合格',
            pendingVerification: '待核验',
            rectified: '已整改'
        },
        // 图表项目
        chartItems: {
            problemCount: '问题数',
            rectificationCount: '整改数',
            domestic: '国内',
            international: '国际'
        },
        // 月份
        months: {
            march: '3月',
            april: '4月',
            may: '5月',
            june: '6月',
            july: '7月',
            august: '8月',
            september: '9月',
            october: '10月',
            november: '11月',
            december: '12月',
            january: '1月',
            february: '2月'
        }
    },

    // 业务管理
    business: {
        overview: '经营总览',
        performance: '经营业绩',
        analysis: '经营分析',
        revenue2025: '2025年度营收',
        revenue2024: '2024年度营收',
        revenue2023: '2023年度营收',
        annualRevenue: '年度营收',
        billionYuan: '亿元',

        // 业务经营数据页面
        businessData: {
            title: '业务经营数据',
            internationalEngineering: '国际工程项目经营数据',
            domesticEngineering: '国内工程项目经营数据',
            internationalTrade: '国际贸易经营数据',
            investmentBusiness: '投资经营数据',

            // 数据指标
            metrics: {
                newContractAmount: '新签合同额',
                internationalOutput: '国际产值',
                constructionOutput: '建筑业产值',
                operatingRevenue: '营业收入',
                totalProfit: '利润总额',
                importAmount: '进口额',
                exportAmount: '出口额'
            },

            // 目标和完成度
            target: {
                thisYearTarget: '本年目标',
                completion: '完成度',
                yearOnYearGrowth: '同比增长',
                lastYear: '去年',
                thisYear: '今年'
            },

            // 单位
            units: {
                tenThousandUSD: '万美元',
                tenThousandRMB: '万元',
                percent: '%'
            },

            // 对话框
            dialog: {
                title: '2024年主要新签项目',
                companyHeader: '公司',
                projectHeader: '项目名称',
                contractHeader: '合同额（万美元）'
            }
        }
    },

    // 资产管理
    assets: {
        overview: '资产总览',
        management: '资产管理',
        analysis: '资产分析',
        netFixedAssets: '固定资产净额',
        currentYearDepreciation: '本年折旧',
        investmentRealEstate: '投资性房地产',
        assetRanking: '资产排名',
        land: '土地',
        houseAndBuilding: '房屋及建筑物',
        transportationEquipment: '运输设备',
        machinery: '机器设备',
        officeAndOtherEquipment: '办公及其他设备',
        totalCurrentYearDepreciation: '本年折旧总额',
        depreciationRate: '折旧率',
        yearEndAmount: '年末金额',
        fairValueChange: '公允价值变动',
        cost: '成本',
        unit: '（万元）',
        rankingTop5: 'TOP5'
    },

    // 一部一屏
    oneOfficeOneScreen: {
        domestic: '国内业务',
        international: '国际业务',
        overview: '业务总览',
        // 集团总部
        group: {
            headquarters: '中江国际集团'
        },
        // 党委系统
        partyCommittee: {
            partyCommittee: '党委会',
            disciplinaryCommittee: '纪委',
            disciplinaryOffice: '纪委办公室',
            supervisionInspectionDept: '监督检查部',
            investigationDept: '审查调查部',
            organizationDept: '党委组织部',
            partyMassWorkDept: '党群工作部'
        },
        // 治理结构
        governance: {
            boardOfDirectors: '董事会',
            strategyInvestmentBudgetCommittee: '战略、投资与预算委员会',
            compensationAssessmentCommittee: '薪酬与考核委员会',
            auditCommittee: '审计委员会',
            riskComplianceCommittee: '风险与合规管理委员会',
            managementTeam: '经理层'
        },
        // 职能部门
        functionalDepts: {
            groupOffice: '集团办公室',
            financeDept: '财务部',
            domesticBusinessDept: '国内事业部',
            internationalBusinessDept: '国际事业部',
            enterprisePlanningDept: '企业规划管理部',
            auditDept: '审计部',
            legalComplianceDept: '法律合规部',
            qualitySafetyDept: '质量安全部'
        },
        // 国际工程
        internationalEngineering: {
            title: '国际工程',
            overseasEngineeringCo1: '海外工程一公司',
            overseasEngineeringCo2: '海外工程二公司',
            overseasEngineeringCo3: '海外工程三公司',
            jianshaConstructionCo: '江苏建达建设股份有限公司',
            mauritiusCo: '中江国际毛里求斯公司',
            papuaNewGuineaCo: '中江国际巴布亚新几内亚公司',
            humanResourcesBranch: '人力资源分公司',
            overseasCooperationInvestmentCo: '江苏省海外合作投资有限公司'
        },
        // 国内工程
        domesticEngineering: {
            title: '国内工程',
            jiangsuConstructionGroup: '江苏建设集团有限公司',
            thirdConstructionBranch: '第三建设分公司',
            fifthConstructionBranch: '江苏中江国际建筑集团有限公司第五建设分公司',
            decorationCurtainWallCo: '江苏省装饰幕墙工程有限公司',
            architecturalDesignInstitute: '建筑设计院',
            publicEngineeringConstructionCenter: '江苏省公共工程建设中心有限公司'
        },
        // 国际贸易
        internationalTrade: {
            title: '国际贸易',
            overseasImportExportCo: '江苏省中江海外进出口有限公司',
            hengtaiImportExportCo: '江苏省中江恒泰进出口有限公司',
            internationalChemicalCo: '江苏省中江国际化工有限公司',
            internationalImportExportCo: '江苏省中江国际进出口有限公司',
            newMaterialsTechCo: '江苏省新材料科技发展有限公司'
        },
        // 投资服务
        investmentServices: {
            title: '投资服务',
            equipmentCompleteSetCo: '江苏省设备成套股份有限公司',
            realEstateCo: '江苏中江国际房地产有限公司',
            investmentCo: '江苏中江国际投资有限公司',
            urbanRuralConstructionInvestmentCo: '江苏省城乡建设投资有限公司',
            digitalConstructionTechCo: '江苏中江数字建设技术有限公司',
            techInnovationIndustryDevelopmentCo: '江苏省中江科创产业发展有限公司'
        }
    },

    // 现场视频
    onSiteVideo: {
        liveVideo: '实时视频',
        videoManagement: '视频管理',
        monitoring: '监控中心'
    },

    // 一利五率
    oneProfitFiveRates: {
        profit: '利润',
        profitMargin: '利润率',
        costRate: '成本率',
        expenseRate: '费用率',
        taxRate: '税费率',
        analysis: '财务分析',
        profitTotal: '利润总额',
        operatingCashRatio: '营业现金比率',
        netAssetIncome: '净资产收益率',
        assetLiabilityRatio: '资产负债率',
        allStaffLabor: '全员劳动生产率',
        rdFunding: '研发经费投入强度',
        unit: '万元',
        percent: '%',
        indicators: {
            operatingCashRatio: '营业现金比率',
            netAssetIncome: '净资产收益率',
            assetLiabilityRatio: '资产负债率',
            allStaffLabor: '全员劳动生产率',
            rdFunding: '研发经费投入强度'
        }
    },

    // 营地页面
    camp: {
        overview: '营地概览',
        name: 'xxxx营地名称',
        location: 'xxxx营地地点',
        video: '营地视频',
        photos: '营地照片',
        noName: '暂无名称'
    },

    // 国家详情页面
    country: {
        projectsUnderConstruction: '在建项目总数',
        engineeringRevenue: '工程款收入',
        contractAmount: '在建项目合同总额',
        cumulativeOutput: '累计产值',
        overseasPersonnel: '海外人员数量',
        chinese: '中方',
        foreign: '外籍',
        formalEmployees: '正式员工',
        otherEmploymentForms: '其他形式用工',
        laborDispatch: '劳务派遣',
        otherEmployment: '其他用工',
        partnerUnits: '合作单位',
        units: {
            projects: '个',
            people: '人',
            tenThousandYuan: '万元',
            tenThousandUSD: '万美元'
        }
    },

    // 项目详情页面
    projectDetail: {
        // 顶部按钮
        tabs: {
            overview: '项目概览',
            qualitySafety: '质量安全',
            equipmentFacilities: '设备设施'
        },
        // 项目数据字段
        fields: {
            projectName: '项目名称',
            contractAmount: '合同金额',
            projectOutput: '产值',
            engineeringPayment: '工程回款总额',
            projectProgress: '项目进度',
            constructionProgress: '施工进度',
            startDate: '开工日期',
            totalDuration: '总工期',
            projectPhotos: '项目照片',
            projectVideo: '项目视频',
            totalPersonnel: '项目总人数',
            chinese: '中方',
            foreign: '外籍',
            country: '国家',
            province: '省',
            city: '市',
            district: '区',
            longitude: '项目经度',
            latitude: '项目纬度',
            formalEmployees: '正式员工',
            laborDispatch: '劳务派遣',
            otherEmploymentForms: '其他形式用工',
            partnerPersonnel: '合作单位人员'
        },
        // 操作按钮
        actions: {
            back: '返回',
            thumbnailAlt: '缩略图'
        },
        // 单位
        units: {
            tenThousandYuan: '万元',
            tenThousandUSD: '万美元',
            people: '人',
            percent: '%',
            projects: '个',
            days: '天'
        },
        // 提示信息
        messages: {
            noData: '暂无数据',
            loading: '加载中...',
            defaultImageUsed: '使用默认图片',
            imageLoadFailed: '图片加载失败',
            dataLoadFailed: '数据加载失败'
        },
        // 项目概览页面 (item1)
        overview: {
            projectTitle: '项目名称',
            projectManagement: '项目管理',
            projectPersonnel: '项目人员',
            videoMonitoring: '视频监控',
            basicInfo: {
                contractAmount: '合同额',
                projectOutput: '产值',
                totalDuration: '总工期',
                startDate: '开工日期',
                constructionProgress: '施工进度',
                engineeringPayment: '工程回款总额',
                totalPersonnel: '项目总人数'
            }
        },
        // 质量安全页面 (item2)
        qualitySafety: {
            safetyHazards: '安全隐患',
            qualityIssues: '质量问题',
            inspectionRecords: '排查记录',
            hazardRecords: '隐患记录',
            rectificationRate: '整改率',
            timelyRectificationRate: '整改及时率',
            status: {
                resolved: '已销项',
                pendingReview: '待复查',
                pendingRectification: '待整改',
                overdue: '超期',
                qualified: '合格',
                pendingVerification: '待核验'
            },
            units: {
                types: '种',
                percent: '%'
            }
        },
        // 设备设施页面 (item3)
        equipmentFacilities: {
            videoMonitoring: '视频监控',
            dustMonitoring: {
                title: '扬尘监控',
                all: '全部',
                data: {
                    pm25: 'PM2.5',
                    pm10: 'PM10',
                    temperature: '温度',
                    humidity: '湿度',
                    windSpeed: '风速',
                    windDirection: '风向',
                    noise: '噪音',
                    pressure: '气压'
                },
                units: {
                    microgram: 'μg/m³',
                    celsius: '℃',
                    percent: '%',
                    meterPerSecond: 'm/s',
                    decibel: 'dB',
                    kilopascal: 'kPa'
                },
                values: {
                    northeast: '东北'
                }
            },
            tabs: {
                generalDetection: '常规检测',
                towerCraneMonitoring: '塔吊监测',
                elevatorMonitoring: '升降机监测',
                unloadingPlatform: '卸料平台'
            },
            towerCrane: {
                totalCount: '塔吊总数',
                running: '运行中',
                stopped: '已停机',
                height: '塔身高度',
                jibLength: '起重臂长',
                counterJibLength: '平衡臂长'
            },
            elevator: {
                totalCount: '升降机总数',
                onlineCount: '在线总数',
                todayWarnings: '今日预警',
                todayAlarms: '今日报警次数',
                offlineCount: '离线数',
                weight: '载重',
                height: '高度',
                xTilt: 'x倾角',
                yTilt: 'y倾角'
            },
            unloading: {
                realTimeWeight: '实时载重',
                warningValue: '载重预警值',
                alarmValue: '载重报警值'
            },
            units: {
                count: '个',
                meter: 'm',
                kg: 'kg',
                degree: '°'
            }
        }
    },

    // 组件相关
    components: {
        total: '总量',
        projectDistribution: '项目分布',
        staffStats: {
            construction: '施工人员',
            safety: '安管人员',
            management: '管理人员'
        },
        safetyInspection: {
            daily: '日常检查',
            special: '专项检查',
            surprise: '突击检查'
        }
    },

    // 系统消息
    system: {
        loading: '加载中...',
        noData: '暂无数据',
        error: '错误',
        success: '成功',
        tokenFailed: '获取token失败',
        dataFailed: '获取数据失败',
        loginFailed: '登录失败',
        photoFailed: '处理照片失败',
        videoFailed: '处理视频失败',
        campDataFailed: '获取营地数据失败',
        parseCampDataFailed: '解析营地数据失败',
        noCampId: '没有找到营地ID',
        requestCampInfoFailed: '请求营地信息失败',
        overseasDataFailed: '获取境外数据失败',
        domesticDataFailed: '获取境内数据失败',
        restoreStateFailed: '恢复状态失败'
    },

    // 一部一屏
    oneOfficeOneScreen: {
        detail: {
            title: '一部一屏详情',

            // 左侧面板
            leftPanel: {
                personnelManagement: '人员管理',
                projectInspection: '项目检查',
                safetyEducation: '安全教育',
                hazardOverview: '隐患总览'
            },

            // 人员管理
            personnel: {
                totalPersonnel: '项目人员总数',
                constructionWorkers: '施工人员',
                safetyManagers: '安管人员',
                managementStaff: '管理人员'
            },

            // 项目检查
            inspection: {
                totalProjects: '项目总数',
                group: '集团',
                subsidiary: '分公司',
                project: '项目',
                selfInspection: '自检',
                times: '次'
            },

            // 安全教育
            safetyEducation: {
                monthlyExamPassRate: '月度考试合格率',
                safetyTrainingCoverage: '安全培训覆盖率'
            },

            // 隐患总览
            hazard: {
                totalHazards: '隐患总数',
                rectified: '已整改',
                unrectified: '未整改'
            },

            // 中央地图区域统计
            centerStats: {
                greenConstructionAlerts: '绿色施工警数',
                aiWarnings: 'AI预警数量',
                videoMonitoringDevices: '视频监控设备数',
                majorHazardousProjects: '危大工程数'
            },

            // 质量管理
            qualityManagement: {
                title: '质量管理',
                totalInspections: '检查总数',
                firstTimeAcceptanceRate: '一次性验收通过率',
                repairRectificationRate: '返修整改率'
            },

            // 质量问题趋势
            qualityTrend: {
                title: '质量问题趋势',
                overdue: '超期',
                notOverdue: '未超期',
                veryUrgent: '非常紧急',
                generalSituation: '一般情况'
            },

            // 创优奖项
            awards: {
                title: '创优的奖项',
                nationalQualityAward: '国家级优质工程奖',
                provincialSafetyAward: '省级安全文明施工奖',
                municipalGreenBuildingAward: '市级绿色建筑示范奖',
                industryQualityAward: '行业质量管理奖',
                technicalInnovationAward: '技术创新突出贡献奖',
                environmentalProtectionAward: '环保建设先进单位奖',
                smartConstructionAward: '智能建造优秀项目奖',
                lubanAward: '建筑工程鲁班奖'
            },

            // 法律法规
            legalRegulations: {
                title: '法律法规',
                regulations: '制度',
                standards: '规范',
                notices: '通知'
            },

            // 底部按钮
            bottomButtons: {
                safety: '安全',
                quality: '质量',
                environment: '环境'
            },

            // 通用单位
            units: {
                people: '人',
                items: '个',
                times: '次',
                percent: '%'
            }
        },

        // 安全页面
        safety: {
            title: '安全管理',

            // 检查类型分析
            inspectionTypeAnalysis: {
                title: '检查类型分析',
                dailyInspection: '日常检查',
                specialInspection: '专项检查',
                surpriseInspection: '突击检查',
                generalRecord: '一般记录',
                hazardRectification: '隐患整改',
                shutdownRectification: '停工整改'
            },

            // 企业检查
            enterpriseInspection: {
                title: '企业检查',
                totalEnterprises: '企业总数',
                hazardCount: '隐患数'
            },

            // 项目检查分析
            projectInspectionAnalysis: {
                title: '项目检查分析',
                inspectionPoint: '巡检点',
                table: {
                    inspectionTime: '巡查时间',
                    inspectionPoint: '巡检点',
                    inspector: '巡检人',
                    inspectionSituation: '巡检情况',
                    qrCode: '二维码',
                    operation: '操作',
                    search: '搜索'
                }
            },

            // 隐患类型分析
            hazardTypeAnalysis: {
                title: '隐患类型分析',
                hazardType: '隐患类型',
                siteGovernance: '场地治理',
                hazard2: '隐患2',
                hazard3: '隐患3',
                hazard4: '隐患4'
            },

            // 隐患问题top5
            hazardTop5: {
                title: '隐患问题top5',
                hazardTypeLabel: '隐患类型：'
            },

            // 通用单位
            units: {
                items: '个',
                times: '次',
                percent: '%'
            }
        }
    },

    // 通用
    common: {
        loading: '加载中...',
        noData: '暂无数据',
        error: '错误',
        success: '成功',
        save: '保存',
        edit: '编辑',
        delete: '删除',
        search: '搜索',
        back: '返回',
        next: '下一步',
        previous: '上一步',
        submit: '提交',
        reset: '重置',
        close: '关闭',
        total: '总计',
        detail: '详情',
        more: '更多',
        expand: '展开',
        collapse: '不展开',
        languageSwitch: '语言切换',
        dropdown: '下拉菜单',
        header: {
            title: '中江国际集团全球经营管理数字化平台'
        }
    }
} 