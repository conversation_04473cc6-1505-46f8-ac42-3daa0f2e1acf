<template>
    <div class="hazard-chart-container">
        <div ref="chartRef" class="hazard-chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
    width: {
        type: String,
        default: '100%'
    },
    height: {
        type: String,
        default: '100%'
    },
    data: {
        type: Array,
        default: () => [
            { name: '场地治理', value: 4 },
            { name: '隐患2', value: 2 },
            { name: '隐患3', value: 1 },
            { name: '隐患4', value: 2 }
        ]
    }
})

const chartRef = ref(null)
let chart = null

// 指定的四个颜色
const colors = ['#7190FF', '#45CDFF', '#F8DE4D', '#06FFCF']

const initChart = () => {
    if (!chartRef.value) return

    // 确保DOM元素已经有宽高
    setTimeout(() => {
        chart = echarts.init(chartRef.value)
        updateChart()

        // 立即触发一次resize确保渲染正确
        setTimeout(() => {
            chart.resize()
        }, 100)
    }, 0)
}

const updateChart = () => {
    if (!chart) return

    // 准备数据，添加指定颜色
    const chartData = props.data.map((item, index) => ({
        name: item.name,
        value: item.value,
        itemStyle: {
            color: colors[index % colors.length]
        }
    }))

    const option = {
        backgroundColor: 'transparent', // 确保背景透明
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            borderColor: '#409EFF',
            borderWidth: 1,
            textStyle: {
                color: '#fff',
                fontSize: 12
            }
        },
        series: [
            {
                name: '隐患类型',
                type: 'pie',
                radius: ['70%', '80%'], // 调整内外径比例
                center: ['50%', '50%'],
                avoidLabelOverlap: false,
                label: {
                    show: false // 不显示标签
                },
                emphasis: {
                    label: {
                        show: false
                    },
                    scale: true,
                    scaleSize: 5
                },
                labelLine: {
                    show: false
                },
                itemStyle: {
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                    borderWidth: 1
                },
                data: chartData,
                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: function (idx) {
                    return Math.random() * 200;
                }
            }
        ]
    }

    chart.setOption(option, true)
}

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

watch(() => props.data, () => {
    updateChart()
}, { deep: true })

onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
    }
    window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.hazard-chart-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.hazard-chart {
    width: 100%;
    height: 100%;
}
</style>