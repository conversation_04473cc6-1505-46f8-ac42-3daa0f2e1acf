// 国际化文本更新工具
// 这个工具用于批量识别和替换项目中的硬编码中文文本

// 常见的文本映射
export const textMappings = {
    // 导航相关
    '业务布局': 'nav.businessLayout',
    '集团经营': 'nav.groupManagement',
    '集团资产': 'nav.groupAssets',
    '一部一屏': 'nav.oneOfficeOneScreen',
    '现场视频': 'nav.onSiteVideo',
    '一利五率': 'nav.oneProfitFiveRates',

    // 项目页面
    '在建项目总数': 'project.totalProjects',
    '在建项目合同总额': 'project.totalAmount',
    '工程款收入': 'project.revenue',
    '本年预算': 'project.budgetThisYear',
    '完成度': 'project.completion',
    '同比增长': 'project.yearOnYearGrowth',
    '人员总数': 'project.totalStaff',
    '中方': 'project.chinese',
    '外籍': 'project.foreign',
    '正式员工': 'project.formalEmployees',
    '其他形式用工': 'project.otherEmployment',

    // 业务页面
    '年度营收': 'business.annualRevenue',
    '2025年度营收': 'business.revenue2025',
    '2024年度营收': 'business.revenue2024',
    '2023年度营收': 'business.revenue2023',

    // 用户相关
    '管理员': 'user.admin',
    '退出登录': 'user.logout',
    '确定要退出登录吗？': 'user.logoutConfirm',

    // 通用
    '确定': 'common.confirm',
    '取消': 'common.cancel',
    '保存': 'common.save',
    '提交': 'common.submit',
    '重置': 'common.reset',
    '删除': 'common.delete',
    '编辑': 'common.edit',
    '查看': 'common.view',
    '搜索': 'common.search',
    '返回': 'common.back',
    '关闭': 'common.close',
    '加载中...': 'common.loading',
    '暂无数据': 'common.noData',
    '总计': 'common.total',
    '详情': 'common.detail',
    '更多': 'common.more',

    // 单位
    '个': 'project.units.project',
    '万元': 'project.units.tenThousandYuan',
    '人': 'project.units.person',
    '条': 'project.units.record',
    '亿元': 'business.billionYuan'
}

// 生成替换后的Vue模板文本
export function generateI18nTemplate(originalText) {
    const key = textMappings[originalText]
    if (key) {
        return `{{ $t('${key}') }}`
    }
    return originalText
}

// 批量替换函数
export function batchReplace(content) {
    let result = content

    Object.keys(textMappings).forEach(originalText => {
        const i18nKey = textMappings[originalText]
        const regex = new RegExp(`"${originalText}"`, 'g')
        const templateRegex = new RegExp(`>${originalText}<`, 'g')

        // 替换属性值中的文本
        result = result.replace(regex, `$t('${i18nKey}')`)

        // 替换模板中的文本
        result = result.replace(templateRegex, `>{{ $t('${i18nKey}') }}<`)
    })

    return result
}

// 检查文件是否需要更新
export function needsI18nUpdate(content) {
    return Object.keys(textMappings).some(text =>
        content.includes(`"${text}"`) || content.includes(`>${text}<`)
    )
} 