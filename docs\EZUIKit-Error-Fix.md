# EZUIKit JS_HideWnd 错误修复方案

## 问题描述

在使用萤石云EZUIKit视频播放器SDK时，页面卸载或刷新时会出现以下错误：

```
TypeError: Cannot read properties of null (reading 'JS_HideWnd')
    at window.onpagehide (ezuikit.js:1:2733225)
TypeError: Cannot read properties of null (reading 'JS_HideWnd')
    at window.onunload (ezuikit.js:1:2733111)
```

## 错误原因

1. **资源清理顺序问题**: EZUIKit在页面卸载时尝试调用某个插件对象的`JS_HideWnd`方法
2. **对象生命周期不匹配**: 该对象可能已经被提前销毁或设置为null
3. **缺少空值检查**: EZUIKit库内部没有进行足够的空值检查

## 修复方案

### 1. 创建修复工具

在 `src/utils/ezuikitFix.js` 中实现了以下修复功能：

- `fixEZUIKitUnloadError()`: 修复页面卸载错误
- `safeDestroyEZUIKit()`: 安全销毁播放器
- `safeInitEZUIKit()`: 安全初始化播放器
- `cleanupEZUIKitResources()`: 清理所有相关资源
- `setupEZUIKitVisibilityHandler()`: 页面可见性处理

### 2. 核心修复逻辑

```javascript
// 重新定义页面卸载处理器，添加错误捕获
window.onpagehide = function(event) {
  try {
    if (originalOnPageHide && typeof originalOnPageHide === 'function') {
      originalOnPageHide.call(this, event);
    }
  } catch (error) {
    console.warn('EZUIKit页面隐藏事件处理出错，已忽略:', error);
  }
};

window.onunload = function(event) {
  try {
    if (originalOnUnload && typeof originalOnUnload === 'function') {
      originalOnUnload.call(this, event);
    }
  } catch (error) {
    console.warn('EZUIKit页面卸载事件处理出错，已忽略:', error);
  }
};
```

### 3. 使用方法

#### 全局应用（推荐）

在 `src/main.js` 中：

```javascript
import { fixEZUIKitUnloadError, cleanupEZUIKitResources } from "./utils/ezuikitFix.js";

// 应用程序启动时修复
fixEZUIKitUnloadError();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  cleanupEZUIKitResources();
});
```

#### 组件级应用

在具体的Vue组件中：

```javascript
import { 
  fixEZUIKitUnloadError, 
  safeInitEZUIKit, 
  safeDestroyEZUIKit 
} from '@/utils/ezuikitFix.js';

export default {
  async mounted() {
    // 应用修复
    fixEZUIKitUnloadError();
    
    // 安全初始化播放器
    this.player = await safeInitEZUIKit({
      id: "videoPlayer",
      accessToken: "your-token",
      url: "your-video-url"
    });
  },
  
  beforeUnmount() {
    // 安全销毁播放器
    safeDestroyEZUIKit(this.player);
  }
}
```

## 修复效果

### 修复前

- 页面刷新或关闭时控制台出现红色错误信息
- 可能影响用户体验和错误监控

### 修复后

- 页面卸载时不再出现JS_HideWnd相关错误
- 控制台显示友好的警告信息（如果出现问题）
- 播放器资源得到正确清理

## 测试验证

### 1. 自动测试页面

访问 `src/views/test/ezuikit-test.vue` 测试页面：

1. 点击"初始化播放器"
2. 等待播放器加载完成
3. 点击"销毁播放器"或刷新页面
4. 检查控制台是否还有错误

### 2. 手动测试步骤

1. 进入使用EZUIKit的页面（如现场视频页面）
2. 等待视频播放器加载完成
3. 执行以下操作之一：
   - 刷新页面 (F5)
   - 关闭浏览器标签页
   - 导航到其他页面
4. 查看浏览器控制台，确认没有JS_HideWnd错误

## 兼容性说明

- ✅ 与现有EZUIKit功能完全兼容
- ✅ 不影响正常的播放器功能
- ✅ 支持所有主流浏览器
- ✅ 适用于Vue 3组件生命周期

## 注意事项

1. **必须在EZUIKit加载之后应用修复**
2. **建议在应用启动时全局应用一次**
3. **每个使用EZUIKit的组件都应该使用安全方法**
4. **确保在组件卸载时清理资源**

## 文件说明

| 文件 | 说明 |
|------|------|
| `src/utils/ezuikitFix.js` | 修复工具主文件 |
| `src/views/test/ezuikit-test.vue` | 测试页面 |
| `src/views/xianchangshipin/index.vue` | 应用修复的示例页面 |
| `src/main.js` | 全局修复应用 |

## 更新记录

- **2024-01-XX**: 初始版本，修复JS_HideWnd错误
- **2024-01-XX**: 添加页面可见性处理
- **2024-01-XX**: 完善错误处理和资源清理
