// 调试工具函数
export const debugUtils = {
    // 检查环境
    checkEnvironment() {
        const info = {
            userAgent: navigator.userAgent,
            url: window.location.href,
            protocol: window.location.protocol,
            host: window.location.host,
            pathname: window.location.pathname,
            hash: window.location.hash,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            timestamp: new Date().toISOString()
        };
        console.log('环境信息:', info);
        return info;
    },

    // 检查资源加载状态
    checkResources() {
        const images = document.querySelectorAll('img');
        const scripts = document.querySelectorAll('script');
        const links = document.querySelectorAll('link');

        const report = {
            images: Array.from(images).map(img => ({
                src: img.src,
                loaded: img.complete,
                error: img.naturalWidth === 0
            })),
            scripts: Array.from(scripts).map(script => ({
                src: script.src,
                type: script.type
            })),
            styles: Array.from(links).map(link => ({
                href: link.href,
                rel: link.rel
            }))
        };

        console.log('资源加载状态:', report);
        return report;
    },

    // 检查错误
    setupErrorHandlers() {
        // JavaScript错误
        window.addEventListener('error', (e) => {
            console.error('JavaScript错误:', {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                error: e.error
            });
        });

        // 资源加载错误
        window.addEventListener('error', (e) => {
            if (e.target !== window) {
                console.error('资源加载错误:', {
                    tagName: e.target.tagName,
                    src: e.target.src || e.target.href,
                    error: e.target.error
                });
            }
        }, true);

        // Promise rejection
        window.addEventListener('unhandledrejection', (e) => {
            console.error('未处理的Promise拒绝:', e.reason);
        });
    },

    // 路由调试
    logRouteInfo(router, route) {
        console.log('路由信息:', {
            currentRoute: route.path,
            routeName: route.name,
            params: route.params,
            query: route.query,
            matched: route.matched.map(r => r.path),
            hash: route.hash
        });
    },

    // 性能调试
    measurePerformance() {
        if (performance.timing) {
            const timing = performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;

            console.log('性能指标:', {
                loadTime: loadTime + 'ms',
                domReady: domReady + 'ms',
                firstPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-paint')?.startTime || 'N/A'
            });
        }
    },

    // 生成诊断报告
    generateReport() {
        const report = {
            environment: this.checkEnvironment(),
            resources: this.checkResources(),
            performance: {
                timing: performance.timing ? {
                    loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
                    domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
                } : null
            },
            errors: window.debugErrors || [],
            timestamp: new Date().toISOString()
        };

        console.log('完整诊断报告:', report);
        return report;
    }
};

// 在开发环境或需要时启用调试
export function enableDebugging() {
    window.debugErrors = [];
    debugUtils.setupErrorHandlers();

    // 添加全局调试对象
    window.debugUtils = debugUtils;

    console.log('调试模式已启用，使用 window.debugUtils 访问调试工具');
} 