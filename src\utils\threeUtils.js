import * as THREE from 'three'

// 场景管理类
export class SceneManager {
  constructor(container) {
    this.container = container
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
  }

  // 初始化场景
  initScene() {
    this.scene = new THREE.Scene()
    window.scene = this.scene
    return this.scene
  }

  // 初始化相机
  initCamera(fov = 45, near = 0.1, far = 1000) {
    this.camera = new THREE.PerspectiveCamera(
      fov,
      this.container.clientWidth / this.container.clientHeight,
      near,
      far
    )
    window.camera = this.camera
    return this.camera
  }

  // 初始化渲染器
  initRenderer(antialias = true, alpha = true) {
    this.renderer = new THREE.WebGLRenderer({ 
      antialias,
      alpha
    })
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight)
    this.renderer.setPixelRatio(window.devicePixelRatio)
    this.renderer.setClearColor(0x000000, 0)
    this.container.appendChild(this.renderer.domElement)
    window.renderer = this.renderer
    return this.renderer
  }

  // 初始化控制器
  initControls() {
    const { OrbitControls } = require('three/examples/jsm/controls/OrbitControls')
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.enableDamping = true
    this.controls.dampingFactor = 0.05
    this.controls.rotateSpeed = 1.0
    this.controls.minDistance = 3
    this.controls.maxDistance = 10
    this.controls.minPolarAngle = Math.PI / 3
    this.controls.maxPolarAngle = Math.PI * 2/3
    this.controls.autoRotate = true
    this.controls.autoRotateSpeed = 0.4
    return this.controls
  }

  // 添加光源
  addLights() {
    const ambientLight = new THREE.AmbientLight(0xffffff, 2)
    this.scene.add(ambientLight)
    window.ambientLight = ambientLight
    return ambientLight
  }

  // 创建环境贴图
  createEnvMap() {
    const pmremGenerator = new THREE.PMREMGenerator(this.renderer)
    const envMapTexture = pmremGenerator.fromScene(new THREE.Scene()).texture
    pmremGenerator.dispose()
    return envMapTexture
  }

  // 窗口大小改变处理
  onWindowResize() {
    this.camera.aspect = this.container.clientWidth / this.container.clientHeight
    this.camera.updateProjectionMatrix()
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight)
  }

  // 清理资源
  dispose() {
    if (this.renderer) {
      this.renderer.dispose()
    }
    if (this.controls) {
      this.controls.dispose()
    }
  }
}

// 材质管理类
export class MaterialManager {
  // 创建标准材质
  static createStandardMaterial(options = {}) {
    return new THREE.MeshStandardMaterial({
      ...options,
      transparent: true,
      opacity: options.opacity || 1,
      side: options.side || THREE.DoubleSide,
    })
  }

  // 创建物理材质
  static createPhysicalMaterial(options = {}) {
    return new THREE.MeshPhysicalMaterial({
      ...options,
      transparent: true,
      opacity: options.opacity || 0.2,
      roughness: options.roughness || 0.8,
      metalness: options.metalness || 0.2,
      transmission: options.transmission || 0.9,
      thickness: options.thickness || 0.5,
      clearcoat: options.clearcoat || 1,
      clearcoatRoughness: options.clearcoatRoughness || 0.1,
      envMapIntensity: options.envMapIntensity || 1,
      side: options.side || THREE.DoubleSide,
    })
  }

  // 创建基础材质
  static createBasicMaterial(options = {}) {
    return new THREE.MeshBasicMaterial({
      ...options,
      transparent: true,
      opacity: options.opacity || 1,
      side: options.side || THREE.DoubleSide,
    })
  }
}

// 几何体管理类
export class GeometryManager {
  // 创建球体
  static createSphere(radius = 1, widthSegments = 64, heightSegments = 64) {
    return new THREE.SphereGeometry(radius, widthSegments, heightSegments)
  }

  // 创建环
  static createRing(innerRadius, outerRadius, thetaSegments = 64) {
    return new THREE.RingGeometry(innerRadius, outerRadius, thetaSegments)
  }
}

// 纹理管理类
export class TextureManager {
  // 创建渐变纹理
  static createGradientTexture(options = {}) {
    const canvas = document.createElement('canvas')
    canvas.width = options.width || 256
    canvas.height = options.height || 1
    const context = canvas.getContext('2d')
    
    const gradient = context.createLinearGradient(0, 0, canvas.width, 0)
    const colorStops = options.colorStops || [
      { offset: 0, color: 'rgba(0, 208, 255, 0)' },
      { offset: 0.4, color: 'rgba(0, 208, 255, 1)' },
      { offset: 0.5, color: 'rgba(0, 208, 255, 1)' },
      { offset: 0.6, color: 'rgba(0, 208, 255, 1)' },
      { offset: 1, color: 'rgba(0, 208, 255, 0)' }
    ]

    colorStops.forEach(stop => {
      gradient.addColorStop(stop.offset, stop.color)
    })
    
    context.fillStyle = gradient
    context.fillRect(0, 0, canvas.width, canvas.height)
    
    const texture = new THREE.CanvasTexture(canvas)
    texture.wrapS = THREE.RepeatWrapping
    texture.wrapT = THREE.RepeatWrapping
    return texture
  }

  // 加载纹理
  static loadTexture(url) {
    return new THREE.TextureLoader().load(url)
  }
} 