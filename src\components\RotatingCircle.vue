<template>
  <div class="rotating-circle-container">
    <div class="circle-content">
      <img src="../assets/images/jingying/1.png" class="center-image" alt="中心图" />
      <img src="../assets/images/jingying/2.png" class="rotate-left" alt="左旋转边框" />
      <img src="../assets/images/jingying/3.png" class="rotate-right" alt="右旋转边框" />
      <div class="slot-container">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
// 这个组件不需要特殊逻辑
</script>

<style scoped>
.rotating-circle-container {
  position: relative;
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circle-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.center-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
}

.rotate-left {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  animation: rotateLeft 8s linear infinite;
}

.rotate-right {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  animation: rotateRight 10s linear infinite;
}

.slot-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 4;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes rotateLeft {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes rotateRight {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 