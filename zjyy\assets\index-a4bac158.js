import{_ as a}from"./back-473409a1.js";import{_ as t,d as e,Q as l,s,W as i,X as c,o,h as n,e as r,i as d,z as v,F as u,N as m,j as p,k as f,l as g,M as y,Y as b,I as h,R as x,n as w,w as S,c as k,O as j,Z as z,$ as _,a as D,u as C,q as L,g as I,r as N}from"./vendor-22dca409.js";import{f as O,h as $,a as F,r as A}from"./index-8aea96a7.js";/* empty css                                                                       */const W=t({__name:"PieChart",props:{title:{type:String,default:"总量"},total:{type:Number,default:0},labelLength:{type:Number,default:120},numLength:{type:Number,default:30},data:{type:Array,default:()=>[]},center:{type:String,default:"23.2%"}},setup(a){const t=a,r=e(null),d=e(null);l((()=>{!function(){d.value?d.value.clear():d.value=i(c(r.value));let a={tooltip:{textStyle:{fontSize:20},formatter:a=>`${a.name}&nbsp;&nbsp;<strong>${a.value}个</strong>`},title:[{text:"{val|"+O(t.total)+"}\n{name|"+t.title+"}",top:"center",left:"22%",textAlign:"center",show:t.total,textStyle:{rich:{name:{width:120,fontSize:14,fontWeight:"300",padding:[10,0],color:"#fff"},val:{width:120,fontSize:32,color:"#fff",fontFamily:"Oswald Medium"}}}}],grid:{top:"0%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},legend:{orient:"horizontal",icon:"circle",top:"center",left:"48%",itemGap:20,data:t.data.map((a=>a.name)),itemHeight:10,itemWidth:20,formatter:t=>{var e;let l,s=a.series[0].data;for(let a=0;a<s.length;a++)s[a].name===t&&(l=s[a].value,null==(e=s[a].itemStyle)||e.color);return`{name|${t}}{value|${l}}{unit|个}`},textStyle:{rich:{name:{color:"#97acb4",fontSize:14,fontWeight:300,width:t.labelLength},value:{fontSize:20,fontFamily:"Oswald Medium",width:t.numLength,color:"#fff"},unit:{width:30,color:"#97acb4",fontWeight:300}}}},graphic:{elements:[{type:"image",z:1,style:{image:t.total?$("project/cir3.png"):$("project/cir2.png"),width:180,height:180},left:0,top:35}]},series:[{type:"pie",radius:["48%","58%"],center:[t.center,"50%"],startAngle:90,avoidLabelOverlap:!1,data:v(t.data),label:{show:!1},labelLine:{show:!1}},{type:"pie",radius:["40%","58%"],center:[t.center,"50%"],startAngle:90,clockwise:!0,silent:!0,itemStyle:{borderWidth:0},data:v(t.data,!0),label:{show:!1},labelLine:{show:!1}}]};d.value.setOption(a)}()})),s((()=>t.data),(a=>{l((()=>{if(d.value){let e={tooltip:{textStyle:{fontSize:20},formatter:a=>`${a.name}&nbsp;&nbsp;<strong>${a.value}个</strong>`},title:[{text:"{val|"+O(t.total)+"}\n{name|"+t.title+"}",top:"center",left:"22%",textAlign:"center",show:t.total,textStyle:{rich:{name:{width:120,fontSize:14,fontWeight:"300",padding:[10,0],color:"#fff"},val:{width:120,fontSize:32,color:"#fff",fontFamily:"Oswald Medium"}}}}],grid:{top:"0%",left:"0%",right:"0%",bottom:"0%",containLabel:!0},legend:{orient:"horizontal",icon:"circle",top:"center",left:"48%",itemGap:20,data:a.map((a=>a.name)),itemHeight:10,itemWidth:20,formatter:a=>{var t;let l,s=e.series[0].data;for(let e=0;e<s.length;e++)s[e].name===a&&(l=s[e].value,null==(t=s[e].itemStyle)||t.color);return`{name|${a}}{value|${l}}{unit|个}`},textStyle:{rich:{name:{color:"#97acb4",fontSize:14,fontWeight:300,width:t.labelLength},value:{fontSize:20,fontFamily:"Oswald Medium",width:t.numLength,color:"#fff"},unit:{width:30,color:"#97acb4",fontWeight:300}}}},graphic:{elements:[{type:"image",z:1,style:{image:t.total?$("project/cir3.png"):$("project/cir2.png"),width:180,height:180},left:0,top:35}]},series:[{type:"pie",radius:["48%","58%"],center:[t.center,"50%"],startAngle:90,avoidLabelOverlap:!1,data:v(a),label:{show:!1},labelLine:{show:!1}},{type:"pie",radius:["40%","58%"],center:[t.center,"50%"],startAngle:90,clockwise:!0,silent:!0,itemStyle:{borderWidth:0},data:v(a,!0),label:{show:!1},labelLine:{show:!1}}]};d.value.setOption(e)}}))}),{deep:!0});const v=(a,t=!1)=>{const e=[],l=.001*a.reduce(((a,t)=>a+t.value),0);return a.forEach((a=>{e.push({...a,itemStyle:{color:t?a.itemStyle.color.replace("1)","0.2)"):a.itemStyle.color}}),e.push({value:l,itemStyle:{color:"rgba(0, 0, 0, 0)"},label:{show:!1},labelLine:{show:!1}})})),e};return(a,t)=>(o(),n("div",{class:"PieChart",ref_key:"pieChartRef",ref:r},null,512))}},[["__scopeId","data-v-36651dfb"]]),M=t({__name:"BarChart",props:{color:{type:String,default:"0, 255, 136"},data:{type:Array,default:()=>[]},topColor:{type:String,default:"104,226,158"}},setup(a){const t=a,s=e(null),r=e(null);let d=["10月","11月","12月","1月","2月"],v=[80,87,51,81];l((()=>{v=t.data,function(){r.value?r.value.clear():r.value=i(c(s.value));let a={grid:{top:"80%",bottom:"90%"},xAxis:{data:d,axisTick:{show:!1},axisLine:{show:!0},axisLabel:{interval:0,textStyle:{color:"#beceff",fontSize:12}}},yAxis:[{type:"value",gridIndex:0,splitLine:{show:!0,lineStyle:{color:"#4D5359"}},axisTick:{show:!1},axisLine:{lineStyle:{color:"#0c3b71"}},axisLabel:{color:"rgb(170,170,170)",formatter:"{value}"}}],series:[{name:"",type:"pictorialBar",symbolSize:[12,8],symbolOffset:[14.8,-5],z:12,barGap:"0%",data:v.map((function(a){return{value:a,symbolPosition:"end",itemStyle:{normal:{color:"rgba("+t.topColor+", 1)"}}}}))},{name:"",type:"pictorialBar",symbolSize:[12,10],symbolOffset:[-1.5,6],z:12,barGap:"0%",data:v.map((function(a){return{value:a,itemStyle:{normal:{color:"rgba("+t.color+", 1)"}}}}))},{type:"bar",barWidth:12,z:10,barGap:"0%",data:v.map((function(a){return{value:a,label:{normal:{show:!0,formatter:"{c}",position:"top",textStyle:{color:"#fff",fontSize:12,fontWeight:"bold"}}},itemStyle:{normal:{color:{x:0,y:0,x2:0,y2:1,type:"linear",global:!1,colorStops:[{offset:0,color:"rgba("+t.color+", 1)"},{offset:1,color:"rgba("+t.color+", 1)"}]}}}}}))},{name:"",type:"pictorialBar",symbolSize:[18,12],symbolOffset:[-18,8],z:11,barGap:"0%",data:u.map((function(a){return{value:a,itemStyle:{normal:{color:"rgba("+t.color+", 0.6)"}}}}))},{type:"bar",barWidth:18,barGap:"-125%",z:-1,data:u.map((function(a){return{value:100,label:{normal:{show:!1}},itemStyle:{normal:{color:{x:1,y:1,x2:1,y2:0,type:"linear",global:!1,colorStops:[{offset:0,color:"rgba("+t.color+", 0.2)"},{offset:.5,color:"rgba("+t.color+", 0.15)"},{offset:.9,color:"rgba("+t.color+", 0.1)"},{offset:1,color:"rgba("+t.color+", 0)"}]}}}}}))}]};r.value.setOption(a)}()}));let u=new Array(v.length).fill(100);return(a,t)=>(o(),n("div",{class:"BarChart",ref_key:"barChartRef",ref:s},null,512))}},[["__scopeId","data-v-2eeaa745"]]),P={class:"left-content"},V={class:"left-item one"},B={class:"top"},T={class:"project-title"},E={class:"block ml-[-20px]"},R={class:"icon ml-[-20px]"},q=["src"],G={class:"info"},J={class:"title font-light text-[14px]"},U={class:"num"},X={class:"text-[#D1D3D5] text-[14px] font-light ml-[4px]"},H={class:"left-item two"},Q={class:"top"},Y={class:"block"},Z={class:"icon"},K=["src"],aa={class:"title font-light text-[14px]"},ta={class:"num"},ea={class:"text-[#D1D3D5] text-[14px] font-light ml-[10px]"},la={class:"three"},sa={class:"three-one"},ia={class:"block"},ca={class:"icon"},oa=["src"],na={class:"title"},ra={class:"num"},da={class:"text-[#D1D3D5] text-[14px] ml-[10px]"},va={class:"chart"},ua=t({__name:"item1",props:{projectData:Object},setup(a){const t=a,l=e("项目名称"),i=e(!!localStorage.getItem("isChina")),c=e([{id:1,title:"合同额",num:0,unit:"万美元",icon:$("project/icon1.png"),itemStyle:{color:"#a3d2eb"}},{id:2,title:"产值",num:0,unit:"万美元",icon:$("project/icon1.png"),itemStyle:{color:"#a3d2eb"}},{id:3,title:"总工期",num:0,unit:"天",icon:$("project/icon2.png"),itemStyle:{color:"#a3d2eb"}},{id:4,title:"开工日期",num:"",icon:$("project/icon3.png"),unit:"",itemStyle:{color:"#a3d2eb"}}]),y=e([{id:1,title:"施工进度",num:45.37,unit:"%",icon:$("project/icon4.png"),itemStyle:{color:"#a3d2eb"}},{id:2,title:"工程回款总额",num:1100,unit:"万美元",icon:$("project/icon5.png"),itemStyle:{color:"#a3d2eb"}}]),b=e([{id:1,title:"项目总人数",num:211,unit:"人",icon:$("project/icon6.png"),itemStyle:{color:"#a3d2eb"}}]);function h(a={}){const{min:t=80,max:e=100,length:l=5,unique:s=!1}=a;if("number"!=typeof t||"number"!=typeof e)throw new TypeError("min/max 必须是数字");if(t>e)throw new RangeError("最小值不能大于最大值");if(s&&e-t+1<l)throw new Error("唯一性要求下范围不足");const i=()=>Math.floor(Math.random()*(e-t+1))+t;if(s){const a=new Set;for(;a.size<l;)a.add(i());return[...a]}return Array.from({length:l},i)}e([{title:"占地20000平方米"},{title:"超大型商业综合体"},{title:"低能耗，低人力"},{title:"绿色建筑，低碳环保"},{title:"智能建筑体系"},{title:"绿色环保"}]),h(),h();const x=e([]);e([]),e(!0);const w=a=>{var t,e,s;if(a){if(l.value=a.项目名称,c.value[0].num=(null==(t=a.项目合同金额)?void 0:t.replace(/,/g,""))||0,c.value[1].num=(null==(e=a.项目累计产值)?void 0:e.replace(/,/g,""))||0,y.value[1].num=(null==(s=a.项目累收工程款)?void 0:s.replace(/,/g,""))||0,c.value[2].num=a.总工期||0,c.value[3].num=a.开工日期,c.value[0].unit=i.value?"万元":"万美元",c.value[1].unit=i.value?"万元":"万美元",y.value[1].unit=i.value?"万元":"万美元",i.value)if(a.项目累计产值&&a.项目合同金额){const t=a.项目累计产值.replace(/,/g,""),e=a.项目合同金额.replace(/,/g,"");y.value[0].num=(t/e*100).toFixed(2)}else y.value[0].num=0;else a.项目进度?y.value[0].num=parseFloat(a.项目进度.replace("%","")):y.value[0].num=0;if(i.value){const t=Number(a.项目正式员工人数||0)+Number(a.劳务派遣人数||0)+Number(a.其他用工形式人员||0)+Number(a.合作单位人员||0);b.value[0].num=t,x.value=[{name:"其他用工形式人员",value:a.其他用工形式人员||0,itemStyle:{color:"rgba(0, 255, 187, 1)"}},{name:"合作单位人员",value:a.合作单位人员||0,itemStyle:{color:"rgba(0, 255, 255, 1)"}},{name:"劳务派遣人数",value:a.劳务派遣人数||0,itemStyle:{color:"rgba(74, 144, 226, 1)"}},{name:"正式员工",value:a.项目正式员工人数||0,itemStyle:{color:"rgba(0, 216, 255, 1)"}}]}else b.value[0].num=Number(a.中方员工人数)+Number(a.外籍员工人数)||0,x.value=[{name:"中方员工人数",value:a.中方员工人数||0,itemStyle:{color:"rgba(0, 255, 187, 1)"}},{name:"外籍员工人数",value:a.外籍员工人数||0,itemStyle:{color:"rgba(0, 255, 255, 1)"}}]}};return r((()=>{w(t.projectData)})),s((()=>t.projectData),(a=>{w(a)})),(a,t)=>(o(),n("div",P,[d("div",V,[d("div",B,[d("div",T,v(l.value),1),d("div",E,[(o(!0),n(u,null,m(c.value,((a,t)=>(o(),n("div",{class:"item",key:t},[d("div",R,[d("img",{src:a.icon,alt:""},null,8,q)]),d("div",G,[d("div",J,v(a.title),1),d("div",U,[d("span",{class:"text-[20px] font-family-oswald-medium",style:p({color:a.itemStyle.color})},v(3==t?a.num:f(O)(a.num)),5),d("span",X,v(a.unit),1)])])])))),128))])])]),d("div",H,[d("div",Q,[t[0]||(t[0]=d("div",{class:"project-title mt-[20px]"},"项目管理",-1)),d("div",Y,[(o(!0),n(u,null,m(y.value,((a,t)=>(o(),n("div",{class:"item",key:t},[d("div",Z,[d("img",{src:a.icon,alt:""},null,8,K)]),d("div",aa,v(a.title),1),d("div",ta,[d("span",{class:"text-[24px] font-family-oswald-medium",style:p({color:a.itemStyle.color})},v(f(O)(a.num)),5),d("span",ea,v(a.unit),1)])])))),128))])])]),d("div",la,[d("div",sa,[t[1]||(t[1]=d("div",{class:"project-title mt-[20px]"},"项目人员",-1)),d("div",ia,[(o(!0),n(u,null,m(b.value,((a,t)=>(o(),n("div",{class:"item",key:t},[d("div",ca,[d("img",{src:a.icon,alt:""},null,8,oa)]),d("div",na,v(a.title),1),d("div",ra,[d("span",{class:"text-[24px] font-family-oswald-medium",style:p({color:a.itemStyle.color})},v(f(O)(a.num)),5),d("span",da,v(a.unit),1)])])))),128))]),d("div",va,[g(W,{data:x.value},null,8,["data"])])])])]))}},[["__scopeId","data-v-d3a3b8a9"]]),ma=t({__name:"CombinedChart",props:{barData:{type:Object,required:!0},lineData:{type:Object,required:!0},xData:{type:Array,default:()=>[]},width:{type:String,default:"100%"},height:{type:String,default:"100%"}},setup(a){const t=a,l=e(null);let i=null;const d=()=>{const a={backgroundColor:"transparent",tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!1},shadowStyle:{color:"rgba(255, 255, 255, 0.1)"}},backgroundColor:"rgba(0,0,0,0.3)",borderColor:"rgba(255, 255, 255, 0.2)",textStyle:{color:"#fff"}},grid:{top:"10%",left:"3%",right:"3%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:t.xData,axisLine:{lineStyle:{color:"rgba(255, 255, 255, 0.2)"}},axisTick:{show:!1},axisLabel:{color:"#fff",fontSize:12}},yAxis:[{type:"value",name:"数量",nameTextStyle:{color:"rgba(255, 255, 255, 0.6)",fontSize:12},splitLine:{lineStyle:{color:"rgba(255, 255, 255, 0.1)"}},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"rgba(255, 255, 255, 0.6)",fontSize:12}},{type:"value",name:"百分比(%)",nameTextStyle:{color:"rgba(255, 255, 255, 0.6)",fontSize:12},min:0,max:100,splitLine:{show:!1},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{color:"rgba(255, 255, 255, 0.6)",formatter:"{value}%",fontSize:12}}],series:[{name:t.barData.name,type:"bar",data:t.barData.data,barWidth:"30%",itemStyle:{color:new b(0,0,0,1,[{offset:0,color:"rgba(233,54,40, 0.9)"},{offset:1,color:"rgba(233,54,40, 0.5)"}])}},{name:t.lineData.name,type:"line",yAxisIndex:1,data:t.lineData.data,symbol:"circle",symbolSize:8,smooth:!0,itemStyle:{color:"rgb(0,152,101)"},lineStyle:{width:3,color:"rgb(0,152,101)"},areaStyle:{color:new b(0,0,0,1,[{offset:0,color:"rgba(0,152,101, 0.6)"},{offset:1,color:"rgba(0,152,101, 0.1)"}])}}]};i.setOption(a)};return s((()=>[t.barData,t.lineData,t.xData]),(()=>{d()}),{deep:!0}),r((()=>{l.value&&(i=c(l.value),d()),window.addEventListener("resize",(()=>null==i?void 0:i.resize()))})),y((()=>{null==i||i.dispose(),window.removeEventListener("resize",(()=>null==i?void 0:i.resize()))})),(a,t)=>(o(),n("div",{class:"chart-container",ref_key:"chartContainer",ref:l},null,512))}},[["__scopeId","data-v-961b9aa8"]]),pa={class:"left-content"},fa={class:"left-item one"},ga={class:"project-title"},ya={class:"status-section"},ba={class:"status-item"},ha={class:"status-item"},xa={class:"chart-section"},wa={class:"chart"},Sa={class:"status-section"},ka={class:"status-item"},ja={class:"status-item"},za={class:"chart-section"},_a={class:"chart"},Da=t({__name:"item2",props:{projectData:{type:Object,default:()=>({})}},setup(a){const t=a,l=e("安全隐患"),i=a=>{a&&(l.value=a.项目名称)};r((()=>{i(t.projectData)})),s((()=>t.projectData),(a=>{i(a)})),e([{id:1,title:"排查记录",num:76,icon:$("project/icon7.png")},{id:2,title:"隐患记录",num:6666,icon:$("project/icon8.png")}]),e([{value:1914,name:"已销项",itemStyle:{color:"rgba(0,255,170,1)"}},{value:1601,name:"待复查",itemStyle:{color:"rgba(0,255,255, 1)"}},{value:1537,name:"待整改",itemStyle:{color:"rgba(247,147,30, 1)"}},{value:1007,name:"超期",itemStyle:{color:"rgba(237,28,36, 1)"}}]),e([{value:56,name:"合格",itemStyle:{color:"rgba(0,255,170,1)"}},{value:79,name:"待核验",itemStyle:{color:"rgba(0,255,255, 1)"}},{value:49,name:"待复查",itemStyle:{color:"rgba(247,147,30, 1)"}},{value:27,name:"待整改",itemStyle:{color:"rgba(237,28,36, 1)"}}]),e(["折旧金额"]);const c=e(["10月","11月","12月","1月","2月"]);e([{name:"折旧金额",itemStyle:{color:"rgba(5,85,163, 1)"},data:[60,85,56,33,20]}]);const u=e({name:"问题数量",itemStyle:{color:"rgba(233,54,40, 1)"},data:[20,35,28,42,18]}),m=e({name:"整改率",itemStyle:{color:"rgba(0,152,101, 1)"},data:[45,60,75,55,68]});return(a,t)=>{const e=F;return o(),n("div",pa,[d("div",fa,[d("div",ga,v(l.value),1),t[4]||(t[4]=d("div",{class:"info-section"},[d("div",{class:"info-section-bg"},"安全隐患")],-1)),d("div",ya,[d("div",ba,[g(e,{name:"troubleshooting",color:"#fff",style:{width:"28px",height:"28px","margin-top":"4px"}}),t[0]||(t[0]=d("div",{class:"status-content"},[d("div",{class:"status-label"},[h("排查记录: "),d("span",{class:"recordNum"},"77"),d("span",{class:"unit"},"种")])],-1))]),d("div",ha,[g(e,{name:"hiddenDanger",color:"#fff",style:{width:"28px",height:"28px","margin-top":"4px"}}),t[1]||(t[1]=d("div",{class:"status-content"},[d("div",{class:"status-label"},[h("隐患记录: "),d("span",{class:"recordNumRed"},"8,999"),d("span",{class:"unit"},"种")])],-1))])]),t[5]||(t[5]=x('<div class="issue-section" data-v-6419c981><div class="issue-row" data-v-6419c981><div class="issue-item" data-v-6419c981><div class="issue-label" data-v-6419c981>已销项：</div><div class="issue-value" data-v-6419c981><span class="blueFont" data-v-6419c981>1914</span> 个</div></div><div class="issue-item" data-v-6419c981><div class="issue-label" data-v-6419c981>待复查：</div><div class="issue-value" data-v-6419c981><span class="yellowFont" data-v-6419c981>1601</span> 个</div></div></div><div class="divider-line" data-v-6419c981></div><div class="issue-row" data-v-6419c981><div class="issue-item" data-v-6419c981><div class="issue-label" data-v-6419c981>待整改</div><div class="issue-value" data-v-6419c981><span class="yellowFont" data-v-6419c981>1601</span>个</div></div><div class="issue-item" data-v-6419c981><div class="issue-label" data-v-6419c981>超期</div><div class="issue-value" data-v-6419c981><span class="redFont" data-v-6419c981>1601</span>个</div></div></div></div>',1)),d("div",xa,[d("div",wa,[g(M,{color:"255,213,0",topColor:"223,192,66"})])]),t[6]||(t[6]=d("div",{class:"info-section"},[d("div",{class:"info-section-bg"},"质量问题")],-1)),d("div",Sa,[d("div",ka,[g(e,{name:"correction",color:"#fff",style:{width:"28px",height:"28px","margin-top":"4px"}}),t[2]||(t[2]=d("div",{class:"status-content"},[d("div",{class:"status-label"},[h("整改率: "),d("span",{class:"recordNum",style:{"font-size":"20px"}},"77"),d("span",{class:"unit"},"%")])],-1))]),d("div",ja,[g(e,{name:"timelyRectification",color:"#fff",style:{width:"28px",height:"28px","margin-top":"4px"}}),t[3]||(t[3]=d("div",{class:"status-content"},[d("div",{class:"status-label"},[h("整改及时率: "),d("span",{class:"recordNum",style:{"font-size":"20px"}},"8,999"),d("span",{class:"unit"},"%")])],-1))])]),t[7]||(t[7]=x('<div class="issue-section" data-v-6419c981><div class="issue-bg" data-v-6419c981></div><div class="issue-row" data-v-6419c981><div class="issue-item" data-v-6419c981><div class="issue-label" data-v-6419c981>合格</div><div class="issue-value" data-v-6419c981><span class="blueFont" data-v-6419c981>56</span> 个</div></div><div class="issue-item" data-v-6419c981><div class="issue-label" data-v-6419c981>待核验</div><div class="issue-value" data-v-6419c981><span class="yellowFont" data-v-6419c981>79</span> 个</div></div></div><div class="divider-line" data-v-6419c981></div><div class="issue-row" data-v-6419c981><div class="issue-item" data-v-6419c981><div class="issue-label" data-v-6419c981>待复查</div><div class="issue-value" data-v-6419c981><span class="yellowFont" data-v-6419c981>49</span> 个</div></div><div class="issue-item" data-v-6419c981><div class="issue-label" data-v-6419c981>待整改</div><div class="issue-value" data-v-6419c981><span class="redFont" data-v-6419c981>27</span>个</div></div></div></div>',1)),d("div",za,[d("div",_a,[g(ma,{barData:u.value,lineData:m.value,xData:c.value},null,8,["barData","lineData","xData"])])])])])}}},[["__scopeId","data-v-6419c981"]]),Ca={class:"left-content"},La={class:"left-item one"},Ia={class:"project-title"},Na={class:"tabs"},Oa=["onClick"],$a={class:"tab-content"},Fa={key:0,class:"content-item"},Aa={class:"monitor-section"},Wa={class:"monitor-control"},Ma={class:"selector"},Pa={class:"control-icons"},Va={key:0,class:"video-player single-video"},Ba=["src"],Ta={key:1,class:"video-grid grid-4"},Ea=["onClick"],Ra=["src"],qa={key:2,class:"video-grid grid-9"},Ga=["onClick"],Ja=["src"],Ua={class:"dust-section"},Xa={class:"dust-data"},Ha={class:"dust-row"},Qa={class:"dust-value"},Ya={class:"dust-name"},Za={class:"dust-row",style:{"margin-top":"-20px"}},Ka={class:"dust-value"},at={class:"dust-name"},tt={key:1,class:"content-item"},et={key:2,class:"content-item"},lt={key:3,class:"content-item"},st=t({__name:"item3",props:{projectData:{type:Object,default:()=>({})}},setup(a){const t=a,l=e("项目名称"),i=e(0),c=e(["常规检测","塔吊监测","升降机监测","卸料平台"]),y=e(""),b=e([]),C=e(null),L=e(!!localStorage.getItem("isChina")),I=e([]);e(!0);const N=e(1),O=e(0),W=e([]),M=e([{name:"PM2.5",value:"35",unit:"μg/m³"},{name:"PM10",value:"68",unit:"μg/m³"},{name:"温度",value:"26.5",unit:"℃"},{name:"湿度",value:"65",unit:"%"},{name:"风速",value:"5.1",unit:"m/s"},{name:"风向",value:"东北",unit:""},{name:"噪音",value:"58",unit:"dB"},{name:"气压",value:"101.3",unit:"kPa"}]);e({total:12,running:8,stopped:4,params:{height:"10m",armLength:"10m",balanceArmLength:"10m"},safety:"正常"});const P=e(""),V=e(""),B=e([]);async function T(a,e){var l;L.value?D.get("https://vr.ztmapinfo.com/yydpdata.php",{params:{classify:"项目",project:a,type:e}}).then((a=>{if(a.data&&a.data.length>0)try{I.value=function(a,t={}){if("string"!=typeof a)throw new TypeError("路径参数必须是字符串类型");const{separator:e=";",prefix:l="https://vr.ztmapinfo.com/yydpdatamedia.php?path=",allowedExtensions:s=[".mp4",".webm"]}=t;return a.split(e).map((a=>a.trim())).filter((a=>{if(!a)return!1;const t=a.slice(a.lastIndexOf(".")).toLowerCase();return s.includes(t)})).map((a=>`${l}${encodeURIComponent(a)}`))}(a.data,{allowedExtensions:[".mp4",".webm"]}),I.value.length>0&&(P.value=I.value[0])}catch(t){}})).catch((a=>{})):(null==(l=t.projectData)?void 0:l.视频)&&async function(a){if(!a)return;try{const t=await A.get("/globalManage/zjmanage/largescreen/getToken");if(0===t.code&&t.data){const e=t.data.accessToken,l=t.data.userid,s=a.split(",").filter((a=>a.trim()));B.value=s.map((a=>`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${a.trim()}?access_token=${e}&userid=${l}`)),B.value.length>0&&(V.value=B.value[0])}}catch(t){}}(t.projectData.视频)}const E=a=>{N.value=a},R=a=>{O.value=a,N.value=1},q=()=>{C.value&&(document.fullscreenElement?document.exitFullscreen():C.value.requestFullscreen().catch((a=>{})))},G=a=>{a&&(l.value=a.项目名称||"项目名称")};return s([()=>t.projectData,()=>L.value],(([a,t])=>{G(a),(null==a?void 0:a.项目名称)&&T(a.项目名称,"视频")}),{immediate:!0}),r((()=>{G(t.projectData),W.value=["/src/assets/images/projectVideo/moni-1.mp4","/src/assets/images/projectVideo/moni-2.mp4","/src/assets/images/projectVideo/moni-3.mp4","/src/assets/images/projectVideo/moni-4.mp4","/src/assets/images/projectVideo/moni-5.mp4","/src/assets/images/projectVideo/moni-6.mp4","/src/assets/images/projectVideo/moni-7.mp4","/src/assets/images/projectVideo/moni-8.mp4","/src/assets/images/projectVideo/moni-9.mp4"]})),(a,t)=>{const e=z,s=_,r=F;return o(),n("div",Ca,[d("div",La,[d("div",Ia,v(l.value),1),d("div",Na,[(o(!0),n(u,null,m(c.value,((a,t)=>(o(),n("div",{key:t,class:w(["tab-item",{active:i.value===t}]),onClick:a=>i.value=t},v(a),11,Oa)))),128))]),d("div",$a,[0===i.value?(o(),n("div",Fa,[d("div",Aa,[t[4]||(t[4]=d("div",{class:"info-section"},[d("div",{class:"info-section-bg"},"视频监控")],-1)),d("div",Wa,[d("div",Ma,[g(s,{modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=a=>y.value=a),placeholder:"全部",class:"select"},{default:S((()=>[g(e,{value:"",label:"全部"},{default:S((()=>t[3]||(t[3]=[h("全部")]))),_:1}),(o(!0),n(u,null,m(b.value,(a=>(o(),k(e,{key:a.dictId,value:a.dictId,label:a.dictName},null,8,["value","label"])))),128))])),_:1},8,["modelValue"])]),d("div",Pa,[d("div",{class:"icon-item",onClick:q,style:p({background:`url(${f($)("map/controlBg.png")}) no-repeat`,backgroundSize:"100% 100%",display:"flex",justifyContent:"center",alignItems:"center"})},[g(r,{name:"1",color:"#fff",style:{width:"28px",height:"28px",display:"block"}})],4),d("div",{class:"icon-item",onClick:t[1]||(t[1]=a=>E(4)),style:p({background:`url(${f($)("map/controlBg.png")}) no-repeat`,backgroundSize:"100% 100%",display:"flex",justifyContent:"center",alignItems:"center"})},[g(r,{name:"4",color:"#fff",style:{width:"28px",height:"28px",display:"block"}})],4),d("div",{class:"icon-item",onClick:t[2]||(t[2]=a=>E(9)),style:p({background:`url(${f($)("map/controlBg.png")}) no-repeat`,backgroundSize:"100% 100%",display:"flex",justifyContent:"center",alignItems:"center"})},[g(r,{name:"9",color:"#fff",style:{width:"28px",height:"28px",display:"block"}})],4)])]),d("div",{class:"video-container",ref_key:"videoBox",ref:C},[1===N.value?(o(),n("div",Va,[d("video",{src:W.value[O.value],controls:"",autoplay:"",loop:""},null,8,Ba)])):j("",!0),4===N.value?(o(),n("div",Ta,[(o(!0),n(u,null,m(W.value.slice(0,4),((a,t)=>(o(),n("div",{key:t,class:"video-item",onClick:a=>R(t)},[d("video",{src:a,muted:"",loop:"",autoplay:""},null,8,Ra)],8,Ea)))),128))])):j("",!0),9===N.value?(o(),n("div",qa,[(o(!0),n(u,null,m(W.value.slice(0,9),((a,t)=>(o(),n("div",{key:t,class:"video-item",onClick:a=>R(t)},[d("video",{src:a,muted:"",loop:"",autoplay:""},null,8,Ja)],8,Ga)))),128))])):j("",!0)],512)]),d("div",Ua,[t[5]||(t[5]=d("div",{class:"info-section"},[d("div",{class:"info-section-bg"},"扬尘监控")],-1)),d("div",Xa,[d("div",Ha,[(o(!0),n(u,null,m(M.value.slice(0,4),((a,t)=>(o(),n("div",{class:"dust-item",key:t},[d("div",Qa,v(a.value)+v(a.unit),1),d("div",Ya,v(a.name),1)])))),128))]),d("div",Za,[(o(!0),n(u,null,m(M.value.slice(4,8),((a,t)=>(o(),n("div",{class:"dust-item",key:t},[d("div",Ka,v(a.value)+v(a.unit),1),d("div",at,v(a.name),1)])))),128))])]),t[6]||(t[6]=d("div",{class:"dust-chart-bg"},null,-1))])])):j("",!0),1===i.value?(o(),n("div",tt,t[7]||(t[7]=[x('<div class="tower-stats" data-v-f23147c9><div class="tower-stat-item-1" data-v-f23147c9><div class="stat-number" data-v-f23147c9>12 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>塔吊总数</div></div><div class="tower-stat-item-2" data-v-f23147c9><div class="stat-number" data-v-f23147c9>8 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>运行中</div></div><div class="tower-stat-item-3" data-v-f23147c9><div class="stat-number" data-v-f23147c9>4 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>已停机</div></div></div><div class="tower-details" data-v-f23147c9><div class="tower-params" data-v-f23147c9><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>塔身高度：</span>10m</div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>起重臂长：</span>10m</div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>平衡臂长：</span>10m</div></div><div class="tower-safety" data-v-f23147c9></div></div>',2)]))):j("",!0),2===i.value?(o(),n("div",et,t[8]||(t[8]=[x('<div class="tower-stats" data-v-f23147c9><div class="tower-stat-item-1" data-v-f23147c9><div class="stat-number" data-v-f23147c9>12 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>升降机总数</div></div><div class="tower-stat-item-2" data-v-f23147c9><div class="stat-number" data-v-f23147c9>8 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>在线总数</div></div><div class="tower-stat-item-3" data-v-f23147c9><div class="stat-number" data-v-f23147c9>4 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>今日预警</div></div><div class="tower-stat-item-4" data-v-f23147c9><div class="stat-number" data-v-f23147c9>4 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>今日报警次数</div></div></div><div class="tower-details" data-v-f23147c9><div class="tower-params" data-v-f23147c9><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>载重：</span>10m</div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>高度：</span>10m</div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>x倾角：</span> 0.5</div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>y倾角：</span>0.5</div></div><div class="tower-safety-lift" data-v-f23147c9></div></div>',2)]))):j("",!0),3===i.value?(o(),n("div",lt,t[9]||(t[9]=[x('<div class="tower-stats" data-v-f23147c9><div class="tower-stat-item-1" data-v-f23147c9><div class="stat-number" data-v-f23147c9>12 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>升降机总数</div></div><div class="tower-stat-item-2" data-v-f23147c9><div class="stat-number" data-v-f23147c9>8 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>在线总数</div></div><div class="tower-stat-item-5" data-v-f23147c9><div class="stat-number" data-v-f23147c9>4 <span class="stat-string" data-v-f23147c9>个</span></div><div class="stat-title" data-v-f23147c9>离线数</div></div></div><div class="tower-details" data-v-f23147c9><div class="tower-params" data-v-f23147c9><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>实时载重 ：</span> <span class="title-num" data-v-f23147c9>10</span>kg</div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>x倾角：</span><span class="title-num" data-v-f23147c9>0.5°</span></div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>y倾角：</span><span class="title-num" data-v-f23147c9>0.5°</span></div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>载重预警值：</span><span class="title-num" style="color:#FCD494;" data-v-f23147c9>10</span>m</div><div class="param-item" data-v-f23147c9><span class="title" data-v-f23147c9>载重报警值：</span> <span class="title-num" style="color:#FF6262;" data-v-f23147c9>10</span>m</div></div><div class="tower-safety-discharge" data-v-f23147c9></div></div>',2)]))):j("",!0)])])])}}},[["__scopeId","data-v-f23147c9"]]),it=""+new URL("mapImg-487e5822.png",import.meta.url).href,ct={class:"relative w-full h-full origin-[0_0] overflow-hidden bg-[#0a0f1a] bg-[right_bottom] z-[2]"},ot={class:"content relative"},nt={class:"absolute w-1/2 text-center text-[28px] font-family-youshebiaotihei tracking-[8px] title z-10 top-[120px] left-2/4 transform -translate-x-1/2"},rt={class:"btns"},dt=["onClick"],vt={class:"thumbnails-container"},ut=["src","onClick"],mt=t({__name:"index",setup(t){const l=C(),s=L(),i=e("");localStorage.getItem("clickProject");const c=e({});e(!!localStorage.getItem("isChina"));const f=e([{id:1,name:"项目概览"},{id:2,name:"质量安全"},{id:3,name:"设备设施"}]),y=e(0),b=a=>{y.value=a,localStorage.setItem("btnsActive",a)},x=e([]),z=e(0);async function _(){try{let t={},e={};try{e=JSON.parse(s.query.clickProject||"{}")}catch(a){}const l=e.code||"",o=e.isGw||"false",n=String(o).toLowerCase();let r;if(r="true"===n?await A.get("/globalManage/zjmanage/largescreen/getXmxxV2",{params:{id:l}}):await A.get("/globalManage/zjmanage/largescreen/getXmjbxx",{params:{id:l}}),0===r.code&&r.data&&r.data.length>0){const a=r.data[0];if("true"===n?(t={"项目名称":a.项目名称||"","项目造价":a.合同金额||"0","累计完成产值":a.产值||"0","累计收款":a.工程回款总额||"0","项目进度":a.施工进度?a.施工进度+"%":"0%","开工日期":a.开工日期||"","工期":a.总工期||"0","照片":a.项目照片||"","视频":a.项目视频||"","项目总人数":a.项目总人数||"0","中方":a.正式员工||"0","外籍":Number(a.劳务派遣||0)+Number(a.其他工人||0)+Number(a.合作单位人员||0)||"0","类型":"项目","国家":a.国家||""},i.value=a.国家||"",c.value=t):(t={"项目名称":a.项目名称||"","项目造价":a.合同金额||"0","累计完成产值":a.产值||"0","累计收款":a.工程回款总额||"0","项目进度":a.施工进度?a.施工进度+"%":"0%","开工日期":a.开工日期||"","工期":a.总工期||"0","照片":a.项目照片||"","视频":a.项目视频||"","项目总人数":a.项目总人数||"0","正式员工":a.正式员工||"0","劳务派遣":a.劳务派遣||"0","其他用工":a.其他工人||"0","合作单位":a.合作单位人员||"0","类型":"项目","省":a.省||"","市":a.市||"","区":a.区||"","项目经度":a.项目经度||"","项目纬度":a.项目纬度||""},i.value=a.省||"",c.value=t),a.项目照片){const t=await async function(a){if(!a)return[it];try{const t=await A.get("/globalManage/zjmanage/largescreen/getToken");if(0!==t.code||!t.data)return[it];const e=t.data,l="941981453197164545",s=a.split(",").filter((a=>a.trim())),i=[],c=Math.min(s.length,5);for(let a=0;a<c;a++){const t=s[a].trim();t&&i.push(`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${t}?access_token=${e}&userid=${l}`)}return i.length>0?i:[it]}catch(t){return[it]}}(a.项目照片);x.value=t}else x.value=[it];a.项目视频}else x.value=[it]}catch(t){x.value=[it]}}const D=I((()=>{const a=z.value;let t;try{if(x.value&&x.value.length>0){const e=a>=0&&a<x.value.length?a:0;t=x.value[e],"string"==typeof t&&t.startsWith("http")||(t=it)}else t=it}catch(e){t=it}return{backgroundImage:`url(${t})`,backgroundRepeat:"no-repeat",backgroundPosition:"center",backgroundSize:"100% 100%"}}));r((()=>{let a=localStorage.getItem("btnsActive");a&&(a=[0,1,2].includes(Number(a))?Number(a):0,b(a)),_()}));const O=()=>{localStorage.removeItem("clickProject");const a={level:"",title:""};if(s.query.clickProject)try{const t="true"===JSON.parse(s.query.clickProject||"{}").isGw;t?(a.level="country",a.title=c.value.国家||i.value):(a.level="district",a.title=c.value.区||""),a.title||t||(a.level="city",a.title=c.value.市||""),a.title||t||(a.level="province",a.title=c.value.省||"")}catch(t){}l.replace({path:"/project/country",query:{data:JSON.stringify(a)}})};return(t,e)=>{const l=N("divtitle");return o(),n("div",ct,[d("div",ot,[g(l,{class:"absolute w-1/2 text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title z-10 top-0 left-2/4 transform -translate-x-1/2"},{default:S((()=>[h(v(i.value),1)])),_:1}),d("div",nt,v(c.value.项目名称),1),e[0]||(e[0]=d("div",{class:"bg"},null,-1)),e[1]||(e[1]=d("div",{class:"leftBg"},null,-1)),e[2]||(e[2]=d("div",{class:"bottomBg"},null,-1)),d("div",rt,[(o(!0),n(u,null,m(f.value,((a,t)=>(o(),n("div",{class:w(y.value==t&&"active"),key:t,onClick:a=>b(t)},[d("span",null,v(a.name),1)],10,dt)))),128))]),d("div",vt,[(o(!0),n(u,null,m(x.value,((a,t)=>(o(),n("div",{key:t,class:w(["thumbnail",{active:z.value===t}])},[d("img",{src:a,alt:"缩略图",onClick:()=>((a,t)=>{const e=new window.Image;e.onload=()=>{z.value=a},e.src=t,e.complete&&(z.value=a)})(t,a)},null,8,ut)],2)))),128))]),d("div",{class:"map",style:p(D.value)},null,4),0==y.value?(o(),k(ua,{key:0,projectData:c.value},null,8,["projectData"])):j("",!0),1==y.value?(o(),k(Da,{key:1,projectData:c.value},null,8,["projectData"])):j("",!0),2==y.value?(o(),k(st,{key:2,projectData:c.value},null,8,["projectData"])):j("",!0),d("img",{class:"pointer-events-auto cursor-pointer absolute top-[20px] left-[100px]",src:a,alt:"",srcset:"",style:{"z-index":"3"},onClick:O})])])}}},[["__scopeId","data-v-6356349c"]]);export{mt as default};
