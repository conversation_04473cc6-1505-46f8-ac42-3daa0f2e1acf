<template>
  <div class="left-content">
    <!-- 第一行：项目标题 -->
    <div class="left-item one">
      <div class="project-title">
        {{ title }}
      </div>

      <!-- 第二行：选项卡 -->
      <div class="tabs">
        <div v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: activeTab === index }"
          @click="activeTab = index">
          {{ tab }}
        </div>
      </div>

      <!-- 第三行：内容区域 -->
      <div class="tab-content">
        <!-- 选项卡1：常规检测 -->
        <div v-if="activeTab === 0" class="content-item">
          <!-- 视频监控部分 -->
          <div class="monitor-section">
            <div class="info-section">
              <div class="info-section-bg">{{ t('projectDetail.equipmentFacilities.videoMonitoring') }}</div>
            </div>
            <div class="monitor-control">
              <div class="selector">
                <el-select v-model="cameraId" :placeholder="t('projectDetail.equipmentFacilities.dustMonitoring.all')"
                  class="select">
                  <el-option value="" :label="t('projectDetail.equipmentFacilities.dustMonitoring.all')">{{
                    t('projectDetail.equipmentFacilities.dustMonitoring.all') }}</el-option>
                  <el-option v-for="item in cameraList" :key="item.dictId" :value="item.dictId"
                    :label="item.dictName"></el-option>
                </el-select>
              </div>
              <div class="control-icons">
                <div class="icon-item" @click="changeLayout(1)"
                  :style="{ background: `url(${getImg('map/controlBg.png')}) no-repeat`, backgroundSize: '100% 100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }">
                  <svg-icon name="1" color="#fff" style="width: 28px; height: 28px; display: block;" />
                </div>
                <div class="icon-item" @click="changeLayout(4)"
                  :style="{ background: `url(${getImg('map/controlBg.png')}) no-repeat`, backgroundSize: '100% 100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }">
                  <svg-icon name="4" color="#fff" style="width: 28px; height: 28px; display: block;" />
                </div>
                <div class="icon-item" @click="changeLayout(9)"
                  :style="{ background: `url(${getImg('map/controlBg.png')}) no-repeat`, backgroundSize: '100% 100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }">
                  <svg-icon name="9" color="#fff" style="width: 28px; height: 28px; display: block;" />
                </div>
              </div>
            </div>
            <div class="video-container" ref="videoBox">
              <!-- 单视频模式 -->
              <div v-if="currentLayout === 1" class="video-player single-video">
                <video :src="localVideos[currentVideoIndex]" controls autoplay loop></video>
              </div>

              <!-- 四宫格模式 -->
              <div v-if="currentLayout === 4" class="video-grid grid-4">
                <div v-for="(video, index) in localVideos.slice(0, 4)" :key="index" class="video-item"
                  @click="playFullVideo(index)">
                  <video :src="video" muted loop autoplay></video>
                </div>
              </div>

              <!-- 九宫格模式 -->
              <div v-if="currentLayout === 9" class="video-grid grid-9">
                <div v-for="(video, index) in localVideos.slice(0, 9)" :key="index" class="video-item"
                  @click="playFullVideo(index)">
                  <video :src="video" muted loop autoplay></video>
                </div>
              </div>
            </div>
          </div>

          <!-- 扬尘监控部分 -->
          <div class="dust-section">
            <div class="info-section">
              <div class="info-section-bg">{{ t('projectDetail.equipmentFacilities.dustMonitoring.title') }}</div>
            </div>
            <div class="dust-data">
              <div class="dust-row">
                <div class="dust-item" v-for="(item, index) in dustData.slice(0, 4)" :key="index">
                  <div class="dust-value">{{ item.value }}{{ item.unit }}</div>
                  <div class="dust-name">{{ item.name }}</div>
                </div>
              </div>
              <div class="dust-row" style="margin-top: -20px;">
                <div class="dust-item" v-for="(item, index) in dustData.slice(4, 8)" :key="index">
                  <div class="dust-value">{{ item.value }}{{ item.unit }}</div>
                  <div class="dust-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
            <div class="dust-chart-bg"></div>
          </div>
        </div>

        <!-- 选项卡2：塔吊监测 -->
        <div v-if="activeTab === 1" class="content-item">
          <!-- 第一行：三个图标数据展示 -->
          <div class="tower-stats">
            <div class="tower-stat-item-1">
              <div class="stat-number">12 <span class="stat-string">{{
                t('projectDetail.equipmentFacilities.units.count') }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.towerCrane.totalCount') }}</div>
            </div>
            <div class="tower-stat-item-2">
              <div class="stat-number">8 <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
              }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.towerCrane.running') }}</div>
            </div>
            <div class="tower-stat-item-3">
              <div class="stat-number">4 <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
              }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.towerCrane.stopped') }}</div>
            </div>
          </div>

          <!-- 第二行：左右两个数据展示 -->
          <div class="tower-details">
            <!-- 左侧：塔吊参数 -->
            <div class="tower-params">
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.towerCrane.height')
                  }}：</span>10{{ t('projectDetail.equipmentFacilities.units.meter') }}</div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.towerCrane.jibLength')
                  }}：</span>10{{ t('projectDetail.equipmentFacilities.units.meter') }}</div>
              <div class="param-item"><span class="title">{{
                t('projectDetail.equipmentFacilities.towerCrane.counterJibLength') }}：</span>10{{
                    t('projectDetail.equipmentFacilities.units.meter') }}</div>
            </div>

            <!-- 右侧：安全数据 -->
            <div class="tower-safety">
              <!-- <div class="safety-item">
                <div class="safety-title">安全</div>
                <div class="safety-value">正常</div>
              </div> -->
            </div>
          </div>
        </div>

        <!-- 选项卡3：升降机监测 -->
        <div v-if="activeTab === 2" class="content-item">
          <!-- 第一行：三个图标数据展示 -->
          <div class="tower-stats">
            <div class="tower-stat-item-1">
              <div class="stat-number">12 <span class="stat-string">{{
                t('projectDetail.equipmentFacilities.units.count') }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.totalCount') }}</div>
            </div>
            <div class="tower-stat-item-2">
              <div class="stat-number">8 <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.onlineCount') }}</div>
            </div>
            <div class="tower-stat-item-3">
              <div class="stat-number">4 <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.todayWarnings') }}</div>
            </div>
            <div class="tower-stat-item-4">
              <div class="stat-number">4 <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.todayAlarms') }}</div>
            </div>
          </div>

          <!-- 第二行：左右两个数据展示 -->
          <div class="tower-details">
            <div class="tower-params">
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.elevator.weight')
                  }}：</span>10{{ t('projectDetail.equipmentFacilities.units.meter') }}</div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.elevator.height')
                  }}：</span>10{{ t('projectDetail.equipmentFacilities.units.meter') }}</div>
              <div class="param-item"> <span class="title">{{ t('projectDetail.equipmentFacilities.elevator.xTilt')
                  }}：</span> 0.5</div>
              <div class="param-item"> <span class="title">{{ t('projectDetail.equipmentFacilities.elevator.yTilt')
                  }}：</span>0.5</div>
            </div>

            <!-- 右侧：安全数据 -->
            <div class="tower-safety-lift">
              <!-- <div class="safety-item">
                <div class="safety-title">安全</div>
                <div class="safety-value">正常</div>
              </div> -->
            </div>
          </div>
        </div>

        <!-- 选项卡4：卸料平台 -->
        <div v-if="activeTab === 3" class="content-item">
          <!-- 第一行：三个图标数据展示 -->
          <div class="tower-stats">
            <div class="tower-stat-item-1">
              <div class="stat-number">12 <span class="stat-string">{{
                t('projectDetail.equipmentFacilities.units.count') }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.totalCount') }}</div>
            </div>
            <div class="tower-stat-item-2">
              <div class="stat-number">8 <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.onlineCount') }}</div>
            </div>
            <div class="tower-stat-item-5">
              <div class="stat-number">4 <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.offlineCount') }}</div>
            </div>
          </div>

          <!-- 第二行：左右两个数据展示 -->
          <div class="tower-details">
            <div class="tower-params">
              <div class="param-item"><span class="title">{{
                t('projectDetail.equipmentFacilities.unloading.realTimeWeight') }} ：</span> <span
                  class="title-num">10</span>{{ t('projectDetail.equipmentFacilities.units.kg') }}</div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.elevator.xTilt')
                  }}：</span><span class="title-num">0.5{{ t('projectDetail.equipmentFacilities.units.degree') }}</span>
              </div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.elevator.yTilt')
                  }}：</span><span class="title-num">0.5{{ t('projectDetail.equipmentFacilities.units.degree') }}</span>
              </div>
              <div class="param-item"><span class="title">{{
                t('projectDetail.equipmentFacilities.unloading.warningValue') }}：</span><span class="title-num"
                  style="color: #FCD494;">10</span>{{ t('projectDetail.equipmentFacilities.units.meter') }}</div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.unloading.alarmValue')
                  }}：</span> <span class="title-num" style="color: #FF6262;">10</span>{{
                    t('projectDetail.equipmentFacilities.units.meter') }}</div>
            </div>

            <!-- 右侧：安全数据 -->
            <div class="tower-safety-discharge">
              <!-- <div class="safety-item">
                <div class="safety-title">安全</div>
                <div class="safety-value">正常</div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, computed } from "vue";
import { useI18n } from 'vue-i18n';
import { getImg, formatNumber } from "@/utils/method";
import TitleBar from "@/components/TitleBar.vue";
import request from "@/utils/request";
import axios from "axios";

const { t, locale } = useI18n();

const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({}),
  },
});

const projectName = ref('');
const title = computed(() => projectName.value || t('projectDetail.overview.projectTitle'));
const activeTab = ref(0);
const tabs = computed(() => [
  t('projectDetail.equipmentFacilities.tabs.generalDetection'),
  t('projectDetail.equipmentFacilities.tabs.towerCraneMonitoring'),
  t('projectDetail.equipmentFacilities.tabs.elevatorMonitoring'),
  t('projectDetail.equipmentFacilities.tabs.unloadingPlatform')
]);
const cameraId = ref("");
const cameraList = ref([]);
const videoBox = ref(null);
const isChina = ref(localStorage.getItem("isChina") ? true : false);
const videos = ref([]);
const showVideo = ref(true);

// 视频布局相关
const currentLayout = ref(1); // 1表示单视频，4表示四宫格，9表示九宫格
const currentVideoIndex = ref(0);
const localVideos = ref([]);

// 扬尘数据
const dustData = computed(() => [
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pm25'), value: "35", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.microgram') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pm10'), value: "68", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.microgram') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.temperature'), value: "26.5", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.celsius') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.humidity'), value: "65", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.percent') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.windSpeed'), value: "5.1", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.meterPerSecond') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.windDirection'), value: t('projectDetail.equipmentFacilities.dustMonitoring.values.northeast'), unit: "" },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.noise'), value: "58", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.decibel') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pressure'), value: "101.3", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.kilopascal') }
]);

// 塔吊数据
const towerCraneData = ref({
  total: 12,
  running: 8,
  stopped: 4,
  params: {
    height: "10m",
    armLength: "10m",
    balanceArmLength: "10m"
  },
  safety: "正常"
});

// 视频相关的响应式变量
const currentVideo = ref('');
const currentInternationalVideo = ref('');
const internationalVideos = ref([]);

// 初始化本地视频数据
function initLocalVideos() {
  localVideos.value = [
    '/src/assets/images/projectVideo/moni-1.mp4',
    '/src/assets/images/projectVideo/moni-2.mp4',
    '/src/assets/images/projectVideo/moni-3.mp4',
    '/src/assets/images/projectVideo/moni-4.mp4',
    '/src/assets/images/projectVideo/moni-5.mp4',
    '/src/assets/images/projectVideo/moni-6.mp4',
    '/src/assets/images/projectVideo/moni-7.mp4',
    '/src/assets/images/projectVideo/moni-8.mp4',
    '/src/assets/images/projectVideo/moni-9.mp4'
  ];
}

// 生成国内视频URL的方法
function generateVideoUrls(pathStr, options = {}) {
  if (typeof pathStr !== "string") {
    throw new TypeError("路径参数必须是字符串类型");
  }

  const {
    separator = ";",
    prefix = "https://vr.ztmapinfo.com/yydpdatamedia.php?path=",
    allowedExtensions = [".mp4", ".webm"],
  } = options;

  return pathStr
    .split(separator)
    .map((path) => path.trim())
    .filter((path) => {
      if (!path) return false;
      const extension = path.slice(path.lastIndexOf(".")).toLowerCase();
      return allowedExtensions.includes(extension);
    })
    .map((path) => `${prefix}${encodeURIComponent(path)}`);
}

// 获取视频数据的方法
async function getVideos(project, type) {
  if (isChina.value) {
    // 国内视频获取逻辑 - 使用axios直接请求而非request
    axios.get("https://vr.ztmapinfo.com/yydpdata.php", {
      params: {
        classify: "项目",
        project: project,
        type: type,
      }
    })
      .then((res) => {
        if (res.data && res.data.length > 0) {
          try {
            videos.value = generateVideoUrls(res.data, {
              allowedExtensions: [".mp4", ".webm"],
            });
            if (videos.value.length > 0) {
              currentVideo.value = videos.value[0];
            }
          } catch (err) {
            console.error("视频URL生成失败:", err);
          }
        }
      })
      .catch((err) => {
        console.error("请求国内视频失败:", err);
      });
  } else {
    // 国际视频获取逻辑
    if (props.projectData?.视频) {
      getInternationalVideos(props.projectData.视频);
    }
  }
}

// 获取国际项目视频
async function getInternationalVideos(videoIds) {
  if (!videoIds) return;

  // 使用本地视频，不请求接口
  try {
    // 模拟国际项目视频数据
    internationalVideos.value = [
      '/src/assets/images/projectVideo/moni-1.mp4',
      '/src/assets/images/projectVideo/moni-2.mp4',
      '/src/assets/images/projectVideo/moni-3.mp4'
    ];

    if (internationalVideos.value.length > 0) {
      currentInternationalVideo.value = internationalVideos.value[0];
    }
  } catch (err) {
    console.error("获取国际项目视频失败:", err);
  }
}

// 切换视频的方法
function changeVideo(index) {
  if (isChina.value) {
    if (videos.value[index]) {
      currentVideo.value = videos.value[index];
    }
  } else {
    if (internationalVideos.value[index]) {
      currentInternationalVideo.value = internationalVideos.value[index];
    }
  }
}

// 切换布局
const changeLayout = (layout) => {
  currentLayout.value = layout;
};

// 播放单个视频
const playFullVideo = (index) => {
  currentVideoIndex.value = index;
  currentLayout.value = 1;
};

// 全屏
const toggleFullScreen = () => {
  if (videoBox.value) {
    if (!document.fullscreenElement) {
      videoBox.value.requestFullscreen().catch(err => {
        console.log(`全屏请求被拒绝：${err.message}`);
      });
    } else {
      document.exitFullscreen();
    }
  }
};

// 格式化项目数据
const formatProjectData = (data) => {
  if (data) {
    projectName.value = data.项目名称 || '';
  }
};

// 监听项目数据和区域变化
watch(
  [() => props.projectData, () => isChina.value],
  ([newData, newIsChina]) => {
    formatProjectData(newData);
    if (newData?.项目名称) {
      getVideos(newData.项目名称, "视频");
    }
  },
  { immediate: true }
);

// 监听语言变化
watch(locale, (newLocale) => {
  console.log('语言已切换到:', newLocale);
}, { immediate: true });

onMounted(() => {
  formatProjectData(props.projectData);
  initLocalVideos();
});
</script>

<style lang="scss" scoped>
.left-content {
  width: 860px;
  height: 100%;
  padding: 100px 20px 20px 60px;
  display: flex;
  flex-direction: column;
  position: relative;
  background: linear-gradient(to right, #0D121C 0%, #0f141f 70%, transparent 100%);

  .left-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    z-index: 2;
    margin-bottom: 10px;
  }

  .project-title {
    width: 400px;
    height: 60px;
    background: url("@/assets/images/map/title.png") no-repeat;
    background-size: 100% 100%;
    font-family: PangMenZhengDao, PangMenZhengDao;
    font-weight: 400;
    font-size: 20px;
    color: #F2F3FF;
    line-height: 24px;
    letter-spacing: 1px;
    text-shadow: 0px 0px 10px #0094FF;
    text-align: left;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    padding-left: 30px;
    margin-bottom: 20px;
  }

  .tabs {
    display: flex;
    position: relative;
    z-index: 2;
    width: 440px;
    flex-wrap: wrap;
    background: rgba(0, 102, 255, 0.3);
    border-radius: 49px 49px 49px 49px;
    border-image: linear-gradient(360deg, rgba(115, 173, 249, 1), rgba(115, 173, 249, 0)) 1 1;

    .tab-item {
      width: 110px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      color: #a3d2eb;
      cursor: pointer;

      &.active {
        color: #ffffff;
        background: radial-gradient(80% 80% at -18% 93%, #E5F5FF 0%, rgba(229, 245, 255, 0) 100%), rgba(0, 102, 255, 0.85);
        box-shadow: 0 2px 10px rgba(229, 245, 255, 0.3);
        border-radius: 41px 41px 41px 41px;
        border-image: linear-gradient(90deg, rgba(243, 247, 255, 0), rgba(243, 247, 255, 1), rgba(243, 247, 255, 0)) 1 1;
      }
    }
  }

  .tab-content {
    position: relative;
    z-index: 2;
    flex: 1;
    width: 420px;

    .content-item {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      color: #a3d2eb;
    }

    .monitor-section,
    .dust-section {
      width: 420px;
    }

    .monitor-control {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      width: 420px;

      .selector {
        width: 180px;
      }

      .control-icons {
        display: flex;
        gap: 15px;

        .icon-item {
          width: 40px;
          height: 40px;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          &.control-icon {
            background: url("../../../assets/images/map/controlBg.png") no-repeat;
            background-size: 100% 100%;
          }

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .video-container {
      width: 420px;
      height: 400px;
      background: #0c1018;
      border: 1px solid #005CD3;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;

      .video-player {
        width: 100%;
        height: 100%;

        video {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .video-placeholder {
        color: #a3d2eb;
        font-size: 20px;
        text-align: center;
      }

      .video-grid {
        display: grid;
        width: 100%;
        height: 100%;
        gap: 2px;
        /* 视频间的间隙，可根据需要调整或移除 */

        &.grid-4 {
          grid-template-columns: repeat(2, 1fr);
          grid-template-rows: repeat(2, 1fr);
        }

        &.grid-9 {
          grid-template-columns: repeat(3, 1fr);
          grid-template-rows: repeat(3, 1fr);
        }

        .video-item {
          width: 100%;
          height: 100%;
          background-color: #000;
          /* 视频未加载或保持宽高比时的背景色 */
          overflow: hidden;
          /* 确保视频内容不会溢出 */
          position: relative;
          /* 用于未来可能的控件或覆盖层定位 */

          video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            /* 填充并裁剪视频以适应项目，或使用 'contain' 以完整显示视频（可能带黑边） */
            display: block;
            /* 移除行内元素的额外底部空间 */
          }
        }
      }
    }

    .dust-data {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-bottom: 15px;
      width: 420px;
      height: 260px;
      background: url("@/assets/images/map/fourBg.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;

      .dust-row {
        display: flex;
        justify-content: space-between;

        .dust-item {
          width: 100px;
          height: 100px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .dust-value {
            height: 30px;
            width: 100%;
            font-size: 22px;
            line-height: 0px;
            color: #a3d2eb;
            text-align: center;
            font-size: 22px;
            color: #a3d2eb;
            font-family: 'oswald-medium', sans-serif;
            margin-bottom: 5px;
            background: url("@/assets/images/map/base.png") no-repeat;
            background-size: 100% 100%;
          }

          .dust-name {
            font-family: PangMenZhengDao, PangMenZhengDao;
            font-weight: 400;
            font-size: 18px;
            color: #F3F7FF;
            line-height: 21px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
        }
      }
    }

    .dust-chart-bg {
      width: 420px;
      height: 100px;
      background: url("@/assets/images/map/fourBg.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    // 塔吊监测样式
    .tower-stats {
      width: 480px;
      height: 120px;
      display: flex;
      justify-content: center;
      padding: 0 20px;
      margin-top: 120px;
      margin-bottom: 30px;
      background: url("@/assets/images/map/towerCraneBase.png") no-repeat;
      background-size: 100% 100%;

      .tower-stat-item-1 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/total.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;
        margin-right: 20px;

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }

      .tower-stat-item-2 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/online.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;
        margin-right: 20px;

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }

      .tower-stat-item-3 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/earlyWarning.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;
        margin-right: 20px;

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }

      .tower-stat-item-4 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/warning.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }

      .tower-stat-item-5 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/offline.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;
        margin-right: 20px;

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }
    }

    .tower-details {
      width: 480px;
      display: flex;
      justify-content: space-between;
      gap: 20px;

      .tower-safety {
        flex: 1;
        background: url("@/assets/images/map/towerCranes.png") no-repeat;
        background-size: 100% 100%;
        padding: 20px;
        height: 260px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .tower-safety-lift {
        flex: 1;
        background: url("@/assets/images/map/towerCrane.png") no-repeat;
        background-size: 100% 100%;
        padding: 20px;
        height: 260px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .tower-safety-discharge {
        flex: 1;
        background: url("@/assets/images/map/discharge.png") no-repeat;
        background-size: 100% 100%;
        padding: 20px;
        height: 260px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .tower-params {
        flex: 1;
        background: url("@/assets/images/map/towerBg.png") no-repeat;
        background-size: 100% 100%;
        padding: 20px;
        height: 260px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 100px;
      }

      .tower-params {
        .param-item {
          font-family: PangMenZhengDao, PangMenZhengDao;
          font-size: 16px;
          color: #a3d2eb;
          line-height: 36px;
          letter-spacing: 1px;

          span {
            font-family: Alibaba PuHuiTi !important;
            font-weight: normal;
            font-size: 16px;
            color: #DBE9FF;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }

          .title-num {
            font-family: TCloudNumber, TCloudNumber;
            font-weight: 400;
            font-size: 20px;
            color: #79BFFF;
            line-height: 28px;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      .tower-safety {
        .safety-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .safety-title {
            font-family: PangMenZhengDao, PangMenZhengDao;
            font-size: 18px;
            color: #F3F7FF;
            margin-bottom: 15px;
          }

          .safety-value {
            font-family: 'oswald-medium', sans-serif;
            font-size: 24px;
            color: #00FF00;
          }
        }
      }
    }
  }

  .info-section {
    width: 400px;
    height: 35px;
    margin: 20px 0;

    .info-section-bg {
      width: 400px;
      height: 40px;
      background: url("@/assets/images/map/projectInfo.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      padding-left: 40px;
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-size: 20px;
      color: #FFFFFF;
      line-height: 23px;
      letter-spacing: 2px;
      text-align: justified;
      font-style: normal;
      text-transform: none;
    }
  }
}

// 全局样式调整
:deep(.el-select .el-input__inner) {
  background-color: rgba(12, 16, 24, 0.7);
  border: 1px solid rgba(163, 210, 235, 0.5);
  color: #a3d2eb;
}

:deep(.el-select .el-select__popper) {
  background-color: rgba(12, 16, 24, 0.9);
  border: 1px solid rgba(163, 210, 235, 0.5);
}

:deep(.el-select-dropdown__item) {
  color: #a3d2eb;

  &.selected,
  &.hover {
    background-color: rgba(163, 210, 235, 0.2);
  }
}

.no-video-tip {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #a3d2eb;
  font-size: 16px;
}
</style>