import{_ as a,u as e,q as t,d as l,g as i,e as s,o as n,h as c,i as o,z as r,j as d,O as u,l as v,F as g,N as p,w as m,n as f,m as h,v as y,a0 as b}from"./vendor-22dca409.js";import{h as k,r as x,i as w}from"./index-8aea96a7.js";import{_}from"./back-473409a1.js";const j={class:"relative w-full h-full origin-[0_0] overflow-hidden bg-[url('@/assets/images/layout/bg.png')] bg-no-repeat bg-[size:100%_100%] bg-[right_bottom] z-[2] relative"},z={class:"content relative"},$={class:"absolute w-1/2 text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title z-10 top-0 left-2/4 transform -translate-x-1/2"},C={key:1,class:"map"},M=["src"],B={class:"left"},q={class:"container"},I={class:"icon"},N=["src"],O={class:"title"},P={class:"text"},S={style:{height:"100%"}},T={class:"btns"},F=["onClick"],J={class:"imgs"},R=["onClick"],V=["src"],Y={class:"imgs"},A=["onClick"],D=["src"],E=a({__name:"index",setup(a){const E=e(),G=t(),H=l([{id:1,name:"xxxx营地名称",icon:k("yingdi/162.png")},{id:2,name:"xxxx营地地点",icon:k("yingdi/161.png")}]),K=i((()=>ta.value[la.value])),L=l([{id:1,name:"营地视频"},{id:2,name:"营地照片"}]),Q=l(""),U=l(1),W=()=>{E.replace("/project/country")},X=l(""),Z=l(""),aa=l([]),ea=l(0),ta=l([]),la=l(0);async function ia(){try{let e={};try{e=JSON.parse(G.query.clickProject||"{}")}catch(a){}const t=e.code||"";if(!t)return;const l=await x.get("/globalManage/zjmanage/largescreen/getYdxxV2",{params:{id:t}});if(0===l.code&&l.data&&l.data.length>0){const a=l.data[0];X.value=a.营地名称||"",Z.value=a.营地名称||"",Q.value=a.营地介绍||"",H.value=[{id:1,name:a.营地名称||"暂无名称",icon:k("yingdi/162.png")},{id:2,name:`${a.所属国家||""} ${a.所属大洲||""}`,icon:k("yingdi/161.png")}],a.营地照片&&await async function(a){if(a)try{const e=await x.get("/globalManage/zjmanage/largescreen/getToken");if(0!==e.code||!e.data)return;const t=e.data,l="941981453197164545",i=a.split(",").filter((a=>a.trim())),s=[],n=Math.min(i.length,5);for(let a=0;a<n;a++){const e=i[a].trim();e&&s.push(`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${e}?access_token=${t}&userid=${l}`)}s.length>0&&(aa.value=s)}catch(e){}}(a.营地照片),a.营地视频&&await async function(a){if(a)try{const e=await x.get("/globalManage/zjmanage/largescreen/getToken");if(0!==e.code||!e.data)return;const t=e.data,l="941981453197164545",i=a.split(",").filter((a=>a.trim())),s=[];for(let a=0;a<i.length;a++){const e=i[a].trim();e&&s.push(`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${e}?access_token=${t}&userid=${l}`)}s.length>0&&(ta.value=s)}catch(e){}}(a.营地视频)}}catch(e){}}const sa=i((()=>({backgroundImage:`url(${aa.value[ea.value]})`,backgroundRepeat:"no-repeat",backgroundPosition:"center",backgroundSize:"100% 100%"})));return s((()=>{ia()})),(a,e)=>{const t=w,l=b;return n(),c("div",j,[o("div",z,[o("div",$,r(X.value),1),e[0]||(e[0]=o("div",{class:"bg"},null,-1)),e[1]||(e[1]=o("div",{class:"topBg"},null,-1)),e[2]||(e[2]=o("div",{class:"leftBg"},null,-1)),e[3]||(e[3]=o("div",{class:"bottomBg"},null,-1)),1==U.value?(n(),c("div",{key:0,class:"map",style:d(sa.value)},null,4)):u("",!0),0==U.value?(n(),c("div",C,[o("video",{src:K.value,controls:"",autoplay:"",muted:"",style:{width:"100%",height:"100%","object-fit":"contain"}},null,8,M)])):u("",!0),o("div",B,[v(t,{title:"营地概览",style:{"z-index":"3"}}),o("div",q,[(n(!0),c(g,null,p(H.value,((a,e)=>(n(),c("div",{class:"bar",key:e},[o("div",I,[o("img",{src:a.icon,alt:""},null,8,N)]),o("div",O,r(a.name),1)])))),128)),o("div",P,[v(l,{height:"100%"},{default:m((()=>[o("div",S,r(Q.value),1)])),_:1})])])]),o("div",{class:f(["bottom",1==U.value&&"active"])},[o("div",T,[(n(!0),c(g,null,p(L.value,((a,e)=>(n(),c("div",{class:"btn",key:e,onClick:a=>{return t=e,void(U.value=t);var t}},r(a.name),9,F)))),128))]),h(o("div",J,[(n(!0),c(g,null,p(aa.value,((a,e)=>(n(),c("div",{class:f(["img",e==ea.value&&"active"]),key:e,onClick:a=>{return t=e,void(ea.value=t);var t}},[o("img",{src:a,alt:""},null,8,V)],10,R)))),128))],512),[[y,1==U.value]]),h(o("div",Y,[(n(!0),c(g,null,p(ta.value,((a,e)=>(n(),c("div",{class:f(["img",e==la.value&&"active"]),key:e,onClick:a=>{return t=e,void(la.value=t);var t}},[o("video",{src:a,controls:"",style:{width:"100%",height:"100%","object-fit":"contain"}},null,8,D)],10,A)))),128))],512),[[y,0==U.value]])],2),o("img",{class:"pointer-events-auto cursor-pointer absolute top-[30px] left-[60px]",src:_,alt:"",srcset:"",onClick:W,style:{"z-index":"3"}})])])}}},[["__scopeId","data-v-d12550ef"]]);export{E as default};
