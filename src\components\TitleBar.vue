<template>
  <div
    class="zj-leftTitle"
    :style="{
      backgroundImage: inline
        ? 'url(' + getImg('project/leftBar2.png') + ')'
        : 'url(' + getImg('project/leftBar1.png') + ')',
    }"
  >
    <div class="title-left">
      <span
        class="font-color text-[20px] font-family-pangmenzhengdao"
        :text="title"
        >{{ title }}</span
      >
      <span class="enName">{{ EnTitle }}</span>
    </div>
    <slot></slot>
  </div>
</template>
<script setup>
  import { toRefs } from "vue";
  import { getImg } from "@/utils/method";
  const props = defineProps({
    title: {
      type: String,
      required: true,
    },
    EnTitle: {
      type: String,
    },
    inline: {
      type: Boolean,
      default: false,
    },
  });
  const { title, EnTitle, inline } = toRefs(props);
</script>

<style lang="scss" scoped>
  .zj-leftTitle {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 27px;
    line-height: 17px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-sizing: border-box;
    padding-left: 35px;
    padding-right: 15px;
    white-space: nowrap;
  }
  .first-title-text {
    font-size: 28px !important;
  }
</style>
