<template>
    <div class="yibuyiping-container">
        <!-- 背景图片作为容器背景 -->
        <div v-for="(point, index) in points" :key="index" class="clickable-point"
            :style="{ left: point.x + '%', top: point.y + '%' }" @click="point.clickHandler">
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 点位1的点击方法
const handlePoint1Click = () => {
    console.log('点击了位置1');
    // 这里可以执行位置1特定的逻辑
    // router.push({ name: 'yibuyipingDetail', params: { id: 1, type: 'special1' } });
};

// 点位2的点击方法
const handlePoint2Click = () => {
    console.log('点击了位置2');
    // 这里可以执行位置2特定的逻辑
    // router.push({ name: 'yibuyipingDetail', params: { id: 2, feature: 'advanced' } });
};

// 点位3的点击方法
const handlePoint3Click = () => {
    console.log('点击了位置3');
    // 这里可以弹出特定对话框或执行其他操作
    // alert('点击了位置3，这里可以显示特定内容');
};

// 点位4的点击方法
const handlePoint4Click = () => {
    console.log('点击了位置4');
    // 打开新页面或执行其他操作
    // window.open('/special-page-4', '_blank');
};

// 点位5的点击方法
const handlePoint5Click = () => {
    console.log('点击了位置5');
    // 跳转到位置5的专用详情页面
    router.push({ name: 'yibuyipingGuonei' });
};

// 点位6的点击方法
const handlePoint6Click = () => {
    console.log('点击了位置6');
    // 跳转到位置6的专用国际事业部页面
    router.push({ name: 'yibuyipingGuoji' });
};

// 点位7的点击方法
const handlePoint7Click = () => {
    console.log('点击了位置7');
    // 加载特定资源或执行其他操作
    // router.push({ name: 'resources', query: { type: 'documents' } });
};

// 点位8的点击方法
const handlePoint8Click = () => {
    console.log('点击了位置8');
    // 跳转到特定锚点或执行其他操作
    // router.push({ path: '/reports#annual' });
};

// 点位9的点击方法
const handlePoint9Click = () => {
    console.log('点击了位置9');
    // 执行特定业务逻辑
    // router.push({ name: 'analysis', params: { mode: 'advanced' } });
};

// 点位10的点击方法
const handlePoint10Click = () => {
    console.log('点击了位置10');
    router.push({ name: 'yibuyipingDetail' });
};

// 定义十个点位的相对位置和对应的点击处理函数
const points = ref([
    { id: 1, x: 8, y: 50, label: '位置1', clickHandler: handlePoint1Click },
    { id: 2, x: 17, y: 50, label: '位置2', clickHandler: handlePoint2Click },
    { id: 3, x: 26, y: 50, label: '位置3', clickHandler: handlePoint3Click },
    { id: 4, x: 36, y: 50, label: '位置4', clickHandler: handlePoint4Click },
    { id: 5, x: 45, y: 50, label: '位置5', clickHandler: handlePoint5Click },
    { id: 6, x: 55, y: 50, label: '位置6', clickHandler: handlePoint6Click },
    { id: 7, x: 64, y: 50, label: '位置7', clickHandler: handlePoint7Click },
    { id: 8, x: 74, y: 50, label: '位置8', clickHandler: handlePoint8Click },
    { id: 9, x: 83, y: 50, label: '位置9', clickHandler: handlePoint9Click },
    { id: 10, x: 93, y: 50, label: '位置10', clickHandler: handlePoint10Click },
]);

// 旧的函数，保留以备需要
const goToDetail = () => {
    console.log('goToDetail');
    router.push({ name: 'yibuyipingDetail' });
};
</script>

<style scoped lang="scss">
.yibuyiping-container {
    pointer-events: all;
    width: 100%;
    height: 100%;
    background-image: url('@/assets/images/yibuyiping/yibuyiping.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    position: absolute;
    top: 6px;
    cursor: default;
}

.clickable-point {
    width: 180px;
    height: 70px;
    position: absolute;
    transform: translate(-50%, -50%);
    /* 确保点位中心与坐标点对齐 */
    cursor: pointer;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.2s ease;

    &:hover {
        transform: translate(-50%, -50%) scale(1.2);

        .point-label {
            opacity: 1;
        }
    }
}

.point-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    border: 2px solid #1890ff;
    box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
}

.point-label {
    margin-top: 5px;
    padding: 2px 6px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.2s ease;
    white-space: nowrap;
}
</style>