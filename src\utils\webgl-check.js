// WebGL 检测和错误处理工具
export const webglUtils = {
    // 检测WebGL支持
    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            const webgl2 = canvas.getContext('webgl2');

            const support = {
                webgl: !!gl,
                webgl2: !!webgl2,
                maxTextureSize: gl ? gl.getParameter(gl.MAX_TEXTURE_SIZE) : 0,
                maxViewportDims: gl ? gl.getParameter(gl.MAX_VIEWPORT_DIMS) : [],
                version: gl ? gl.getParameter(gl.VERSION) : null,
                vendor: gl ? gl.getParameter(gl.VENDOR) : null,
                renderer: gl ? gl.getParameter(gl.RENDERER) : null
            };

            console.log('WebGL支持信息:', support);
            return support;
        } catch (error) {
            console.error('WebGL检测失败:', error);
            return { webgl: false, webgl2: false, error: error.message };
        }
    },

    // 检测设备性能
    checkDeviceCapabilities() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl');

        if (!gl) {
            return { supported: false };
        }

        try {
            const capabilities = {
                maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
                maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS),
                maxVertexAttribs: gl.getParameter(gl.MAX_VERTEX_ATTRIBS),
                maxVertexUniformVectors: gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS),
                maxFragmentUniformVectors: gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS),
                maxVaryingVectors: gl.getParameter(gl.MAX_VARYING_VECTORS),
                aliasedPointSizeRange: gl.getParameter(gl.ALIASED_POINT_SIZE_RANGE),
                aliasedLineWidthRange: gl.getParameter(gl.ALIASED_LINE_WIDTH_RANGE)
            };

            // 判断是否为低性能设备
            const isLowPerformance = capabilities.maxTextureSize < 2048 ||
                capabilities.maxViewportDims[0] < 2048;

            return { ...capabilities, isLowPerformance, supported: true };
        } catch (error) {
            console.error('设备能力检测失败:', error);
            return { supported: false, error: error.message };
        }
    },

    // 创建安全的WebGL上下文
    createSafeWebGLContext(canvas, options = {}) {
        const defaultOptions = {
            alpha: false,
            antialias: false,
            depth: true,
            failIfMajorPerformanceCaveat: false,
            powerPreference: 'default',
            premultipliedAlpha: true,
            preserveDrawingBuffer: false,
            stencil: false,
            ...options
        };

        try {
            return canvas.getContext('webgl', defaultOptions) ||
                canvas.getContext('experimental-webgl', defaultOptions);
        } catch (error) {
            console.error('WebGL上下文创建失败:', error);
            return null;
        }
    },

    // WebGL错误处理
    handleWebGLError(gl, error) {
        const errorMessages = {
            [gl.INVALID_ENUM]: 'INVALID_ENUM: 无效的枚举值',
            [gl.INVALID_VALUE]: 'INVALID_VALUE: 无效的数值',
            [gl.INVALID_OPERATION]: 'INVALID_OPERATION: 无效的操作',
            [gl.OUT_OF_MEMORY]: 'OUT_OF_MEMORY: 内存不足',
            [gl.CONTEXT_LOST_WEBGL]: 'CONTEXT_LOST_WEBGL: WebGL上下文丢失'
        };

        const message = errorMessages[error] || `未知WebGL错误: ${error}`;
        console.error('WebGL错误:', message);
        return message;
    }
};

// ECharts GL 安全配置
export const getEChartsGLSafeOptions = () => {
    const webglSupport = webglUtils.checkWebGLSupport();
    const deviceCaps = webglUtils.checkDeviceCapabilities();

    if (!webglSupport.webgl) {
        console.warn('WebGL不支持，将使用2D渲染');
        return { use3D: false };
    }

    // 根据设备性能调整配置
    const safeOptions = {
        use3D: true,
        // 降低质量以提高兼容性
        postEffect: {
            enable: !deviceCaps.isLowPerformance,
            SSAO: {
                enable: false, // 禁用SSAO以避免纹理问题
                radius: deviceCaps.isLowPerformance ? 6 : 12,
                intensity: deviceCaps.isLowPerformance ? 0.8 : 1.5
            },
            bloom: {
                enable: !deviceCaps.isLowPerformance,
                bloomIntensity: 0.1
            }
        },
        temporalSuperSampling: {
            enable: false // 禁用时间超采样
        },
        // 降低网格密度
        grid3D: {
            boxWidth: deviceCaps.isLowPerformance ? 200 : 260,
            boxHeight: deviceCaps.isLowPerformance ? 40 : 60
        }
    };

    console.log('ECharts GL 安全配置:', safeOptions);
    return safeOptions;
}; 