<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 225 126" style="enable-background:new 0 0 225 126;" xml:space="preserve">
<style type="text/css">
	.st0{fill-rule:evenodd;clip-rule:evenodd;fill:#F5F5F5;}
	.st1{filter:url(#Adobe_OpacityMaskFilter);}
	.st2{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st3{mask:url(#mask-2_1_);}
	.st4{fill:#BFBFBF;}
	.st5{filter:url(#Adobe_OpacityMaskFilter_1_);}
	.st6{mask:url(#mask-2_2_);}
</style>
<title>加载失败@3x</title>
<rect x="0.7" class="st0" width="224" height="126"/>
<defs>
	<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="95.1" y="48.4" width="18.7" height="29.2">
		<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
	</filter>
</defs>
<mask maskUnits="userSpaceOnUse" x="95.1" y="48.4" width="18.7" height="29.2" id="mask-2_1_">
	<g class="st1">
		<rect id="path-1_1_" x="0.7" class="st2" width="224" height="126"/>
	</g>
</mask>
<g class="st3">
	<g id="形状结合" transform="translate(94.326510, 48.000000)">
		<path class="st4" d="M16.7,0.4c0.1,0,0.2,0,0.2,0l-0.6,2.5L5,2.9c-0.9,0-1.7,0.7-1.7,1.6l0,0.1v12.5l6.4-4.5c1-0.7,2.3-0.7,3.3,0
			l0.2,0.1l6.4,5l-0.8,2.6l-7.1-5.6c-0.1-0.1-0.3-0.1-0.4-0.1l-0.1,0l-7.8,5.5v5.2c0,0.9,0.7,1.6,1.6,1.7l0.2,0l11.7,0l-0.7,2.5
			L5,29.6c-2.3,0-4.1-1.8-4.2-4l0-0.2V4.6c0-2.3,1.8-4.1,4-4.2l0.2,0H16.7z"/>
	</g>
</g>
<defs>
	<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="112.8" y="48.4" width="17.3" height="29.2">
		<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
	</filter>
</defs>
<mask maskUnits="userSpaceOnUse" x="112.8" y="48.4" width="17.3" height="29.2" id="mask-2_2_">
	<g class="st5">
		<rect id="path-1_2_" x="0.7" class="st2" width="224" height="126"/>
	</g>
</mask>
<g class="st6">
	
		<g transform="translate(121.479530, 63.000000) rotate(-360.000000) translate(-121.479530, -63.000000) translate(111.979530, 48.000000)">
		<path class="st4" d="M13.9,0.4c2.3,0,4.1,1.8,4.2,4l0,0.2v20.7c0,2.3-1.8,4.1-4,4.2l-0.2,0l-13.1,0l0.7-2.5l12.3,0
			c0.9,0,1.7-0.7,1.7-1.6l0-0.1V4.6c0-0.9-0.8-1.7-1.8-1.7l-12.6,0l0.6-2.5L13.9,0.4z M4.4,17.7l6.8,5.3c0.5,0.4,0.6,1.2,0.2,1.8
			c-0.4,0.5-1.1,0.6-1.6,0.3L9.6,25l-6-4.7L4.4,17.7z M8.6,5.4c2.5,0,4.6,2.1,4.6,4.6s-2.1,4.6-4.6,4.6S4,12.5,4,10S6,5.4,8.6,5.4z
			 M8.6,7.9c-1.2,0-2.1,0.9-2.1,2.1s0.9,2.1,2.1,2.1c1.2,0,2.1-0.9,2.1-2.1S9.7,7.9,8.6,7.9z"/>
	</g>
</g>
</svg>
