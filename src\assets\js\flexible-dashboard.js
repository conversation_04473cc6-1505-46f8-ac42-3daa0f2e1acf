export const refreshScale = () => {
  let baseWidth = document.documentElement.clientWidth;
  let baseHeight = document.documentElement.clientHeight;
  let appStyle = document.getElementById("app").style;
  // let realRatio = baseWidth / baseHeight
  // let designRatio = 16 / 9
  let scaleRate = baseHeight / 1080;
  appStyle.transformOrigin = "left top";
  appStyle.transform = `scale(${scaleRate}) translateX(-50%)`;
  appStyle.width = `${baseWidth / scaleRate}px`;
};