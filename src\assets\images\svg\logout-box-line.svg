<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1194_34469)">
<g filter="url(#filter0_d_1194_34469)">
<path d="M20 18H18V20H6V4H18V6H20V3C20 2.44772 19.5523 2 19 2H5C4.4477 2 4 2.44772 4 3V21C4 21.5523 4.4477 22 5 22H19C19.5523 22 20 21.5523 20 21V18ZM18 11H11V13H18V16L23 12L18 8V11Z" fill="url(#paint0_linear_1194_34469)"/>
</g>
</g>
<defs>
<filter id="filter0_d_1194_34469" x="0" y="-2" width="27" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.52 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1194_34469"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1194_34469" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1194_34469" x1="13.5" y1="2" x2="13.5" y2="22" gradientUnits="userSpaceOnUse">
<stop offset="0.284973" stop-color="#F9FDFF"/>
<stop offset="1" stop-color="#7DD0FF"/>
</linearGradient>
<clipPath id="clip0_1194_34469">
<rect width="24" height="24" fill="white" transform="matrix(-1 0 0 1 24 0)"/>
</clipPath>
</defs>
</svg>
