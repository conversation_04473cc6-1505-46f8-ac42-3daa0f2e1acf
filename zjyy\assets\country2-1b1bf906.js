import{_ as e}from"./back-473409a1.js";import{_ as a,u as t,q as l,d as n,e as s,f as o,o as i,h as u,i as r,F as c,N as m,O as d,z as v,Q as g,l as p,w as x,c as f,k as y,P as b,I as h}from"./vendor-22dca409.js";import{R as F}from"./RotatingCircle-5c206fe2.js";import{r as w,a as k}from"./index-8aea96a7.js";/* empty css                                                                       */const E={class:"project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between"},j={class:"leftList"},C={class:"item-img"},N={class:"item-content"},_={class:"item-title"},I={class:"item-amount"},L={class:"text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont"},z={class:"bgfont"},S={key:0,class:"absolute right-3 top-20"},q={class:"text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']"},M={class:"text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']"},P={class:"text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']"},B={class:"text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']"},O={class:"item-img"},D={class:"item-content"},J={class:"item-title"},R={class:"item-amount"},Q={class:"text-[30px] font-extrabold text-[#EEF4FF] mr-2 bgfont"},T={class:"bgfont"},W={key:0,class:"absolute right-3 top-20"},A={class:"text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']"},G={class:"text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']"},H={class:"text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']"},K={class:"text-[20px] font-extrabold text-[#EEF4FF] ml-5 mr-2 font-['xiaoweiLogo']"},U={class:"absolute w-full flex justify-center font-family-youshebiaotihei tracking-[8px] text-[30px] title mt-[70px]"},V=a({__name:"country2",setup(a){const V=t(),X=l(),Y=n(document.getElementById("iframe")),Z=n(!!localStorage.getItem("isChina")),$=n(localStorage.getItem("clickCountry")),ee=n(0);n([]),n([]);const ae=n(0),te=n(0),le=n(0),ne=n(0),se=n(0),oe=n(0),ie=n(0),ue=n(0),re=n(0),ce=n(0);n([]),n(0),n(null);const me=n([{id:"projects",name:"在建项目总数",amount:0,unit:"个",svgName:"home-2",needCount:!1},{id:"gcksr",name:"工程款收入",amount:0,unit:"",svgName:"",needCount:!0},{id:"htze",name:"在建项目合同总额",amount:0,unit:"",svgName:"",needCount:!0},{id:"bnmb",name:"累计产值",amount:0,unit:"",svgName:"",needCount:!0},{id:"renshu",name:"项目总人数",amount:0,unit:"人",svgName:"home-6",needCount:!1}]);function de(e){const a=Number(e);return isNaN(a)?0:a}const ve=e=>{"project"===e.data.type?V.push({path:"/projectDetail/index",query:{clickProject:JSON.stringify(e.data)}}):"yd"===e.data.type&&V.push({path:"/yingdi/index",query:{clickProject:JSON.stringify(e.data)}}),e.data.level&&pe(e.data.level,e.data.title)};s((()=>{Y.value=document.getElementById("iframe"),(()=>{if(X.query.data)try{const e=JSON.parse(X.query.data);e.title&&"中国"!==e.title&&"earth"!==e.title&&($.value=e.title,localStorage.setItem("clickCountry",e.title)),e.level&&pe(e.level,e.title)}catch(e){}})(),window.addEventListener("message",ve)})),o((()=>{window.removeEventListener("message",ve)}));const ge=n([]),pe=async(e,a)=>{try{let t;const l=e;if("country"===l?t=await w.get("/globalManage/zjmanage/largescreen/getForeignInfoByCountry",{params:{gb:a}}):"province"===l?t=await w.get("/globalManage/zjmanage/largescreen/getInnerProviceCityInfo",{params:{province:a}}):"city"===l?t=await w.get("/globalManage/zjmanage/largescreen/getInnerProviceCityDistrictInfo",{params:{city:a}}):"district"===l&&(t=await w.get("/globalManage/zjmanage/largescreen/getInnerDistrictBaseInfo",{params:{district:a}})),0===t.code&&t.data&&t.data.length>0){const e=t.data[0];$.value=a,ee.value=de(e.在建项目总数),ae.value=de(e.工程款收入),te.value=de(e.在建项目合同总额),le.value=de(e.累计产值),ne.value=de(e.项目总人数),se.value=de(e.中方),oe.value=de(e.外籍);const l=me.value.map((e=>{switch(e.id){case"projects":return{...e,amount:ee.value};case"gcksr":return{...e,amount:ae.value,unit:Z.value?"万元":"万美元",svgName:"home-5-"+(Z.value?"r":"l")};case"htze":return{...e,amount:te.value,unit:Z.value?"万元":"万美元",svgName:"home-3-"+(Z.value?"r":"l")};case"bnmb":return{...e,amount:le.value,unit:Z.value?"万元":"万美元",svgName:"home-4-"+(Z.value?"r":"l")};case"renshu":return{...e,amount:ne.value};default:return e}}));ge.value=l,g((()=>{ge.value=l}))}}catch(t){}},xe=n(!1),fe=()=>{xe.value||(xe.value=!0,Y.value.contentWindow.postMessage({eve:"cancle"},"*"),localStorage.removeItem("clickCountry"),V.replace("/project/index"),setTimeout((()=>{xe.value=!1}),1e3))};return(a,t)=>(i(),u("div",E,[r("div",j,[0===ge.value.length?(i(!0),u(c,{key:0},m(me.value,((e,a)=>(i(),u("div",{class:"items",key:a},[r("div",C,[p(F,{style:{width:"100px",height:"100px"}},{default:x((()=>[p(k,{name:e.svgName,color:"#fff",style:{width:"50px",height:"50px"}},null,8,["name"])])),_:2},1024)]),r("div",N,[r("div",_,v(e.name),1),r("div",I,[r("span",L,[e.needCount?(i(),f(y(b),{key:0,"start-val":0,"end-val":e.amount,duration:5e3,decimals:2,autoplay:!0,separator:","},null,8,["end-val"])):(i(),u(c,{key:1},[h(v(e.amount),1)],64))]),r("span",z,v(e.unit),1)])]),"renshu"===e.id?(i(),u("div",S,[Z.value?(i(),u(c,{key:0},[r("div",null,[t[0]||(t[0]=r("span",{class:"bg-gradient"},"正式员工",-1)),r("span",q,v(ie.value),1),t[1]||(t[1]=r("span",null,"人",-1))]),r("div",null,[t[2]||(t[2]=r("span",{class:"bg-gradient"},"其他形式用工",-1)),r("span",M,v(ue.value+re.value+ce.value),1),t[3]||(t[3]=r("span",null,"人",-1))])],64)):(i(),u(c,{key:1},[r("div",null,[t[4]||(t[4]=r("span",{class:"bg-gradient"},"中方",-1)),r("span",P,v(se.value),1),t[5]||(t[5]=r("span",null,"人",-1))]),r("div",null,[t[6]||(t[6]=r("span",{class:"bg-gradient"},"外籍",-1)),r("span",B,v(oe.value),1),t[7]||(t[7]=r("span",null,"人",-1))])],64))])):d("",!0)])))),128)):d("",!0),ge.value.length>0?(i(!0),u(c,{key:1},m(ge.value,((e,a)=>(i(),u("div",{class:"items",key:a},[r("div",O,[p(F,{style:{width:"100px",height:"100px"}},{default:x((()=>[p(k,{name:e.svgName,color:"#fff",style:{width:"50px",height:"50px"}},null,8,["name"])])),_:2},1024)]),r("div",D,[r("div",J,v(e.name),1),r("div",R,[r("span",Q,[e.needCount?(i(),f(y(b),{key:0,"start-val":0,"end-val":e.amount,duration:5e3,decimals:2,autoplay:!0,separator:","},null,8,["end-val"])):(i(),u(c,{key:1},[h(v(e.amount),1)],64))]),r("span",T,v(e.unit),1)])]),"renshu"===e.id?(i(),u("div",W,[Z.value?(i(),u(c,{key:0},[r("div",null,[t[8]||(t[8]=r("span",{class:"bg-gradient"},"正式员工",-1)),r("span",A,v(ie.value),1),t[9]||(t[9]=r("span",null,"人",-1))]),r("div",null,[t[10]||(t[10]=r("span",{class:"bg-gradient"},"其他形式用工",-1)),r("span",G,v(ue.value+re.value+ce.value),1),t[11]||(t[11]=r("span",null,"人",-1))])],64)):(i(),u(c,{key:1},[r("div",null,[t[12]||(t[12]=r("span",{class:"bg-gradient"},"中方",-1)),r("span",H,v(se.value),1),t[13]||(t[13]=r("span",null,"人",-1))]),r("div",null,[t[14]||(t[14]=r("span",{class:"bg-gradient"},"外籍",-1)),r("span",K,v(oe.value),1),t[15]||(t[15]=r("span",null,"人",-1))])],64))])):d("",!0)])))),128)):d("",!0)]),r("img",{class:"pointer-events-auto cursor-pointer absolute top-[30px] left-[60px]",src:e,alt:"",srcset:"",onClick:fe}),r("div",U,v($.value),1)]))}},[["__scopeId","data-v-9789c90d"]]);export{V as default};
