<template>
  <div class="video-display-container">
    <!-- 简化的头部 - 与业务布局保持一致 -->
    <div class="simple-header">
      <!-- 中间标题 -->
      <div class="header-center">
        <div class="title-wrapper">
          <img src="@/assets/images/header/ywbj.png" alt="背景" class="title-bg" />
        </div>
      </div>
      
      <!-- 右上角控制区域 -->
      <div class="header-controls">
        
        <!-- 返回按钮 -->
        <div class="control-item back-control" @click="goBack" title="返回业务布局">
          <svg class="back-icon" viewBox="0 0 24 24" width="20" height="20">
            <path fill="currentColor" d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
          </svg>
          <span class="back-text">返回</span>
        </div>
      </div>
    </div>

    <div class="flex home-container justify-center relative z-10" :class="{ 'expanded': isExpanded }">
    <div class="carousel-3d -mt-[150px]">
      <!-- 重大工程卡片 -->
      <div class="carousel-card" :class="{
        'active': activeCardIndex === 0,
        'right': activeCardIndex === 3,
        'left': activeCardIndex === 1 || activeCardIndex === 2,
        'left-far': activeCardIndex === 2,
        'right-far': activeCardIndex === 3
      }" @click="switchCard(0)">
        <div class="rotate-perspective mx-[10px]">
          <div class="mt-[10px] video-card-bg w-[2800px] h-[700px] rotate-item2">
            <div class="w-full h-full py-[15px] box-border rotate-perspective relative">
              <!-- 切换按钮区域 - 放在左上角 -->
              <div v-if="shouldRenderContent.majorProjects" class="major-project-controls-top">
                <div
                  class="major-project-tab-small"
                  :class="{ 'active': majorProjectType === 'international' }"
                  @click.stop="switchMajorProjectType('international')"
                >
                  国际工程
                </div>
                <div
                  class="major-project-tab-small"
                  :class="{ 'active': majorProjectType === 'domestic' }"
                  @click.stop="switchMajorProjectType('domestic')"
                >
                  国内工程
                </div>
              </div>

              <div class="w-full text-center text-[28px] h-[40px] leading-[40px] card-title">
                重点工程
              </div>

              <!-- 加载状态 -->
              <div v-if="isLoadingMajorData" class="loading-overlay">
                <div class="loading-spinner"></div>
                <p class="loading-text">正在加载重点工程数据...</p>
              </div>

              <!-- 重点工程项目网格 -->
              <div v-else-if="shouldRenderContent.majorProjects" class="video-grid major-projects-grid">
                <div class="video-row" v-for="(row, rowIndex) in displayedMajorProjectRows" :key="rowIndex">
                  <div class="video-item" v-for="(project, colIndex) in row"
                    :key="project.id || project.storeId || `${rowIndex}-${colIndex}`"
                    :data-project-id="project.isEmpty ? undefined : (project.id || project.storeId)"
                    :data-device-serial="project.deviceSerial"
                    :data-channel-no="project.channelNo">

                    <!-- 国际工程项目 - 播放视频 -->
                    <div v-if="!project.isEmpty && majorProjectType === 'international'"
                         class="video-bg"
                         :class="{ 'clickable': project.sp }"
                         :title="project.sp ? '点击放大视频' : ''"
                         @click="openFullscreenVideo(project)">
                      <div class="project-name" :title="project.xmmc">
                        {{ project.xmjc || project.xmmc }}
                      </div>
                      <!-- 项目视频播放器 -->
                      <div v-if="project.sp" class="video-player-container-wrapper">
                        <!-- 放大按钮覆盖层 -->
                        <div class="fullscreen-overlay">
                          <div class="fullscreen-btn">
                            <svg viewBox="0 0 24 24" width="24" height="24">
                              <path fill="currentColor"
                                d="M7,14H5v5h5v-2H7V14z M12,10V7H9V5h5v5H12z M14,17h3v-3h2v5h-5V17z M17,10h2V5h-5v2h3V10z" />
                            </svg>
                          </div>
                        </div>
                        <!-- 加载状态 -->
                        <div v-if="playerLoadingStates.get(`major-international-player-${project.id}`)"
                          class="player-loading-overlay">
                          <div class="loading-spinner-small"></div>
                          <div class="loading-text-small">正在加载视频...</div>
                        </div>
                        <!-- 播放器容器 -->
                        <div :id="`major-international-player-${project.id}`" class="video-player-container">
                        </div>
                      </div>
                      <!-- 无视频状态显示 -->
                      <div v-else class="no-video-placeholder">
                        <div class="placeholder-icon">🎬</div>
                        <div class="placeholder-text">暂无视频</div>
                      </div>
                    </div>

                    <!-- 国内工程项目 - 播放监控 -->
                    <div v-else-if="!project.isEmpty && majorProjectType === 'domestic'" class="monitor-bg">
                      <div class="monitor-name" :title="project.storeName">
                        {{ project.storeName }}
                      </div>
                      <!-- EZUIKit监控播放器 -->
                      <div v-if="project.channelStatus === '1'" class="monitor-player-wrapper">
                        <!-- 加载状态 -->
                        <div
                          v-if="playerLoadingStates.get(`major-domestic-player-${project.deviceSerial}-${project.channelNo}`)"
                          class="player-loading-overlay">
                          <div class="loading-spinner-small"></div>
                          <div class="loading-text-small">正在连接监控...</div>
                        </div>
                        <!-- 错误状态 -->
                        <div
                          v-else-if="playerErrorStates.get(`major-domestic-player-${project.deviceSerial}-${project.channelNo}`)"
                          class="player-error-overlay">
                          <div class="error-icon">⚠️</div>
                          <div class="error-text-small">连接失败</div>
                        </div>
                        <!-- 播放器容器 -->
                        <div :id="`major-domestic-player-${project.deviceSerial}-${project.channelNo}`" class="monitor-player">
                        </div>
                      </div>
                      <!-- 离线监控显示 -->
                      <div v-else class="offline-monitor">
                        <div class="offline-icon">📷</div>
                        <div class="offline-text">设备{{ getStatusText(project.channelStatus) }}</div>
                      </div>
                    </div>

                    <!-- 空白项目 -->
                    <div v-else class="video-bg empty-project">
                      <div class="project-name">等待项目</div>
                      <div class="empty-video-placeholder">
                        <div class="placeholder-icon">🎬</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 如果没有数据显示空状态 -->
                <div v-if="currentMajorProjects.length === 0" class="empty-state">
                  <div class="empty-text">暂无{{ majorProjectType === 'international' ? '国际' : '国内' }}重点工程项目</div>
                </div>
              </div>

              <!-- 非激活状态提示 -->
              <div v-else class="inactive-card-hint">
                <div class="hint-icon">🏗️</div>
                <div class="hint-text">点击查看重点工程项目</div>
              </div>
            </div>
          </div>
          <div v-if="activeCardIndex !== 0" class="card-overlay"></div>
        </div>
      </div>

      <!-- 国际工程卡片 -->
      <div class="carousel-card" :class="{
        'active': activeCardIndex === 1,
        'right': activeCardIndex === 0,
        'left': activeCardIndex === 2 || activeCardIndex === 3,
        'left-far': activeCardIndex === 3,
        'right-far': activeCardIndex === 0
      }" @click="switchCard(1)">
        <div class="rotate-perspective mx-[10px]">
          <div class="mt-[10px] video-card-bg w-[2800px] h-[700px] rotate-item2">
            <div class="w-full h-full py-[15px] box-border rotate-perspective">
              <div class="w-full text-center text-[28px] h-[40px] leading-[40px] card-title">
                国际工程
              </div>
              <!-- 加载状态 -->
              <div v-if="isLoadingInternationalData" class="loading-overlay">
                <div class="loading-spinner"></div>
                <p class="loading-text">正在加载国际工程数据...</p>
              </div>
              <!-- 国际工程项目网格 - 按需渲染 -->
              <div v-else-if="shouldRenderContent.internationalProjects" ref="internationalVideoGrid"
                class="video-grid">
                <div class="video-row" v-for="(row, rowIndex) in displayedInternationalRows" :key="rowIndex">
                  <div class="video-item" v-for="(project, colIndex) in row"
                    :key="project.id || `${rowIndex}-${colIndex}`"
                    :data-project-id="project.isEmpty ? undefined : project.id">
                    <div v-if="!project.isEmpty" class="video-bg" :class="{ 'clickable': project.sp }"
                      :title="project.sp ? '点击放大视频' : ''" @click="openFullscreenVideo(project)">
                      <div class="project-name" :title="project.xmmc">
                        {{ project.xmjc || project.xmmc }}
                      </div>
                      <!-- 项目视频播放器 -->
                      <div v-if="project.sp" class="video-player-container-wrapper">
                        <!-- 放大按钮覆盖层 -->
                        <div class="fullscreen-overlay">
                          <div class="fullscreen-btn">
                            <svg viewBox="0 0 24 24" width="24" height="24">
                              <path fill="currentColor"
                                d="M7,14H5v5h5v-2H7V14z M12,10V7H9V5h5v5H12z M14,17h3v-3h2v5h-5V17z M17,10h2V5h-5v2h3V10z" />
                            </svg>
                          </div>
                        </div>
                        <!-- 加载状态 -->
                        <div v-if="playerLoadingStates.get(`international-player-${project.id}`)"
                          class="player-loading-overlay">
                          <div class="loading-spinner-small"></div>
                          <div class="loading-text-small">正在加载视频...</div>
                        </div>
                        <!-- 错误状态 -->
                        <!-- <div v-else-if="playerErrorStates.get(`international-player-${project.id}`)"
                          class="player-error-overlay">
                          <div class="error-icon">⚠️</div>
                          <div class="error-text-small">视频加载失败</div>
                        </div> -->
                        <!-- 播放器容器 -->
                        <div :id="`international-player-${project.id}`" class="video-player-container">
                        </div>
                      </div>
                      <!-- 无视频状态显示 -->
                      <div v-else class="no-video-placeholder">
                        <div class="placeholder-icon">🎬</div>
                        <div class="placeholder-text">暂无视频</div>
                      </div>
                    </div>
                    <div v-else class="video-bg empty-project">
                      <div class="project-name">等待项目</div>
                      <div class="empty-video-placeholder">
                        <div class="placeholder-icon">🎬</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 如果没有数据显示空状态 -->
                <div v-if="internationalProjects.length === 0" class="empty-state">
                  <div class="empty-text">暂无国际工程项目</div>
                </div>
              </div>
              <!-- 非激活状态提示 -->
              <div v-else class="inactive-card-hint">
                <div class="hint-icon">🏗️</div>
                <div class="hint-text">点击查看国际工程项目</div>
              </div>
            </div>
          </div>
          <div v-if="activeCardIndex !== 1" class="card-overlay"></div>
        </div>
      </div>

      <!-- 国内工程卡片 - 直接显示监控 -->
      <div class="carousel-card" :class="{
        'active': activeCardIndex === 2,
        'right': activeCardIndex === 0 || activeCardIndex === 1,
        'right-far': activeCardIndex === 0,
        'left': activeCardIndex === 3,
        'left-far': activeCardIndex === 0
      }" @click="switchCard(2)">
        <div class="rotate-perspective mx-[10px]">
          <div class="mt-[10px] video-card-bg w-[2800px] h-[700px] rotate-item2">
            <div class="w-full h-full py-[15px] box-border rotate-perspective">
              <div class="w-full text-center text-[28px] h-[40px] leading-[40px] card-title">
                国内工程监控
              </div>
              <!-- 监控视频网格 - 按需渲染 -->
              <div v-if="isLoadingMonitorData" class="loading-overlay">
                <div class="loading-spinner"></div>
                <p class="loading-text">正在加载监控数据...</p>
              </div>
              <div v-else-if="shouldRenderContent.domesticMonitoring" ref="monitorVideoGrid" class="video-grid">
                <div class="video-row" v-for="(row, rowIndex) in displayedMonitorRows" :key="rowIndex">
                  <div class="video-item" v-for="(monitor, colIndex) in row"
                    :key="monitor.id || `${rowIndex}-${colIndex}`"
                    :data-monitor-id="monitor.isEmpty ? undefined : `${monitor.deviceSerial}-${monitor.channelNo}`">
                    <div v-if="!monitor.isEmpty" class="monitor-bg">
                      <div class="monitor-name" :title="monitor.storeName">
                        {{ monitor.storeName }}
                      </div>
                      <!-- <div class="monitor-info">
                        <span class="device-serial">{{ monitor.deviceSerial }}</span>
                        <span class="channel-no">通道{{ monitor.channelNo }}</span>
                        <span class="status-indicator" :class="getStatusClass(monitor.channelStatus)">
                          {{ getStatusText(monitor.channelStatus) }}
                        </span> -->
                      <!-- </div> -->
                      <!-- EZUIKit监控播放器 -->
                      <div v-if="monitor.channelStatus === '1'" class="monitor-player-wrapper">
                        <!-- 加载状态 -->
                        <div
                          v-if="playerLoadingStates.get(`monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`)"
                          class="player-loading-overlay">
                          <div class="loading-spinner-small"></div>
                          <div class="loading-text-small">正在连接监控...</div>
                        </div>
                        <!-- 错误状态 -->
                        <div
                          v-else-if="playerErrorStates.get(`monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`)"
                          class="player-error-overlay">
                          <div class="error-icon">⚠️</div>
                          <div class="error-text-small">连接失败</div>
                        </div>
                        <!-- 播放器容器 -->
                        <div :id="`monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`" class="monitor-player">
                        </div>
                      </div>
                      <!-- 离线监控显示 -->
                      <div v-else class="offline-monitor">
                        <div class="offline-icon">📷</div>
                        <div class="offline-text">设备{{ getStatusText(monitor.channelStatus) }}</div>
                      </div>
                    </div>
                    <div v-else class="monitor-bg empty-monitor">
                      <div class="monitor-name">等待监控</div>
                      <div class="empty-monitor-placeholder">
                        <div class="placeholder-icon">📷</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 如果没有数据显示空状态 -->
                <div v-if="monitorData.length === 0" class="empty-state">
                  <div class="empty-text">暂无监控设备</div>
                </div>
              </div>
              <!-- 非激活状态提示 -->
              <div v-else class="inactive-card-hint">
                <div class="hint-icon">📹</div>
                <div class="hint-text">点击查看国内工程监控</div>
              </div>
            </div>
          </div>
          <div v-if="activeCardIndex !== 2" class="card-overlay"></div>
        </div>
      </div>

      <!-- 公建中心卡片 -->
      <div class="carousel-card" :class="{
        'active': activeCardIndex === 3,
        'right': activeCardIndex === 0 || activeCardIndex === 1 || activeCardIndex === 2,
        'right-far': activeCardIndex === 1,
        'right-furthest': activeCardIndex === 0,
        'left': activeCardIndex === 0
      }" @click="switchCard(3)">
        <div class="rotate-perspective mx-[10px]">
          <div class="mt-[10px] video-card-bg w-[2800px] h-[700px] rotate-item2">
            <div class="w-full h-full py-[15px] box-border rotate-perspective">
              <div class="w-full text-center text-[28px] h-[40px] leading-[40px] card-title">
                公建中心
              </div>
              <div v-if="shouldRenderContent.publicBuilding" class="video-grid">
                <div class="video-row" v-for="row in 4" :key="row">
                  <div class="video-item" v-for="col in 4" :key="col">
                    <div class="video-bg">
                      <div class="project-name">公建中心项目{{ (row - 1) * 4 + col }}</div>
                      <video autoplay loop muted src="@/assets/images/projectVideo/moni-1.mp4" class="video-player"></video>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="inactive-card-hint">
                <div class="hint-icon">🏢</div>
                <div class="hint-text">点击查看公建中心项目</div>
              </div>
            </div>
          </div>
          <div v-if="activeCardIndex !== 3" class="card-overlay"></div>
        </div>
      </div>

      <div class="carousel-controls" style="display: none;">
        <div class="carousel-dot" :class="{ 'active': activeCardIndex === 0 }" @click="switchCard(0)"></div>
        <div class="carousel-dot" :class="{ 'active': activeCardIndex === 1 }" @click="switchCard(1)"></div>
        <div class="carousel-dot" :class="{ 'active': activeCardIndex === 2 }" @click="switchCard(2)"></div>
        <div class="carousel-dot" :class="{ 'active': activeCardIndex === 3 }" @click="switchCard(3)"></div>
      </div>

      <div class="base-navigation">
        <img src="@/assets/images/jingying/jingyingBase.png" @click="handleBaseClick" alt="底部导航" />
      </div>
    </div>

    <!-- 全屏视频弹窗 -->
    <div v-if="isFullscreenVisible" class="fullscreen-modal" @click="closeFullscreenVideo">
      <div class="fullscreen-content" @click.stop>
        <!-- 关闭按钮 -->
        <div class="fullscreen-close" @click="closeFullscreenVideo">
          <svg viewBox="0 0 24 24" width="32" height="32">
            <path fill="currentColor"
              d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
          </svg>
        </div>

        <!-- 视频信息 -->
        <div v-if="fullscreenProject" class="fullscreen-header">
          <h2 class="fullscreen-title">{{ fullscreenProject.xmmc }}</h2>
          <!-- <p class="fullscreen-subtitle">{{ fullscreenProject.xmmc }}</p> -->
        </div>

        <!-- 全屏播放器容器 -->
        <div class="fullscreen-player-wrapper">
          <!-- 加载状态 -->
          <div v-if="fullscreenLoading" class="fullscreen-loading">
            <div class="loading-spinner"></div>
            <p class="loading-text">正在加载视频...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="fullscreenError" class="fullscreen-error">
            <div class="error-icon">⚠️</div>
            <div class="error-text">{{ fullscreenError }}</div>
            <button class="retry-btn" @click="retryFullscreenVideo">重试</button>
          </div>

          <!-- 播放器容器 -->
          <div id="fullscreen-video-player" class="fullscreen-player">
          </div>
        </div>
      </div>
    </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from "vue";
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import request from '@/utils/request';
// 导入优化后的管理器
import tokenManager from '@/utils/tokenManager.js';
import playerManager from '@/utils/playerManager.js';

const activeCardIndex = ref(1);
let carouselTimer = null;

const router = useRouter();
const { t, locale } = useI18n();

// 头部展开/收起状态
const isExpanded = ref(false);

// 展开/收起功能
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 返回业务布局页面
const goBack = () => {
  router.push('/business-display');
};

// 监控数据相关状态
const monitorData = ref([]);
const isLoadingMonitorData = ref(false);

// 国际工程数据相关状态
const internationalProjects = ref([]);
const isLoadingInternationalData = ref(false);

// 播放器加载状态管理
const playerLoadingStates = ref(new Map());
const playerErrorStates = ref(new Map());

// 全屏视频相关状态
const isFullscreenVisible = ref(false);
const fullscreenProject = ref(null);
const fullscreenLoading = ref(false);
const fullscreenError = ref(null);
const fullscreenPlayer = ref(null);

// Intersection Observer Refs
const internationalVideoGrid = ref(null);
const monitorVideoGrid = ref(null);
const internationalObserver = ref(null);
const monitorObserver = ref(null);

// 防抖工具函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 是否应该渲染内容（按需渲染优化）
const shouldRenderContent = ref({
  majorProjects: false,
  internationalProjects: false,
  domesticMonitoring: false,
  publicBuilding: false
});

// 重点工程卡片的切换状态：'international' 或 'domestic'
const majorProjectType = ref('international');

// 重点工程数据相关状态
const majorInternationalProjects = ref([]);
const majorDomesticProjects = ref([]);
const isLoadingMajorData = ref(false);

// 切换重点工程类型
const switchMajorProjectType = async (type) => {
  if (majorProjectType.value === type) return;

  console.log(`🔄 切换重点工程类型从 ${majorProjectType.value} 到: ${type}`);
  majorProjectType.value = type;

  if (type === 'international') {
    console.log(`📊 当前国际项目数量: ${majorInternationalProjects.value.length}`);
    if (majorInternationalProjects.value.length === 0) {
      await fetchMajorInternationalProjects();
    }
    // 等待DOM更新后设置观察者
    await nextTick();
    console.log('🔍 设置国际项目观察者...');
    setupMajorInternationalPlayerObserver();
  } else if (type === 'domestic') {
    console.log(`📊 当前国内项目数量: ${majorDomesticProjects.value.length}`);
    if (majorDomesticProjects.value.length === 0) {
      await fetchMajorDomesticProjects();
    }
    // 等待DOM更新后设置观察者
    await nextTick();
    console.log('🔍 设置国内项目观察者...');
    setupMajorDomesticPlayerObserver();
  }
};

// 计算属性：当前显示的重点工程项目
const currentMajorProjects = computed(() => {
  return majorProjectType.value === 'international'
    ? majorInternationalProjects.value
    : majorDomesticProjects.value;
});

// 计算属性：将重点工程项目按行分组显示（每行4个）
const displayedMajorProjectRows = computed(() => {
  const rows = [];
  const itemsPerRow = 4;
  const projects = currentMajorProjects.value;

  if (!projects || projects.length === 0) {
    return [];
  }

  // 按行分组
  for (let i = 0; i < projects.length; i += itemsPerRow) {
    const row = projects.slice(i, i + itemsPerRow);
    rows.push(row);
  }

  // 如果最后一行不满4个，用空白项目填充
  const lastRow = rows[rows.length - 1];
  if (lastRow && lastRow.length < itemsPerRow) {
    const placeholdersToAdd = itemsPerRow - lastRow.length;
    for (let i = 0; i < placeholdersToAdd; i++) {
      lastRow.push({
        id: `empty-major-${rows.length - 1}-${lastRow.length}`,
        isEmpty: true
      });
    }
  }

  return rows;
});

// 计算属性：将显示的监控数据按行分组显示（每行4个）
const displayedMonitorRows = computed(() => {
  const rows = [];
  const itemsPerRow = 4;
  const monitors = monitorData.value;

  if (!monitors || monitors.length === 0) {
    return [];
  }

  // 按行分组
  for (let i = 0; i < monitors.length; i += itemsPerRow) {
    const row = monitors.slice(i, i + itemsPerRow);
    rows.push(row);
  }

  // 如果最后一行不满4个，用空白监控填充
  const lastRow = rows[rows.length - 1];
  if (lastRow && lastRow.length < itemsPerRow) {
    const placeholdersToAdd = itemsPerRow - lastRow.length;
    for (let i = 0; i < placeholdersToAdd; i++) {
      lastRow.push({
        id: `empty-monitor-${rows.length - 1}-${lastRow.length}`,
        isEmpty: true,
      });
    }
  }

  return rows;
});

// 计算属性：将显示的国际工程项目按行分组显示（每行4个）
const displayedInternationalRows = computed(() => {
  const rows = [];
  const itemsPerRow = 4;
  const projects = internationalProjects.value;

  if (!projects || projects.length === 0) {
    return [];
  }

  // 按行分组
  for (let i = 0; i < projects.length; i += itemsPerRow) {
    const row = projects.slice(i, i + itemsPerRow);
    rows.push(row);
  }

  // 如果最后一行不满4个，用空白项目填充
  const lastRow = rows[rows.length - 1];
  if (lastRow && lastRow.length < itemsPerRow) {
    const placeholdersToAdd = itemsPerRow - lastRow.length;
    for (let i = 0; i < placeholdersToAdd; i++) {
      lastRow.push({
        id: `empty-${rows.length - 1}-${lastRow.length}`,
        isEmpty: true
      });
    }
  }

  return rows;
});

// 获取国际工程项目列表
const fetchInternationalProjects = async () => {
  try {
    isLoadingInternationalData.value = true;
    // 在重新获取数据前，确保销毁所有现有播放器
    await playerManager.destroyAllPlayers('international');

    const response = await request.get('/globalManage/zjmanage/largescreen/allsplist');

    if (response.code === 0 && response.data) {
      internationalProjects.value = response.data;
      console.log('✅ 国际工程项目获取成功:', internationalProjects.value.length, '个项目');

      // await nextTick();
      // if (activeCardIndex.value === 1) {
      //   initializeAllInternationalPlayers();
      // }
    } else {
      console.error('❌ 获取国际工程项目失败:', response.msg || '未知错误');
      internationalProjects.value = [];
    }
  } catch (error) {
    console.error('❌ 获取国际工程项目失败:', error);
    internationalProjects.value = [];
  } finally {
    isLoadingInternationalData.value = false;
  }
};

// 获取监控数据列表
const fetchMonitorData = async () => {
  try {
    isLoadingMonitorData.value = true;
    // 在重新获取数据前，确保销毁所有现有播放器
    await playerManager.destroyAllPlayers('monitor');

    const response = await request.get('/globalManage/zjmanage/largescreen/allcameraList');

    if (response.code === 0 && response.data) {
      monitorData.value = response.data;
      console.log('✅ 监控数据获取成功:', monitorData.value.length, '个设备');

      // await nextTick();
      // if (activeCardIndex.value === 2) {
      //   initializeAllMonitorPlayers();
      // }
    } else {
      console.error('❌ 获取监控数据失败:', response.msg || '未知错误');
      monitorData.value = [];
    }
  } catch (error) {
    console.error('❌ 获取监控数据失败:', error);
    monitorData.value = [];
  } finally {
    isLoadingMonitorData.value = false;
  }
};

// 获取重点工程国际项目列表
const fetchMajorInternationalProjects = async () => {
  try {
    console.log('🔄 开始获取重点工程国际项目数据...');
    isLoadingMajorData.value = true;
    await playerManager.destroyAllPlayers('major-international');

    const response = await request.get('/globalManage/zjmanage/largescreen/allSpImportList');
    console.log('📡 重点工程国际项目API响应:', response);

    if (response.code === 0 && response.data) {
      majorInternationalProjects.value = response.data;
      console.log('✅ 重点工程国际项目获取成功:', majorInternationalProjects.value.length, '个项目');
      console.log('📋 项目数据:', majorInternationalProjects.value);
    } else {
      console.error('❌ 获取重点工程国际项目失败:', response.msg || '未知错误');
      majorInternationalProjects.value = [];
    }
  } catch (error) {
    console.error('❌ 获取重点工程国际项目失败:', error);
    majorInternationalProjects.value = [];
  } finally {
    isLoadingMajorData.value = false;
  }
};

// 获取重点工程国内项目列表
const fetchMajorDomesticProjects = async () => {
  try {
    console.log('🔄 开始获取重点工程国内项目数据...');
    isLoadingMajorData.value = true;
    await playerManager.destroyAllPlayers('major-domestic');

    const response = await request.get('/globalManage/zjmanage/largescreen/allCameraImportList');
    console.log('📡 重点工程国内项目API响应:', response);

    if (response.code === 0 && response.data) {
      majorDomesticProjects.value = response.data;
      console.log('✅ 重点工程国内项目获取成功:', majorDomesticProjects.value.length, '个项目');
      console.log('📋 监控数据:', majorDomesticProjects.value);
    } else {
      console.error('❌ 获取重点工程国内项目失败:', response.msg || '未知错误');
      majorDomesticProjects.value = [];
    }
  } catch (error) {
    console.error('❌ 获取重点工程国内项目失败:', error);
    majorDomesticProjects.value = [];
  } finally {
    isLoadingMajorData.value = false;
  }
};

// 播放器状态管理
const handlePlayerLoadingChange = (playerId, isLoading) => {
  if (isLoading) {
    playerLoadingStates.value.set(playerId, true);
  } else {
    playerLoadingStates.value.delete(playerId);
  }
};

const handlePlayerError = (playerId, error) => {
  playerErrorStates.value.set(playerId, error.message);
  console.error(`播放器 ${playerId} 错误:`, error);
};

// 手动清理播放器错误状态
const clearPlayerError = (playerId) => {
  playerErrorStates.value.delete(playerId);
  playerManager.clearErrorState(playerId);
  console.log(`🧹 手动清理播放器 ${playerId} 的错误状态`);
};

// 获取播放器管理器统计信息（调试用）
const getPlayerStats = () => {
  const stats = playerManager.getStats();
  console.log('📊 播放器统计信息:', stats);
  return stats;
};

// 打开全屏视频
const openFullscreenVideo = (project) => {
  if (!project.sp) {
    console.log('项目没有视频，无法打开全屏');
    return;
  }

  console.log('🖥️ 打开全屏视频:', project.xmjc || project.xmmc);

  fullscreenProject.value = project;
  isFullscreenVisible.value = true;
  fullscreenLoading.value = true;
  fullscreenError.value = null;

  // 延迟初始化，确保DOM已渲染
  nextTick(() => {
    setTimeout(() => {
      initializeFullscreenPlayer();
    }, 100);
  });
};

// 关闭全屏视频
const closeFullscreenVideo = () => {
  console.log('❌ 关闭全屏视频');

  // 销毁全屏播放器
  if (fullscreenPlayer.value) {
    try {
      fullscreenPlayer.value.pause();
      fullscreenPlayer.value.src = '';
      if (fullscreenPlayer.value.parentNode) {
        fullscreenPlayer.value.parentNode.removeChild(fullscreenPlayer.value);
      }
    } catch (error) {
      console.error('清理全屏播放器失败:', error);
    }
    fullscreenPlayer.value = null;
  }

  // 重置状态
  isFullscreenVisible.value = false;
  fullscreenProject.value = null;
  fullscreenLoading.value = false;
  fullscreenError.value = null;
};

// 重试全屏视频加载
const retryFullscreenVideo = () => {
  if (fullscreenProject.value) {
    console.log('🔄 重试加载全屏视频');
    fullscreenLoading.value = true;
    fullscreenError.value = null;

    setTimeout(() => {
      initializeFullscreenPlayer();
    }, 100);
  }
};

// 初始化全屏播放器
const initializeFullscreenPlayer = async () => {
  if (!fullscreenProject.value) return;

  const project = fullscreenProject.value;
  const container = document.getElementById('fullscreen-video-player');

  if (!container) {
    console.error('全屏播放器容器未找到');
    fullscreenError.value = '播放器容器初始化失败';
    fullscreenLoading.value = false;
    return;
  }

  try {
    // 获取Token
    const token = await tokenManager.getVideoToken();

    // 处理视频ID列表
    const videoIds = project.sp.split(',').filter(id => id.trim());
    const firstVideoId = videoIds[0];

    // 构建视频URL
    const userId = "941981453197164545";
    let videoUrl;

    // 🔥 开发模式：如果是临时token，使用测试视频
    if (token.startsWith('temp_token_for_testing')) {
      console.warn('⚠️ 全屏播放器使用测试视频URL（开发模式）');
      videoUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
    } else {
      videoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${firstVideoId}?access_token=${token}&userid=${userId}`;
    }

    console.log(`🎬 初始化全屏视频播放器:`, {
      projectName: project.xmjc,
      firstVideoId,
      videoUrl,
      isTestMode: token.startsWith('temp_token_for_testing')
    });

    // 创建video元素
    const videoElement = document.createElement('video');
    videoElement.src = videoUrl;
    videoElement.autoplay = true;
    videoElement.loop = true;
    videoElement.muted = false; // 全屏时允许声音
    videoElement.controls = true; // 全屏时显示控制条
    videoElement.className = 'fullscreen-video';
    videoElement.style.width = '100%';
    // videoElement.style.height = '100%';
    videoElement.style.objectFit = 'contain';
    videoElement.style.aspectRatio = '16/9';

    // 添加事件监听
    videoElement.addEventListener('loadstart', () => {
      console.log('📺 全屏视频开始加载');
    });

    videoElement.addEventListener('canplaythrough', () => {
      console.log('✅ 全屏视频可以播放');
      fullscreenLoading.value = false;
    });

    videoElement.addEventListener('playing', () => {
      console.log('▶️ 全屏视频开始播放');
      fullscreenLoading.value = false;
    });

    videoElement.addEventListener('error', (e) => {
      const errorMsg = `视频加载失败: ${e.target.error?.message || '未知错误'}`;
      console.error('❌ 全屏视频加载失败:', e.target.error);
      fullscreenError.value = errorMsg;
      fullscreenLoading.value = false;
    });

    // 清空容器并添加视频元素
    container.innerHTML = '';
    container.appendChild(videoElement);

    // 保存播放器引用
    fullscreenPlayer.value = videoElement;

    console.log('✅ 全屏视频播放器初始化成功');

  } catch (error) {
    console.error('❌ 初始化全屏视频播放器失败:', error);
    fullscreenError.value = `初始化失败: ${error.message}`;
    fullscreenLoading.value = false;
  }
};

// 初始化单个监控播放器
const initializeMonitorPlayer = async (monitor) => {
  const playerId = `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
      
  // 检查是否已存在且正常工作的播放器
  if (playerManager.hasPlayer(playerId)) {
    const playerInfo = playerManager.getPlayer(playerId); // getPlayer需要自己在manager中实现
    const container = document.getElementById(playerId);
    
    if (container && playerInfo && playerInfo.instance && 
        container.contains(playerInfo.instance) && 
        playerInfo.instance.readyState > 0) { // readyState > 0 表示至少有元数据
      console.log(`播放器 ${playerId} 已存在且正常，跳过初始化`);
      return;
    }
  }

  await playerManager.initMonitorPlayer(
    monitor,
    handlePlayerLoadingChange,
    handlePlayerError
  );
};

// 初始化国际工程项目视频 - 改为懒加载观察者
const setupInternationalPlayerObserver = () => {
  if (internationalObserver.value) {
    internationalObserver.value.disconnect();
  }

  const options = {
    root: internationalVideoGrid.value,
    rootMargin: '0px 0px 200px 0px', // 预加载视口下方200px的视频
    threshold: 0.1,
  };

  internationalObserver.value = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const projectId = target.dataset.projectId;
        if (!projectId) return;

        const project = internationalProjects.value.find((p) => p.id === projectId);

        if (project && project.sp) {
          const playerId = `international-player-${project.id}`;
          if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
            console.log(`👁️‍🗨️ [Lazy Load] Project in view, initializing: ${project.xmjc}`);
            initializeProjectVideo(project);
          }
        }
        internationalObserver.value.unobserve(target); // 触发后停止观察
      }
    });
  }, options);

  if (internationalVideoGrid.value) {
    const elementsToObserve = internationalVideoGrid.value.querySelectorAll('.video-item[data-project-id]');
    elementsToObserve.forEach((el) => {
      internationalObserver.value.observe(el);
    });
    console.log(`[Observer] Set up for ${elementsToObserve.length} international projects.`);
  }
};

// 设置重点工程国际项目懒加载观察者
const setupMajorInternationalPlayerObserver = () => {
  console.log('🔍 设置重点工程国际项目观察者...');
  const majorProjectsGrid = document.querySelector('.major-projects-grid');
  if (!majorProjectsGrid) {
    console.warn('⚠️ 未找到 .major-projects-grid 元素');
    return;
  }

  const options = {
    root: majorProjectsGrid,
    rootMargin: '0px 0px 200px 0px',
    threshold: 0.1,
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const projectId = target.dataset.projectId;
        console.log(`👁️‍🗨️ [Major International] 元素进入视口, projectId: ${projectId}`);

        if (!projectId) {
          console.warn('⚠️ 元素缺少 data-project-id 属性');
          return;
        }

        const project = majorInternationalProjects.value.find((p) => p.id === projectId);
        console.log(`🔍 查找项目结果:`, project);

        if (project && project.sp) {
          const playerId = `major-international-player-${project.id}`;
          if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
            console.log(`👁️‍🗨️ [Major International] Project in view, initializing: ${project.xmjc}`);
            initializeMajorInternationalPlayer(project);
          } else {
            console.log(`⏭️ 播放器 ${playerId} 已存在或正在加载中`);
          }
        } else {
          console.warn(`⚠️ 项目不存在或没有视频: ${projectId}`);
        }
        observer.unobserve(target);
      }
    });
  }, options);

  const elementsToObserve = majorProjectsGrid.querySelectorAll('.video-item[data-project-id]');
  elementsToObserve.forEach((el) => {
    observer.observe(el);
  });
  console.log(`[Observer] Set up for ${elementsToObserve.length} major international projects.`);
};

// 设置重点工程国内监控懒加载观察者
const setupMajorDomesticPlayerObserver = () => {
  const majorProjectsGrid = document.querySelector('.major-projects-grid');
  if (!majorProjectsGrid) return;

  const options = {
    root: majorProjectsGrid,
    rootMargin: '50px 0px 100px 0px',
    threshold: 0.3,
  };

  const observer = new IntersectionObserver((entries) => {
    let delay = 200;
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const deviceSerial = target.dataset.deviceSerial;
        const channelNo = target.dataset.channelNo;

        if (!deviceSerial || !channelNo) return;

        const monitor = majorDomesticProjects.value.find((m) =>
          !m.isEmpty && m.deviceSerial === deviceSerial && m.channelNo === channelNo
        );

        if (monitor) {
          const playerId = `major-domestic-player-${monitor.deviceSerial}-${monitor.channelNo}`;
          if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
            console.log(`👁️‍🗨️ [Major Domestic] Monitor in view: ${monitor.storeName}, scheduling with delay ${delay}ms`);

            setTimeout(() => {
              initializeMajorDomesticPlayer(monitor);
            }, delay);
            delay += 1200;
          }
        }
        observer.unobserve(target);
      }
    });
  }, options);

  const elementsToObserve = majorProjectsGrid.querySelectorAll('.video-item[data-device-serial]');
  elementsToObserve.forEach((el) => {
    observer.observe(el);
  });
  console.log(`[Observer] Set up for ${elementsToObserve.length} major domestic monitors.`);
};

// 防抖处理观察者回调
const debounceObserverCallback = debounce(function(entries) {
  let delay = 200; // 初始延迟
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      const target = entry.target;
      const monitorId = target.dataset.monitorId;
      if (!monitorId) return;

      const monitor = monitorData.value.find(
        (m) => !m.isEmpty && `${m.deviceSerial}-${m.channelNo}` === monitorId
      );

      if (monitor) {
        const playerId = `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
        if (!playerManager.hasPlayer(playerId) && !playerLoadingStates.value.has(playerId)) {
          console.log(`👁️‍🗨️ [Lazy Load] Monitor in view: ${monitor.storeName}, scheduling with delay ${delay}ms`);
          
          // 延迟初始化，并为下一个递增延迟
          setTimeout(() => {
            initializeMonitorPlayer(monitor);
          }, delay);
          delay += 1200; // 增加1.2秒的间隔
        }
      }
      monitorObserver.value.unobserve(target);
    }
  });
}, 300); // 300ms防抖

// 初始化国内监控视频 - 改为懒加载观察者
const setupMonitorPlayerObserver = () => {
  if (monitorObserver.value) {
    monitorObserver.value.disconnect();
  }

  const options = {
    root: monitorVideoGrid.value,
    rootMargin: '50px 0px 100px 0px', // 减小预加载范围
    threshold: 0.3, // 提高阈值，确保元素更可见时才触发
  };

  monitorObserver.value = new IntersectionObserver((entries) => {
    // 使用防抖处理，避免频繁触发
    debounceObserverCallback(entries);
  }, options);

  if (monitorVideoGrid.value) {
    const elementsToObserve = monitorVideoGrid.value.querySelectorAll('.video-item[data-monitor-id]');
    elementsToObserve.forEach((el) => {
      monitorObserver.value.observe(el);
    });
    console.log(`[Observer] Set up for ${elementsToObserve.length} domestic monitors.`);
  }
};

// 初始化单个项目视频播放器
const initializeProjectVideo = async (project) => {
  await playerManager.initProjectPlayer(
    project,
    handlePlayerLoadingChange,
    handlePlayerError
  );
};

// 初始化重点工程国际项目视频播放器
const initializeMajorInternationalPlayer = async (project) => {
  const playerId = `major-international-player-${project.id}`;

  // 检查是否已存在且正常工作的播放器
  if (playerManager.hasPlayer(playerId)) {
    const playerInfo = playerManager.getPlayer(playerId);
    const container = document.getElementById(playerId);

    if (container && playerInfo && playerInfo.instance &&
        container.contains(playerInfo.instance) &&
        playerInfo.instance.readyState > 0) {
      console.log(`播放器 ${playerId} 已存在且正常，跳过初始化`);
      return;
    }
  }

  await playerManager.initProjectPlayer(
    project,
    handlePlayerLoadingChange,
    handlePlayerError,
    'major-international'
  );
};

// 初始化重点工程国内监控播放器
const initializeMajorDomesticPlayer = async (monitor) => {
  const playerId = `major-domestic-player-${monitor.deviceSerial}-${monitor.channelNo}`;

  // 检查是否已存在且正常工作的播放器
  if (playerManager.hasPlayer(playerId)) {
    const playerInfo = playerManager.getPlayer(playerId);
    const container = document.getElementById(playerId);

    if (container && playerInfo && playerInfo.instance &&
        container.contains(playerInfo.instance) &&
        playerInfo.instance.readyState > 0) {
      console.log(`播放器 ${playerId} 已存在且正常，跳过初始化`);
      return;
    }
  }

  await playerManager.initMonitorPlayer(
    monitor,
    handlePlayerLoadingChange,
    handlePlayerError,
    'major-domestic'
  );
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case '1': return 'online';
    case '0': return 'offline';
    case '-1': return 'error';
    default: return 'unknown';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case '1': return '在线';
    case '0': return '离线';
    case '-1': return '异常';
    default: return '未知';
  }
};

// 定期切换卡片的函数
function startCarouselTimer() {
  carouselTimer = setInterval(() => {
    activeCardIndex.value = (activeCardIndex.value + 1) % 4; // 现在有4张卡片
  }, 10000); // 每10秒切换一次
}

// 手动切换卡片
function switchCard(index) {
  if (activeCardIndex.value !== index) {
    console.log(`🔄 切换到卡片 ${index}`);

    // 断开当前卡片的观察者
    if (activeCardIndex.value === 1 && internationalObserver.value) {
      internationalObserver.value.disconnect();
      console.log('[Observer] Disconnected international project observer.');
    }
    if (activeCardIndex.value === 2 && monitorObserver.value) {
      monitorObserver.value.disconnect();
      console.log('[Observer] Disconnected domestic monitor observer.');
    }

    // 🔥 关键改动：只暂停非目标卡片的播放器，而不是销毁
    if (index !== 0) {
      // 如果不是切换到重点工程卡片，则暂停重点工程播放器
      playerManager.pausePlayersByType('major-international');
      playerManager.pausePlayersByType('major-domestic');
    }
    if (index !== 2) {
      // 如果不是切换到监控卡片，则暂停监控播放器
      playerManager.pausePlayersByType('monitor');
    }
    if (index !== 1) {
      // 如果不是切换到国际工程卡片，则暂停国际工程播放器
      playerManager.pausePlayersByType('international');
    }

    // 更新激活状态
    activeCardIndex.value = index;

    // 更新渲染状态
    updateRenderState(index);
    console.log('🔄 切换到卡片', index);

    // 重置自动切换计时器
    clearInterval(carouselTimer);
  }
}

// 验证和修复播放器状态
async function validateAndFixPlayers() {
  console.log('🔍 验证播放器状态...');
  
  const monitorsToFix = [];
  for (const monitor of monitorData.value) {
    if (monitor && !monitor.isEmpty && monitor.channelStatus === '1') {
      const playerId = `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
      const container = document.getElementById(playerId);
      
      if (container && playerManager.hasPlayer(playerId)) {
        const playerInfo = playerManager.getPlayer(playerId); // 假设playerManager有此方法
        if (playerInfo && playerInfo.instance) {
          // EZUIKit没有标准的readyState或videoWidth，这里的检查可能需要适配
          // 我们可以检查播放器实例是否还在DOM中
          const playerElement = container.querySelector('.ezuikit-video-player');
          if (!playerElement) {
            console.log(`⚠️ 播放器 ${playerId} 状态异常（DOM元素丢失），准备修复`);
            monitorsToFix.push({ playerId, monitor });
          }
        }
      }
    }
  }
  
  if (monitorsToFix.length > 0) {
    console.log(`[Fix] 将要修复 ${monitorsToFix.length} 个播放器`);
    for (let i = 0; i < monitorsToFix.length; i++) {
      const { playerId, monitor } = monitorsToFix[i];
      await fixPlayerState(playerId, monitor);
      // 如果不是最后一个，则等待1.2秒
      if (i < monitorsToFix.length - 1) {
        console.log('[Fix] 等待1.2秒后修复下一个...');
        await new Promise(resolve => setTimeout(resolve, 1200));
      }
    }
    console.log(`✅ 完成修复 ${monitorsToFix.length} 个播放器`);
  }
}

// 修复单个播放器状态
async function fixPlayerState(playerId, monitor) {
  try {
    // 销毁有问题的播放器
    await playerManager.destroyPlayer(playerId);
    
    // 等待一小段时间
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 重新初始化
    await initializeMonitorPlayer(monitor);
    
    console.log(`🔧 播放器 ${playerId} 修复完成`);
  } catch (error) {
    console.error(`修复播放器 ${playerId} 失败:`, error);
  }
}

// 更新渲染状态
const updateRenderState = async (cardIndex) => {
  // 根据当前卡片设置渲染状态并加载数据
  switch (cardIndex) {
    case 0:
      shouldRenderContent.value.majorProjects = true;
      // 根据当前选择的类型加载对应数据
      if (majorProjectType.value === 'international' && majorInternationalProjects.value.length === 0) {
        await fetchMajorInternationalProjects();
      } else if (majorProjectType.value === 'domestic' && majorDomesticProjects.value.length === 0) {
        await fetchMajorDomesticProjects();
      }

      // 恢复重点工程播放器
      await nextTick();
      if (majorProjectType.value === 'international') {
        playerManager.resumePlayersByType('major-international');
        // 设置重点工程国际项目懒加载观察者
        setupMajorInternationalPlayerObserver();
      } else {
        playerManager.resumePlayersByType('major-domestic');
        // 设置重点工程国内监控懒加载观察者
        setupMajorDomesticPlayerObserver();
      }
      break;

    case 1:
      shouldRenderContent.value.internationalProjects = true;
      if (internationalProjects.value.length === 0) {
        await fetchInternationalProjects();
      }
      // 恢复国际工程播放器
      await nextTick();
      playerManager.resumePlayersByType('international');
      // 设置懒加载观察者
      setupInternationalPlayerObserver();
      break;

    case 2:
      shouldRenderContent.value.domesticMonitoring = true;
      if (monitorData.value.length === 0) {
        await fetchMonitorData();
      }
      
      // 关键修改：减少等待时间，并分步骤处理
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // 先恢复已有播放器
      playerManager.resumePlayersByType('monitor');
      
      // 检查并修复播放器状态
      await validateAndFixPlayers();
      
      // 再设置懒加载观察者
      await nextTick();
      setupMonitorPlayerObserver();
      break;

    case 3:
      shouldRenderContent.value.publicBuilding = true;
      break;
  }
};

// 安全重绘单个播放器
async function safeRepaintPlayer(playerId, container) {
  try {
    // 方法1: 强制重排
    container.style.transform = 'translateZ(0)';
    // 强制浏览器重排
    void container.offsetHeight;

    await new Promise(resolve => {
      requestAnimationFrame(() => {
        container.style.transform = '';
        resolve();
      });
    });

    // 方法2: 检查播放器状态并恢复 (对EZUIKit可能不直接适用)
    const playerInfo = playerManager.getPlayer(playerId);
    if (playerInfo && playerInfo.instance) {
      const player = playerInfo.instance;
      // EZUIKit没有标准的play/pause/readyState API，但可以尝试调用其内部方法
      if (typeof player.play === 'function') {
         try {
           await player.play();
         } catch (error) {
            // 很多时候play()会因用户未交互而失败，这里静默处理
         }
      }
    }
  } catch (error) {
    console.error(`重绘播放器 ${playerId} 失败:`, error);
  }
}

// 重绘所有监控播放器
const repaintAllMonitorPlayers = async () => {
  console.log('[Repaint Fix] 开始重绘所有监控播放器...');
  
  // 暂停DOM观察，避免重绘时触发不必要的检测
  if (playerManager.domObserver) {
    playerManager.domObserver.disconnect();
  }

  await new Promise(resolve => setTimeout(resolve, 100));

  const repaintPromises = [];
  
  for (const monitor of monitorData.value) {
    if (monitor && !monitor.isEmpty && monitor.channelStatus === '1') {
      const playerId = `monitor-player-${monitor.deviceSerial}-${monitor.channelNo}`;
      const playerContainer = document.getElementById(playerId);

      if (playerContainer && playerManager.hasPlayer(playerId)) {
        repaintPromises.push(safeRepaintPlayer(playerId, playerContainer));
      }
    }
  }

  await Promise.all(repaintPromises);
  
  // 重新启动DOM观察
  if (playerManager.domObserver) {
    playerManager.domObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  console.log(`[Repaint Fix] 重绘完成`);
};

// 处理底部导航图片点击
function handleBaseClick() {
  // 切换到下一张卡片
  activeCardIndex.value = (activeCardIndex.value + 1) % 4;

  // 重置自动切换计时器
  clearInterval(carouselTimer);
  // startCarouselTimer(); // 如果需要重新启动自动切换，取消注释
}

// 添加返回函数
const back = () => {
  router.replace("/");
};

// 添加语言切换函数
function toggleLanguage() {
  const currentLang = locale.value;
  const newLang = currentLang === 'zh' ? 'en' : 'zh';
  locale.value = newLang;
  localStorage.setItem('language', newLang);
}

// 在组件卸载时清除定时器
onUnmounted(async () => {
  console.log('🧹 组件卸载，开始清理资源...');

  clearInterval(carouselTimer);

  // 断开观察者
  if (internationalObserver.value) {
    internationalObserver.value.disconnect();
  }
  if (monitorObserver.value) {
    monitorObserver.value.disconnect();
  }
  
  // 清理DOM观察者
  if (playerManager.domObserver) {
    playerManager.domObserver.disconnect();
  }

  // 清理所有播放器
  await playerManager.destroyAllPlayers();

  // 清理全屏播放器
  if (fullscreenPlayer.value) {
    try {
      fullscreenPlayer.value.pause();
      fullscreenPlayer.value.src = '';
      if (fullscreenPlayer.value.parentNode) {
        fullscreenPlayer.value.parentNode.removeChild(fullscreenPlayer.value);
      }
    } catch (error) {
      console.error('清理全屏播放器失败:', error);
    }
    fullscreenPlayer.value = null;
  }

  // 清理Token管理器
  tokenManager.cleanup();

  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeydown);

  console.log('✅ 资源清理完成');
});

onMounted(async () => {
  console.log('🚀 组件挂载，初始化页面...');

  // 激活当前卡片的渲染状态并加载数据
  await updateRenderState(activeCardIndex.value);
  
  // 启动DOM观察
  if (playerManager.domObserver) {
    playerManager.domObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown);

  console.log('✅ 页面初始化完成');
});

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape' && isFullscreenVisible.value) {
    closeFullscreenVideo();
  }
};
</script>

<style lang="scss" scoped>
/* 视频展示容器样式 */
.video-display-container {
  width: 100%;
  height: 100%;
  background: #0a0f1a;
  position: relative;
  overflow: hidden;
}

/* 简化头部样式 - 与业务布局保持一致 */
.simple-header {
  width: 100%;
  height: 122px;
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-center {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}


.title-bg {
  height: 110px;
}

.header-controls {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 20px;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.control-item:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #00FFFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.control-text, .back-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  font-family: Alibaba PuHuiTi 2.0, sans-serif;
  transition: color 0.3s ease;
}

.control-item:hover .control-text,
.control-item:hover .back-text {
  color: #00FFFF;
}

.back-icon {
  color: white;
  transition: color 0.3s ease;
}

.control-item:hover .back-icon {
  color: #00FFFF;
}

.back-control {
  background: rgba(255, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.back-control:hover {
  background: rgba(255, 0, 0, 0.2);
  border-color: #ff6b6b;
}

.back-control:hover .back-text,
.back-control:hover .back-icon {
  color: #ff6b6b;
}

.home-container {
  pointer-events: all;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/images/jingying/jingyingBg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 0px; /* 为头部留出空间 */
  padding-top: 20px;
  box-sizing: border-box;
  transition: all 0.5s ease;

  /* 展开效果 */
  &.expanded {
    transform: scale(1.1);
    filter: brightness(1.2);
  }

  .video-card-bg {
    background: url("@/assets/images/xianchangVideo/card.png") no-repeat center center / 100% 100%;
  }

  .carousel-3d {
    position: relative;
    width: 100%;
    height: 800px;
    perspective: 2000px;
    display: flex;
    justify-content: center;
    align-items: center;

    .carousel-card {
      position: absolute;
      transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
      cursor: pointer;
      transform-style: preserve-3d;

      &.active {
        z-index: 10;
        transform: translateZ(100px) scale(1.05);
        filter: brightness(1.1);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
      }

      &.left {
        transform: translateX(-2540px) translateZ(0) rotateY(-20deg);
        filter: brightness(0.8) blur(1px);
        opacity: 0.9;
        z-index: 5;
      }

      &.right {
        transform: translateX(2565px) translateZ(0) rotateY(20deg);
        filter: brightness(0.8) blur(1px);
        opacity: 0.9;
        z-index: 5;
      }

      &.right-far {
        transform: translateX(2340px) translateZ(-100px) rotateY(30deg);
        filter: brightness(0.7) blur(2px);
        opacity: 0.7;
        z-index: 4;
      }

      &.right-furthest {
        transform: translateX(1500px) translateZ(-200px) rotateY(40deg);
        filter: brightness(0.6) blur(3px);
        opacity: 0.5;
        z-index: 3;
      }

      &.left-far {
        transform: translateX(-2350px) translateZ(-100px) rotateY(-30deg);
        filter: brightness(0.7) blur(2px);
        opacity: 0.7;
        z-index: 4;
      }

      &.left-furthest {
        transform: translateX(-1500px) translateZ(-200px) rotateY(-40deg);
        filter: brightness(0.6) blur(3px);
        opacity: 0.5;
        z-index: 3;
      }
    }

    .carousel-controls {
      position: absolute;
      bottom: -40px;
      display: flex;
      justify-content: center;
      gap: 15px;
      z-index: 20;

      .carousel-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background-color: #fff;
          transform: scale(1.2);
        }
      }
    }
  }

  .rotate-perspective {
    perspective: 800px;

    .card-title {
      width: 100%;
      margin-top: 0px;
      font-family: Weiruanyahei, Weiruanyahei;
      font-weight: 500;
      font-size: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      text-align: center;
    }
  }
}

/* 重点工程切换按钮样式 - 左上角位置 */
.major-project-controls-top {
  position: absolute;
  top: 32px;
  left: 30px;
  display: flex;
  gap: 10px;
  z-index: 10;
}

.major-project-tab-small {
  width: 120px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: Alibaba PuHuiTi 2.0, sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  user-select: none;

  /* 未选中状态 */
  background: rgba(70, 118, 226, 0.15);
  box-shadow: inset 0px 0px 8px 1px rgba(70, 118, 226, 0.5);
  border: 1px solid rgba(70, 118, 226, 0.4);

  &:hover {
    background: rgba(70, 118, 226, 0.25);
    border-color: rgba(70, 118, 226, 0.6);
    transform: translateY(-1px);
  }

  /* 选中状态 */
  &.active {
    background: rgba(70, 118, 226, 0.3);
    box-shadow: inset 0px 0px 8px 1px #4676E2, inset 0px 0px 8px 1px #4676E2;
    border: 1px solid #4676E2;
    color: #00FFFF;
    font-weight: 600;

    &:hover {
      transform: none;
    }
  }
}

/* 重点工程网格特殊样式 */
.major-projects-grid {
  margin-top: 0px;
}

.video-grid {
  width: 100%;
  height: calc(100% - 50px);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.video-row {
  height: 270px;
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.video-item {
  flex: 1;
  aspect-ratio: 16/9;
  min-height: 140px;
  border-radius: 8px;
  overflow: hidden;
}

.video-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
  }
}

.project-name {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  height: auto;
  line-height: 1.4;
  padding: 8px 12px;
  box-sizing: border-box;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 14px;
  color: #00FFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.base-navigation {
  position: absolute;
  bottom: -200px;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 20;

  img {
    cursor: pointer;
    width: 60%;
    max-width: 800px;
  }
}

// 加载状态样式
.loading-overlay {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
}

.loading-text {
  margin-top: 15px;
  color: #00FFFF;
  font-size: 16px;
  font-weight: 500;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 空状态样式
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.6);
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
}

// 加载更多样式
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #00FFFF;
  margin-top: 10px;
}

.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

.loading-more-text {
  font-size: 14px;
  font-weight: 500;
}

// 没有更多数据样式
.no-more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 10px;
}

.no-more-text {
  font-size: 14px;
  font-weight: 500;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 15px;
  text-align: center;
  width: 100%;
}

// 空白项目样式
.empty-project {
  opacity: 0.3;
  pointer-events: none;
}

.empty-video-placeholder {
  width: 200px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
}

.placeholder-icon {
  font-size: 30px;
  opacity: 0.5;
}

/* 国际工程项目样式 */
.video-player-container {
  width: 100%;
  // height: 100%;
  aspect-ratio: 16/9;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.no-video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.placeholder-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  text-align: center;
  margin-top: 4px;
}

/* 监控背景样式 */
.monitor-bg {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
  transition: all 0.3s ease;
  object-fit: cover; /* Ensure video fits within the container */
}

.monitor-bg:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.monitor-name {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  height: auto;
  padding: 8px 12px;
  box-sizing: border-box;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: block;
  font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
  font-weight: normal;
  font-size: 12px;
  color: #00FFFF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.monitor-info {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
  font-size: 10px;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
}

.device-serial,
.channel-no {
  background: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.8);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
}

.status-indicator {
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 500;
}

.status-indicator.online {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.status-indicator.offline {
  background: rgba(255, 152, 0, 0.3);
  color: #FF9800;
}

.status-indicator.error {
  background: rgba(244, 67, 54, 0.3);
  color: #F44336;
}

.status-indicator.unknown {
  background: rgba(158, 158, 158, 0.3);
  color: #9E9E9E;
}

/* 监控播放器容器 */
.monitor-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  aspect-ratio: 16/9; /* Maintain aspect ratio */
}

.monitor-player-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player-container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 离线监控显示 */
.offline-monitor {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.offline-icon {
  font-size: 24px;
  opacity: 0.5;
  margin-bottom: 4px;
}

.offline-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  text-align: center;
}

/* 空白监控样式 */
.empty-monitor {
  opacity: 0.3;
  pointer-events: none;
}

.empty-monitor-placeholder {
  width: 180px;
  height: 110px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

/* 监控播放器EZUIKit样式优化 */
:deep(.monitor-player .ez-player) {
  border-radius: 4px !important;
  overflow: hidden !important;
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

:deep(.monitor-player .ez-player-container) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

:deep(.monitor-player .ez-player-video) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: cover !important;
  box-sizing: border-box !important;
}

/* 强制限制所有EZUIKit相关元素的尺寸 */
:deep(.monitor-player .ezuikit-video-player) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

:deep(.monitor-player .ezuikit-video-player *) {
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

/* EZUIKit播放器样式优化 */
:deep(.ez-player) {
  border-radius: 6px !important;
  overflow: hidden !important;
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

:deep(.ez-player-container) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

:deep(.ez-player-video) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: cover !important;
  box-sizing: border-box !important;
}

/* 强制限制所有EZUIKit相关元素的尺寸 */
:deep(.ezuikit-video-player) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

:deep(.ezuikit-video-player *) {
  max-width: 100% !important;
  max-height: 100% !important;
  box-sizing: border-box !important;
}

/* 隐藏滚动条但保留滚动功能 */
.popup-body::-webkit-scrollbar,
.camera-list::-webkit-scrollbar {
  width: 6px;
}

.popup-body::-webkit-scrollbar-track,
.camera-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.popup-body::-webkit-scrollbar-thumb,
.camera-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.popup-body::-webkit-scrollbar-thumb:hover,
.camera-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 播放器状态覆盖层 */
.player-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
}

.player-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 44, 52, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 4px;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.loading-text-small {
  color: #00FFFF;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.error-text-small {
  color: #FF6B6B;
  font-size: 10px;
  font-weight: 500;
  margin-top: 4px;
  text-align: center;
}

.error-icon {
  font-size: 20px;
  opacity: 0.8;
  margin-bottom: 2px;
}

/* 非激活卡片提示 */
.inactive-card-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.inactive-card-hint:hover {
  opacity: 0.8;
}

.hint-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.hint-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

/* 可点击的视频项目样式 */
.video-bg.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.video-bg.clickable:hover {
  transform: scale(1.03);
  box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
}

/* 放大按钮覆盖层 */
.fullscreen-overlay {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-bg.clickable:hover .fullscreen-overlay {
  opacity: 1;
}

.fullscreen-btn {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00FFFF;
  transition: all 0.3s ease;
}

.fullscreen-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  transform: scale(1.1);
}

.fullscreen-btn svg {
  width: 18px;
  height: 18px;
}

/* 全屏弹窗样式 */
.fullscreen-modal {
  position: fixed;
  margin: 0 auto;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.fullscreen-content {
  width: 90%;
  height: 90%;
  max-width: 2800px;
  max-height: 900px;
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 关闭按钮 */
.fullscreen-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  height: 48px;
  background: rgba(255, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #ff6b6b;
  transition: all 0.3s ease;
  z-index: 100;
}

.fullscreen-close:hover {
  background: rgba(255, 0, 0, 0.2);
  transform: scale(1.1);
}

/* 视频信息头部 */
.fullscreen-header {
  padding: 30px 40px 20px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  flex-shrink: 0;
}

.fullscreen-title {
  color: #00FFFF;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
}

.fullscreen-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  margin: 0;
  font-weight: 400;
}

/* 播放器包装容器 */
.fullscreen-player-wrapper {
  flex: 1;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-height: 0;
  aspect-ratio: 16/9;
}

/* 全屏播放器 */
.fullscreen-player {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fullscreen-video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
}

/* 全屏加载状态 */
.fullscreen-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #00FFFF;
  gap: 20px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  z-index: 10;
}

.fullscreen-loading .loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
}

.fullscreen-loading .loading-text {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

/* 全屏错误状态 */
.fullscreen-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ff6b6b;
  gap: 20px;
  text-align: center;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  z-index: 10;
}

.fullscreen-error .error-icon {
  font-size: 48px;
  opacity: 0.8;
}

.fullscreen-error .error-text {
  font-size: 18px;
  font-weight: 500;
  max-width: 400px;
  line-height: 1.5;
}

.retry-btn {
  padding: 12px 24px;
  background: rgba(0, 255, 255, 0.1);
  border: 2px solid #00FFFF;
  border-radius: 6px;
  color: #00FFFF;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: rgba(0, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .camera-popup {
    width: 95%;
    height: 85%;
  }

  .video-popup {
    width: 95%;
    height: 85%;
    max-width: none;
    max-height: none;
  }

  .camera-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .camera-info {
    margin-right: 0;
  }

  .camera-details {
    flex-wrap: wrap;
  }

  .view-btn {
    align-self: flex-end;
  }

  .fullscreen-content {
    width: 95%;
    height: 95%;
    border-radius: 8px;
  }

  .fullscreen-header {
    padding: 20px 25px 15px;
  }

  .fullscreen-title {
    font-size: 20px;
  }

  .fullscreen-subtitle {
    font-size: 14px;
  }

  .fullscreen-close {
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
  }

  .fullscreen-close svg {
    width: 24px;
    height: 24px;
  }

  .fullscreen-player-wrapper {
    padding: 15px;
  }
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 6;
}

.grid-video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

/* 移除了原来的独立返回按钮样式，现在集成在头部中 */
</style>
