// 创建3D地球及国家标注
let renderer, scene, camera, controls, earth, countries = {}, labelRenderer;
const worldRadius = 1;

// 检查TWEEN是否可用
function isTweenAvailable() {
    return typeof TWEEN !== 'undefined';
}

// 初始化Three.js场景
function init() {
    // 检查容器是否存在
    const container = document.getElementById('container');
    if (!container) {
        console.error('找不到容器元素: #container');
        // 尝试创建容器
        const newContainer = document.createElement('div');
        newContainer.id = 'container';
        newContainer.style.width = '100%';
        newContainer.style.height = '100vh';
        newContainer.style.position = 'relative';
        document.body.appendChild(newContainer);
        console.log('已创建新的容器元素');
    }

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.getElementById('container').appendChild(renderer.domElement);

    // 创建场景
    scene = new THREE.Scene();

    // 创建相机
    camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 3;

    // 创建控制器
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.5;
    controls.minDistance = 1.5;
    controls.maxDistance = 5;
    controls.autoRotate = true;
    controls.autoRotateSpeed = 0.5;

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
    scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(5, 3, 5);
    scene.add(directionalLight);

    // 创建地球
    createEarth();

    // 创建星空
    createStarfield();

    // 添加国家标注
    addCountries();

    // 处理窗口大小变化
    window.addEventListener('resize', onWindowResize);

    // 开始动画
    animate();

    // 告知父窗口加载完成
    try {
        if (window.parent) {
            window.parent.postMessage({ type: "earthReady" }, "*");
        }
    } catch (e) {
        console.log('无法通知父窗口', e);
    }
}

// 创建地球
function createEarth() {
    // 加载纹理
    const textureLoader = new THREE.TextureLoader();

    try {
        // 尝试多个可能的路径加载地球纹理
        const earthTexture = textureLoader.load('map6.png',
            // 成功回调
            function (texture) {
                console.log('地球纹理加载成功');
            },
            // 进度回调
            undefined,
            // 错误回调
            function (err) {
                console.error('地球纹理加载失败，尝试备用路径');
                // 尝试备用路径
                textureLoader.load('./public/map/textures/earth.png',
                    function (texture) {
                        earth.material.map = texture;
                        earth.material.needsUpdate = true;
                    },
                    undefined,
                    function () {
                        // 如果还是失败，使用默认颜色
                        console.error('所有地球纹理加载失败，使用纯色');
                        earth.material.color.set(0x1a3d6d);
                        earth.material.needsUpdate = true;
                    }
                );
            }
        );

        // 创建地球几何体和材质
        const geometry = new THREE.SphereGeometry(worldRadius, 64, 64);
        const material = new THREE.MeshPhongMaterial({
            map: earthTexture,
            color: 0x1a3d6d,  // 添加默认颜色，以防纹理加载失败
            shininess: 15,
            transparent: true,      // 启用透明
            opacity: 0.6,           // 设置透明度
            side: THREE.DoubleSide  // 双面渲染
        });

        // 创建地球网格
        earth = new THREE.Mesh(geometry, material);
        scene.add(earth);
    } catch (e) {
        console.error('创建地球时出错:', e);

        // 出错时创建一个简单的纯色地球
        const geometry = new THREE.SphereGeometry(worldRadius, 64, 64);
        const material = new THREE.MeshPhongMaterial({
            color: 0x1a3d6d,
            shininess: 15,
            transparent: true,      // 启用透明
            opacity: 0.6,           // 设置透明度
            side: THREE.DoubleSide  // 双面渲染
        });

        // 创建地球网格
        earth = new THREE.Mesh(geometry, material);
        scene.add(earth);
    }
}

// 创建星空效果
function createStarfield() {
    // 星空配置参数
    const config = {
        starCount: 200,         // 星星数量
        connectionDistance: 0.8, // 连线的最大距离
        starRadius: 2,          // 星星半径
        starfieldRadius: 8,     // 星空半径（比地球大）
        starColor: 0x4e99ff,    // 星星颜色
        lineColor: 0x335577,    // 连线颜色
        lineOpacity: 0.3        // 连线透明度
    };

    // 创建星星点
    const starGeometry = new THREE.BufferGeometry();
    const starPositions = [];
    const starVertices = [];

    // 生成随机星星位置
    for (let i = 0; i < config.starCount; i++) {
        // 在球面上均匀分布点
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.acos(2 * Math.random() - 1);

        const x = config.starfieldRadius * Math.sin(phi) * Math.cos(theta);
        const y = config.starfieldRadius * Math.sin(phi) * Math.sin(theta);
        const z = config.starfieldRadius * Math.cos(phi);

        starPositions.push({
            x: x,
            y: y,
            z: z
        });

        starVertices.push(x, y, z);
    }

    // 创建星星点材质和几何体
    starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
    const starMaterial = new THREE.PointsMaterial({
        color: config.starColor,
        size: config.starRadius / 100,
        sizeAttenuation: true
    });

    // 创建星星点云
    const stars = new THREE.Points(starGeometry, starMaterial);
    scene.add(stars);

    // 创建连线
    const lineGeometry = new THREE.BufferGeometry();
    const linePositions = [];

    // 检查哪些星星需要连线
    for (let i = 0; i < starPositions.length; i++) {
        const point1 = starPositions[i];

        for (let j = i + 1; j < starPositions.length; j++) {
            const point2 = starPositions[j];

            // 计算两点之间的距离
            const distance = Math.sqrt(
                Math.pow(point1.x - point2.x, 2) +
                Math.pow(point1.y - point2.y, 2) +
                Math.pow(point1.z - point2.z, 2)
            );

            // 如果距离在阈值内，则连线
            if (distance < config.connectionDistance) {
                linePositions.push(point1.x, point1.y, point1.z);
                linePositions.push(point2.x, point2.y, point2.z);
            }
        }
    }

    // 创建线条几何体和材质
    lineGeometry.setAttribute('position', new THREE.Float32BufferAttribute(linePositions, 3));
    const lineMaterial = new THREE.LineBasicMaterial({
        color: config.lineColor,
        transparent: true,
        opacity: config.lineOpacity
    });

    // 创建线条
    const lines = new THREE.LineSegments(lineGeometry, lineMaterial);
    scene.add(lines);

    // 在地球表面创建全球网络效果
    createGlobalNetwork();
}

// 创建全球网络效果
function createGlobalNetwork() {
    // 网络配置
    const config = {
        pointCount: 250,                  // 全球点数量
        height: 0.02,                     // 点距离地球表面的高度
        pointSize: 2.5,                   // 点的大小
        pointColor: 0x4e99ff,             // 点的颜色
        pointBrightColor: 0x00ffff,       // 亮点颜色
        lineColor: 0x0088cc,              // 线的颜色
        lineOpacity: 0.5,                 // 线的透明度
        brightPointsPercent: 0.15,        // 亮点百分比
        animationSpeed: 0.002             // 动画速度
    };

    // 创建点的几何体
    const pointGeometry = new THREE.BufferGeometry();
    const pointPositions = [];
    const pointVertices = [];
    const pointColors = [];

    // 颜色对象
    const normalColor = new THREE.Color(config.pointColor);
    const brightColor = new THREE.Color(config.pointBrightColor);

    // 预先定义一些关键点作为主干网络节点
    const keyPoints = [];
    for (let i = 0; i < 12; i++) {
        // 创建均匀分布在球体上的点
        const phi = Math.acos(-1 + (2 * i) / 12);
        const theta = Math.sqrt(12 * Math.PI) * phi;

        const x = (worldRadius + config.height) * Math.sin(phi) * Math.cos(theta);
        const y = (worldRadius + config.height) * Math.sin(phi) * Math.sin(theta);
        const z = (worldRadius + config.height) * Math.cos(phi);

        keyPoints.push({
            x: x,
            y: y,
            z: z,
            isBright: true,
            phase: Math.random() * Math.PI * 2,
            connected: false  // 标记点是否已连接
        });
    }

    // 添加关键点到点位置数组
    keyPoints.forEach(point => {
        pointPositions.push(point);
        pointVertices.push(point.x, point.y, point.z);
        pointColors.push(brightColor.r, brightColor.g, brightColor.b);
    });

    // 在地球表面均匀分布点
    for (let i = 0; i < config.pointCount; i++) {
        // 球面上均匀分布点的算法
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.acos(2 * Math.random() - 1);

        // 计算表面点位置
        const x = (worldRadius + config.height) * Math.sin(phi) * Math.cos(theta);
        const y = (worldRadius + config.height) * Math.sin(phi) * Math.sin(theta);
        const z = (worldRadius + config.height) * Math.cos(phi);

        pointPositions.push({
            x: x,
            y: y,
            z: z,
            isBright: Math.random() < config.brightPointsPercent, // 随机选择亮点
            phase: Math.random() * Math.PI * 2, // 随机相位，用于动画
            connected: false  // 标记点是否已连接
        });

        pointVertices.push(x, y, z);

        // 根据是否为亮点选择颜色
        if (Math.random() < config.brightPointsPercent) {
            pointColors.push(brightColor.r, brightColor.g, brightColor.b);
        } else {
            pointColors.push(normalColor.r, normalColor.g, normalColor.b);
        }
    }

    // 创建点的材质和几何体
    pointGeometry.setAttribute('position', new THREE.Float32BufferAttribute(pointVertices, 3));
    pointGeometry.setAttribute('color', new THREE.Float32BufferAttribute(pointColors, 3));

    const pointMaterial = new THREE.PointsMaterial({
        size: config.pointSize / 100,
        sizeAttenuation: true,
        vertexColors: true,
        transparent: true,
        opacity: 0.8
    });

    // 创建点的网格
    const networkPoints = new THREE.Points(pointGeometry, pointMaterial);
    networkPoints.name = 'global-network-points';
    scene.add(networkPoints);

    // 创建连线
    const lineGeometry = new THREE.BufferGeometry();
    const linePositions = [];
    const lineColors = [];

    // 连接关键节点形成骨干网络 - 简化为环形连接
    for (let i = 0; i < keyPoints.length; i++) {
        const point1 = keyPoints[i];
        const point2 = keyPoints[(i + 1) % keyPoints.length]; // 循环连接

        linePositions.push(point1.x, point1.y, point1.z);
        linePositions.push(point2.x, point2.y, point2.z);

        // 使用亮色
        lineColors.push(brightColor.r, brightColor.g, brightColor.b);
        lineColors.push(brightColor.r, brightColor.g, brightColor.b);

        point1.connected = true;
        point2.connected = true;
    }

    // 为每个未连接的点找一个最近的点连接，确保每个点只有一条线
    // 先处理亮点，确保它们先连接起来
    const brightPoints = pointPositions.filter(p => p.isBright && !p.connected);
    for (let i = 0; i < brightPoints.length; i++) {
        if (brightPoints[i].connected) continue;

        let closestPoint = null;
        let closestDistance = Infinity;

        // 找到最近的另一个亮点
        for (let j = 0; j < brightPoints.length; j++) {
            if (i === j) continue;

            const distance = calculateDistance(brightPoints[i], brightPoints[j]);
            if (distance < closestDistance) {
                closestDistance = distance;
                closestPoint = brightPoints[j];
            }
        }

        // 如果找到，就连接它们
        if (closestPoint) {
            linePositions.push(brightPoints[i].x, brightPoints[i].y, brightPoints[i].z);
            linePositions.push(closestPoint.x, closestPoint.y, closestPoint.z);

            lineColors.push(brightColor.r, brightColor.g, brightColor.b);
            lineColors.push(brightColor.r, brightColor.g, brightColor.b);

            brightPoints[i].connected = true;
            closestPoint.connected = true;
        }
    }

    // 处理普通点 - 每个点连接到最近的未连接点
    for (let i = 0; i < pointPositions.length; i++) {
        const point = pointPositions[i];
        if (point.connected) continue;

        let closestPoint = null;
        let closestDistance = Infinity;

        // 先尝试连接到亮点
        for (let j = 0; j < pointPositions.length; j++) {
            if (i === j) continue;

            const otherPoint = pointPositions[j];
            if (otherPoint.isBright && !otherPoint.connected) {
                const distance = calculateDistance(point, otherPoint);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestPoint = otherPoint;
                }
            }
        }

        // 如果没有找到亮点，就连接到任何未连接的点
        if (!closestPoint) {
            for (let j = 0; j < pointPositions.length; j++) {
                if (i === j) continue;

                const otherPoint = pointPositions[j];
                if (!otherPoint.connected) {
                    const distance = calculateDistance(point, otherPoint);
                    if (distance < closestDistance) {
                        closestDistance = distance;
                        closestPoint = otherPoint;
                    }
                }
            }
        }

        // 如果找到了最近点，就连接它们
        if (closestPoint) {
            linePositions.push(point.x, point.y, point.z);
            linePositions.push(closestPoint.x, closestPoint.y, closestPoint.z);

            // 如果任一端点是亮点，则线也更亮
            const useColor = (point.isBright || closestPoint.isBright) ? brightColor : normalColor;
            lineColors.push(useColor.r, useColor.g, useColor.b);
            lineColors.push(useColor.r, useColor.g, useColor.b);

            point.connected = true;
            closestPoint.connected = true;
        }
    }

    // 确保所有点至少连接一条线 - 连接到最近的已连接点
    for (let i = 0; i < pointPositions.length; i++) {
        const point = pointPositions[i];
        if (point.connected) continue;

        let closestPoint = null;
        let closestDistance = Infinity;

        for (let j = 0; j < pointPositions.length; j++) {
            if (i === j) continue;

            const otherPoint = pointPositions[j];
            const distance = calculateDistance(point, otherPoint);
            if (distance < closestDistance) {
                closestDistance = distance;
                closestPoint = otherPoint;
            }
        }

        if (closestPoint) {
            linePositions.push(point.x, point.y, point.z);
            linePositions.push(closestPoint.x, closestPoint.y, closestPoint.z);

            const useColor = (point.isBright || closestPoint.isBright) ? brightColor : normalColor;
            lineColors.push(useColor.r, useColor.g, useColor.b);
            lineColors.push(useColor.r, useColor.g, useColor.b);

            point.connected = true;
        }
    }

    // 添加经纬网格主要线条 - 简化为十字线
    // 创建赤道线
    const equatorPoints = 36;  // 赤道上的点数量
    const equatorRadius = worldRadius + config.height;

    for (let i = 0; i < equatorPoints; i++) {
        if (i % 4 !== 0) continue; // 只保留部分点以减少密度

        const angle1 = (i / equatorPoints) * Math.PI * 2;
        const angle2 = ((i + 1) % equatorPoints / equatorPoints) * Math.PI * 2;

        const x1 = equatorRadius * Math.cos(angle1);
        const z1 = equatorRadius * Math.sin(angle1);

        const x2 = equatorRadius * Math.cos(angle2);
        const z2 = equatorRadius * Math.sin(angle2);

        linePositions.push(x1, 0, z1);
        linePositions.push(x2, 0, z2);

        lineColors.push(normalColor.r, normalColor.g, normalColor.b);
        lineColors.push(normalColor.r, normalColor.g, normalColor.b);
    }

    // 添加几条主要经线
    const meridianCount = 6;  // 经线数量

    for (let m = 0; m < meridianCount; m++) {
        const longitude = (m / meridianCount) * Math.PI * 2;

        // 只添加南北极连线
        const x1 = equatorRadius * Math.cos(longitude);
        const z1 = equatorRadius * Math.sin(longitude);

        linePositions.push(0, -equatorRadius, 0); // 南极
        linePositions.push(x1, 0, z1); // 赤道点

        linePositions.push(x1, 0, z1); // 赤道点
        linePositions.push(0, equatorRadius, 0); // 北极

        lineColors.push(normalColor.r, normalColor.g, normalColor.b);
        lineColors.push(normalColor.r, normalColor.g, normalColor.b);
        lineColors.push(normalColor.r, normalColor.g, normalColor.b);
        lineColors.push(normalColor.r, normalColor.g, normalColor.b);
    }

    // 创建线的几何体和材质
    lineGeometry.setAttribute('position', new THREE.Float32BufferAttribute(linePositions, 3));
    lineGeometry.setAttribute('color', new THREE.Float32BufferAttribute(lineColors, 3));

    const lineMaterial = new THREE.LineBasicMaterial({
        vertexColors: true,
        transparent: true,
        opacity: config.lineOpacity
    });

    // 创建线的网格
    const networkLines = new THREE.LineSegments(lineGeometry, lineMaterial);
    networkLines.name = 'global-network-lines';
    scene.add(networkLines);

    // 存储配置和点位置，用于动画
    networkPoints.userData = {
        config: config,
        positions: pointPositions,
        initialPositions: pointPositions.map(p => ({ x: p.x, y: p.y, z: p.z }))
    };

    // 将引用存储在scene.userData中，便于在动画中访问
    scene.userData.globalNetwork = {
        points: networkPoints,
        lines: networkLines
    };
}

// 计算两点之间的距离
function calculateDistance(point1, point2) {
    return Math.sqrt(
        Math.pow(point1.x - point2.x, 2) +
        Math.pow(point1.y - point2.y, 2) +
        Math.pow(point1.z - point2.z, 2)
    );
}

// 更新全球网络动画
function updateGlobalNetwork() {
    if (scene.userData.globalNetwork && scene.userData.globalNetwork.points) {
        const networkPoints = scene.userData.globalNetwork.points;
        const positions = networkPoints.geometry.attributes.position.array;
        const userData = networkPoints.userData;
        const config = userData.config;
        const pointPositions = userData.positions;
        const initialPositions = userData.initialPositions;

        // 更新每个点的位置（轻微脉动）
        for (let i = 0; i < pointPositions.length; i++) {
            const point = pointPositions[i];
            const index = i * 3;

            // 根据点的相位产生轻微脉动
            point.phase += config.animationSpeed;
            const pulseFactor = 1 + 0.03 * Math.sin(point.phase); // 3%脉动

            // 更新点的位置
            positions[index] = initialPositions[i].x * pulseFactor;
            positions[index + 1] = initialPositions[i].y * pulseFactor;
            positions[index + 2] = initialPositions[i].z * pulseFactor;
        }

        // 标记几何体需要更新
        networkPoints.geometry.attributes.position.needsUpdate = true;
    }
}

// 添加国家标注
function addCountries() {
    try {
        // 创建标签渲染器
        labelRenderer = new THREE.CSS2DRenderer();
        labelRenderer.setSize(window.innerWidth, window.innerHeight);
        labelRenderer.domElement.style.position = 'absolute';
        labelRenderer.domElement.style.top = '0';
        labelRenderer.domElement.style.pointerEvents = 'none';
        document.getElementById('container').appendChild(labelRenderer.domElement);

        // 国家数据 [名称, 经度, 纬度]
        const countryData = [
            ['中国', 116.4, 39.9],
            ['美国', -98.5, 39.8],
            ['俄罗斯', 105.3, 61.5],
            ['巴西', -51.9, -14.2],
            ['印度', 78.9, 20.6],
            ['澳大利亚', 133.8, -25.3],
            ['英国', -3.4, 55.4],
            ['日本', 138.2, 36.2],
            ['法国', 2.2, 46.2],
            ['德国', 10.5, 51.2],
            ['加拿大', -106.3, 56.1],
            ['埃及', 30.8, 26.8],
            ['南非', 22.9, -30.6],
            ['墨西哥', -102.6, 23.6],
            ['阿根廷', -63.6, -38.4],
            ['意大利', 12.6, 41.9],
            ['韩国', 127.8, 35.9],
            ['沙特阿拉伯', 45.1, 23.9],
            ['土耳其', 35.2, 38.9],
            ['印尼', 113.9, -0.8],
            ['尼日利亚', 8.7, 9.1],
            ['新西兰', 174.9, -40.9],
            ['越南', 108.3, 14.1],
            ['泰国', 100.9, 15.9],
            ['西班牙', -3.7, 40.5]
        ];

        // 遍历国家数据并添加标签
        countryData.forEach(country => {
            const [name, longitude, latitude] = country;
            const position = latLonToVector3(latitude, longitude, worldRadius);

            // 创建HTML元素
            const div = document.createElement('div');
            div.className = 'country-label';
            div.textContent = name;
            div.style.color = '#ffffff';
            div.style.backgroundColor = 'rgba(0,30,90,0.8)';
            div.style.padding = '2px 5px';
            div.style.borderRadius = '3px';
            div.style.fontSize = '12px';
            div.style.pointerEvents = 'auto';
            div.style.cursor = 'pointer';

            // 添加点击事件
            div.addEventListener('click', () => {
                onCountryClick(name, position);
            });

            // 创建CSS2D对象
            const label = new THREE.CSS2DObject(div);
            label.position.copy(position);
            scene.add(label);

            // 存储标签引用
            countries[name] = label;
        });
    } catch (e) {
        console.error('添加国家标注时出错:', e);
    }
}

// 经纬度转3D坐标
function latLonToVector3(lat, lon, radius) {
    const phi = (90 - lat) * Math.PI / 180;
    const theta = (lon + 180) * Math.PI / 180;

    return new THREE.Vector3(
        -radius * Math.sin(phi) * Math.cos(theta),
        radius * Math.cos(phi),
        radius * Math.sin(phi) * Math.sin(theta)
    );
}

// 国家点击事件
function onCountryClick(name, position) {
    console.log(`点击了${name}`);

    // 禁用自动旋转
    controls.autoRotate = false;

    // 计算相机目标位置
    const lookAtTarget = position.clone().normalize().multiplyScalar(worldRadius);

    // 计算相机位置
    const cameraPosition = position.clone().normalize().multiplyScalar(worldRadius + 1);

    // 使用TWEEN动画移动相机
    if (isTweenAvailable()) {
        new TWEEN.Tween(controls.target)
            .to({ x: lookAtTarget.x, y: lookAtTarget.y, z: lookAtTarget.z }, 1000)
            .easing(TWEEN.Easing.Cubic.Out)
            .start();

        new TWEEN.Tween(camera.position)
            .to({ x: cameraPosition.x, y: cameraPosition.y, z: cameraPosition.z }, 1000)
            .easing(TWEEN.Easing.Cubic.Out)
            .start();
    } else {
        // 如果TWEEN不可用，则直接设置位置
        controls.target.set(lookAtTarget.x, lookAtTarget.y, lookAtTarget.z);
        camera.position.set(cameraPosition.x, cameraPosition.y, cameraPosition.z);
    }

    // 安全地通知父窗口
    try {
        if (window.parent) {
            window.parent.postMessage({ type: "clickCountry", data: { name } }, "*");
        }
    } catch (e) {
        console.log('无法通知父窗口点击事件', e);
    }
}

// 窗口大小变化处理
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
    if (labelRenderer) {
        labelRenderer.setSize(window.innerWidth, window.innerHeight);
    }
}

// 检查标签可见性
function updateLabelVisibility() {
    for (const name in countries) {
        const label = countries[name];
        const position = label.position.clone();

        // 计算标签到相机的方向
        const directionToCamera = new THREE.Vector3().subVectors(camera.position, position);

        // 计算标签位置的法向量(从地球中心指向标签)
        const normalVector = position.clone().normalize();

        // 计算点积，用于判断标签是否在可见面
        const dotProduct = normalVector.dot(directionToCamera);

        // 设置透明度
        label.element.style.opacity = dotProduct > 0 ? 1 : 0;
    }
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);
    controls.update();

    // 只有当TWEEN可用时才更新
    if (isTweenAvailable()) {
        TWEEN.update();
    }

    // 更新全球网络动画
    updateGlobalNetwork();

    updateLabelVisibility();
    renderer.render(scene, camera);
    if (labelRenderer) {
        labelRenderer.render(scene, camera);
    }
}

// 资源释放
function dispose() {
    // 释放几何体和材质
    if (earth && earth.geometry) earth.geometry.dispose();
    if (earth && earth.material) earth.material.dispose();

    // 移除标签
    for (const name in countries) {
        scene.remove(countries[name]);
        if (countries[name].element) {
            countries[name].element.remove();
        }
    }

    // 清空引用
    countries = {};

    // 停止动画
    if (controls) controls.autoRotate = false;
}

// 初始化场景
window.addEventListener('DOMContentLoaded', init); 