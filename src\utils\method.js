//获取图片
export function getImg(url) {
  return new URL(`../assets/images/${url}`, import.meta.url).href;
}
//计算百分比
export const getPercent = (value, total, decimals) => {
  if (total === 0) return "0"; // 防止除以零的错误
  let percent = (value / total) * 100;
  return percent.toFixed(decimals); // 返回格式化后的百分比值
};
//给数字加，
export const formatNumber = (num) => {
  let [integerPart, decimalPart] = num.toString().split(".");

  if (integerPart.length >= 4) {
    integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  if (decimalPart) {
    return `${integerPart}.${decimalPart}`;
  }
  return integerPart;
};

export const hex2Rgba = (bgColor, alpha = 1) => {
  let color = bgColor.slice(1); // 去掉'#'号
  let rgba = [
    parseInt("0x" + color.slice(0, 2)),
    parseInt("0x" + color.slice(2, 4)),
    parseInt("0x" + color.slice(4, 6)),
    alpha,
  ];
  return "rgba(" + rgba.toString() + ")";
};
export const numberToThousands = (number, decimalPlaces = 0) => {
  const numStr = String(number).replace(/,/g, "");
  const num = parseFloat(numStr);
  if (isNaN(num)) return "Invalid Number";
  const n = Math.max(0, Math.floor(Math.abs(decimalPlaces)));
  const rounded = n > 0 ? num.toFixed(n) : Math.round(num).toString();
  let [integer, decimal = ""] = rounded.split(".");
  integer = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  if (n > 0) {
    decimal = decimal.padEnd(n, "0").slice(0, n);
  }
  return n > 0 ? `${integer}.${decimal}` : integer;
};


export const filterString = (str) => {
  // 将str中的,去掉
  if (str) {
    return str.replace(/,/g, "");
  } else {
    return 0;
  }
};

