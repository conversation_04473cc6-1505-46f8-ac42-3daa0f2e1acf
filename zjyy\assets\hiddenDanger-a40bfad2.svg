<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame" clip-path="url(#clip0_126_4327)">
<g id="Vector" opacity="0.3" filter="url(#filter0_i_126_4327)">
<path d="M28.5576 23.9931L21.6951 5.70879C20.8556 3.47323 18.9299 3.1534 17.4131 4.9976L5.00845 20.0819C3.4913 21.9282 4.17838 23.7539 6.53276 24.145L25.8017 27.3453C28.1561 27.7363 29.3955 26.2304 28.5576 23.9931ZM17.4447 9.86743C17.853 9.55064 18.3242 9.43657 18.861 9.52572C19.398 9.61491 19.807 9.87315 20.0923 10.2995C20.3752 10.7262 20.4705 11.2171 20.3782 11.7729C20.2988 12.2511 18.9961 15.6483 18.3315 18.1668L16.6005 17.8793C16.8152 15.286 16.6195 11.64 16.6989 11.1618C16.7899 10.6144 17.0387 10.1827 17.4447 9.86743ZM18.0464 22.4676C17.6234 22.7618 17.1607 22.8665 16.6592 22.7832C16.1578 22.7 15.7537 22.4513 15.4486 22.0362C15.1443 21.6221 15.035 21.1636 15.1186 20.6603C15.2017 20.1596 15.4541 19.7564 15.8775 19.4536C16.3019 19.1509 16.7655 19.0413 17.2669 19.1246C17.7684 19.2079 18.1715 19.4614 18.4753 19.8851C18.7778 20.3084 18.8868 20.7716 18.8036 21.2723C18.72 21.7756 18.468 22.1741 18.0464 22.4676Z" fill="url(#paint0_linear_126_4327)"/>
</g>
<g id="Vector_2" filter="url(#filter1_i_126_4327)">
<path d="M28.3664 23.6932L18.6008 6.78034C17.4064 4.71254 15.4543 4.71254 14.2601 6.78034L4.49458 23.6932C3.30043 25.7631 4.27737 27.4516 6.664 27.4516H26.1968C28.5836 27.4516 29.5595 25.763 28.3664 23.6932ZM15.0892 11.5792C15.4401 11.1998 15.8862 11.01 16.4304 11.01C16.9748 11.01 17.4205 11.1978 17.7718 11.5716C18.1208 11.9463 18.2952 12.4148 18.2952 12.9783C18.2952 13.463 17.5668 17.0277 17.3238 19.6211H15.5691C15.356 17.0277 14.5656 13.463 14.5656 12.9783C14.5656 12.4234 14.7403 11.9567 15.0892 11.5792ZM17.7472 23.9105C17.3781 24.27 16.9389 24.4491 16.4305 24.4491C15.9222 24.4491 15.4829 24.27 15.1138 23.9105C14.7458 23.5519 14.5629 23.1175 14.5629 22.6073C14.5629 22.0997 14.7458 21.6606 15.1138 21.2926C15.4829 20.9244 15.9222 20.7404 16.4305 20.7404C16.9389 20.7404 17.3781 20.9244 17.7472 21.2926C18.115 21.6606 18.2984 22.0997 18.2984 22.6073C18.2984 23.1175 18.115 23.5519 17.7472 23.9105Z" fill="url(#paint1_linear_126_4327)"/>
</g>
</g>
<defs>
<filter id="filter0_i_126_4327" x="4.20166" y="3.79883" width="24.6182" height="24.3869" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.778538"/>
<feGaussianBlur stdDeviation="0.389269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.723839 0 0 0 0 0.779072 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_126_4327"/>
</filter>
<filter id="filter1_i_126_4327" x="3.98486" y="5.22949" width="24.8906" height="23.0002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.778538"/>
<feGaussianBlur stdDeviation="0.389269"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.723839 0 0 0 0 0.779072 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_126_4327"/>
</filter>
<linearGradient id="paint0_linear_126_4327" x1="19.808" y1="3.82326" x2="16.167" y2="25.7451" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE2DB"/>
<stop offset="1" stop-color="#FF604A" stop-opacity="0.8"/>
</linearGradient>
<linearGradient id="paint1_linear_126_4327" x1="16.4302" y1="5.22949" x2="16.4302" y2="27.4516" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE2DB"/>
<stop offset="1" stop-color="#FF604A" stop-opacity="0.8"/>
</linearGradient>
<clipPath id="clip0_126_4327">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
