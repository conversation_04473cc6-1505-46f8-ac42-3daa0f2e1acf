import request from '@/utils/request';

/**
 * Token管理器
 * 负责管理萤石云Token和视频Token的缓存和定时刷新
 */
class TokenManager {
  constructor() {
    // Token缓存
    this.tokens = {
      hikToken: null,
      videoToken: null
    };
    
    // Token过期时间
    this.tokenExpires = {
      hikToken: null,
      videoToken: null
    };
    
    // 定时器
    this.refreshTimers = {
      hikToken: null,
      videoToken: null
    };
    
    // Token有效期（25分钟，提前5分钟刷新）
    this.tokenTTL = 25 * 60 * 1000; // 25分钟
  }

  /**
   * 获取萤石云Token
   * @returns {Promise<string>}
   */
  async getHikToken() {
    const now = Date.now();
    
    // 检查token是否有效
    if (this.tokens.hikToken && this.tokenExpires.hikToken && now < this.tokenExpires.hikToken) {
      console.log('🎯 使用缓存的萤石云Token');
      return this.tokens.hikToken;
    }

    // 获取新的token
    try {
      console.log('🔄 正在获取新的萤石云Token...');
      const response = await request.get('/globalManage/zjmanage/largescreen/getHikToken');
      
      if (response.code === 0) {
        this.tokens.hikToken = response.data;
        this.tokenExpires.hikToken = now + this.tokenTTL;
        
        // 设置定时刷新
        this.scheduleTokenRefresh('hikToken');
        
        console.log('✅ 萤石云Token获取成功，将在25分钟后刷新');
        return this.tokens.hikToken;
      } else {
        throw new Error(response.msg || '获取萤石云Token失败');
      }
    } catch (error) {
      console.error('❌ 获取萤石云Token失败:', error);
      throw error;
    }
  }

  /**
   * 获取视频Token
   * @returns {Promise<string>}
   */
  async getVideoToken() {
    const now = Date.now();
    
    // 检查token是否有效
    if (this.tokens.videoToken && this.tokenExpires.videoToken && now < this.tokenExpires.videoToken) {
      console.log('🎯 使用缓存的视频Token');
      return this.tokens.videoToken;
    }

    // 获取新的token
    try {
      console.log('🔄 正在获取新的视频Token...');
      const response = await request.get('/globalManage/zjmanage/largescreen/getToken');
      
      if (response.code === 0) {
        this.tokens.videoToken = response.data;
        this.tokenExpires.videoToken = now + this.tokenTTL;
        
        // 设置定时刷新
        this.scheduleTokenRefresh('videoToken');
        
        console.log('✅ 视频Token获取成功，将在25分钟后刷新');
        return this.tokens.videoToken;
      } else {
        throw new Error(response.msg || '获取视频Token失败');
      }
    } catch (error) {
      console.error('❌ 获取视频Token失败:', error);
      throw error;
    }
  }

  /**
   * 安排Token定时刷新
   * @param {string} tokenType - token类型
   */
  scheduleTokenRefresh(tokenType) {
    // 清除之前的定时器
    if (this.refreshTimers[tokenType]) {
      clearTimeout(this.refreshTimers[tokenType]);
    }

    // 设置新的定时器，在token过期前5分钟刷新
    this.refreshTimers[tokenType] = setTimeout(async () => {
      try {
        console.log(`🔄 定时刷新${tokenType}...`);
        if (tokenType === 'hikToken') {
          await this.getHikToken();
        } else if (tokenType === 'videoToken') {
          await this.getVideoToken();
        }
      } catch (error) {
        console.error(`❌ 定时刷新${tokenType}失败:`, error);
        // 刷新失败，5分钟后重试
        setTimeout(() => {
          this.scheduleTokenRefresh(tokenType);
        }, 5 * 60 * 1000);
      }
    }, this.tokenTTL - 5 * 60 * 1000); // 提前5分钟刷新
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    console.log('🧹 清理Token管理器资源...');
    
    // 清除所有定时器
    Object.values(this.refreshTimers).forEach(timer => {
      if (timer) clearTimeout(timer);
    });
    
    // 重置状态
    this.tokens = { hikToken: null, videoToken: null };
    this.tokenExpires = { hikToken: null, videoToken: null };
    this.refreshTimers = { hikToken: null, videoToken: null };
    
    console.log('✅ Token管理器资源清理完成');
  }

  /**
   * 强制刷新指定Token
   * @param {string} tokenType - token类型
   */
  async forceRefreshToken(tokenType) {
    // 清除缓存
    this.tokens[tokenType] = null;
    this.tokenExpires[tokenType] = null;
    
    // 获取新token
    if (tokenType === 'hikToken') {
      return await this.getHikToken();
    } else if (tokenType === 'videoToken') {
      return await this.getVideoToken();
    }
  }
}

// 创建单例实例
const tokenManager = new TokenManager();

export default tokenManager; 