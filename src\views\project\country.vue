<template>
  <div
    class="project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between">
    <div
      class="left-container pointer-events-auto w-[442px] h-[922px] bg-[url('@/assets/images/xiangmu/bg-left.png')] bg-no-repeat bg-cover rotate-perspective">
      <div class="w-[442px] h-[922px] py-[25px] box-border rotate-item1">
        <div class="w-full text-center text-[22px] font-family-youshebiaotihei h-[33px] leading-[33px]">
          在建项目总览
        </div>
        <div class="w-full h-[100px] box-border flex flex-row flex-wrap">
          <div class="w-1/2 h-[100px] flex items-end">
            <img class="w-[111px] h-[100px]" src="@/assets/images/xiangmu/left-img1.png" alt="" srcset="" />
            <div class="ml-[6px]">
              <div class="w-[150px] font-light text-[14px] mt-[-30px]">
                在建项目总数
              </div>
              <div class="text-[14px]">
                <span
                  class="text-[#a3d2eb] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">11</span>个
              </div>
            </div>
          </div>
          <div class="w-1/2 h-[100px] flex items-end ml-[-26px]">
            <img class="w-[111px] h-[100px]" src="@/assets/images/xiangmu/left-img2.png" alt="" srcset="" />
            <div class="ml-[6px]">
              <div class="w-[150px] font-light text-[14px] mt-[-30px]">
                在建项目合同总额
              </div>
              <div class="text-[14px]">
                <span
                  class="text-[#a3d2eb] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">934,084</span>万元
              </div>
            </div>
          </div>
        </div>
        <div class="w-full h-[220px] box-border flex flex-row flex-wrap">
          <div class="w-[220px] h-[200px] flex justify-center items-center">
            <Pie3DChart2 :data="pieData2" width="220px" height="220px" />
          </div>
          <div class="w-[220px] h-[200px] flex justify-center items-center">
            <Pie3DChart2 :data="pieData" width="220px" height="220px" />
          </div>
        </div>
        <div class="flex items-end h-[150px] justify-evenly mt-[50px]">
          <div class="flex flex-col items-center">
            <div class="text-[14px]">
              <span
                class="text-[#a3d2eb] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">441,150</span>万元
            </div>
            <img src="@/assets/images/xiangmu/left-img3.png" alt="" srcset="" />
            <div class="items-center font-light text-[16px] mt-[-30px]">
              工程款收入
            </div>
          </div>
          <div class="w-[240px]">
            <div
              class="w-[240px] h-[21px] bg-[url('@/assets/images/xiangmu/left-img4.png')] bg-no-repeat bg-cover flex justify-between items-center">
              <span class="text-[12px] font-light ml-[26px]">本年预算</span>
              <div class="text-[14px] mt-[-10px]">
                <span
                  class="text-[#a3d2eb] text-[20px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">500,000</span>万元
              </div>
            </div>
            <div class="w-[240px] h-[20px] flex justify-between items-center mt-[14px]">
              <div class="">完成度</div>
              <div class="text-[14px] text-[#a3d2eb] leading-[50px] font-family-oswald-medium tracking-[1.3px]">
                <span class="text-[18px]">88.23</span><span>%</span>
              </div>
            </div>
            <div class="relative w-[240px] h-[8px] bg-[#fff]/[0.1] rounded-[4px] mt-[10px]">
              <div class="jindu-container-green absolute top-0 left-0 w-[211px] h-[8px] rounded-[4px]"></div>
            </div>
            <div class="w-[240px] h-[20px] flex justify-between items-center mt-[14px]">
              <div class="">同比增长</div>
              <div class="text-[14px] text-[#a3d2eb] leading-[50px] font-family-oswald-medium tracking-[1.3px]">
                <span class="text-[18px]">+21.44</span><span>%</span>
              </div>
            </div>
            <div class="relative w-[240px] h-[8px] bg-[#fff]/[0.1] rounded-[4px] mt-[10px]">
              <div class="jindu-container-green absolute top-0 left-0 w-[51px] h-[8px] rounded-[4px]"></div>
            </div>
          </div>
        </div>
        <div class="w-full h-[300px] flex justify-center items-center ml-[10px] mt-[50px]">
          <StaffChart2 title="人员总数" total="14664" :data="pieChartData" />
        </div>
      </div>
    </div>
    <div
      class="right-container pointer-events-auto w-[442px] h-[922px] bg-[url('@/assets/images/xiangmu/bg-right.png')] bg-no-repeat bg-cover rotate-perspective">
      <div class="w-[442px] h-[922px] py-[25px] box-border rotate-item2">
        <div class="w-full text-center text-[22px] font-family-youshebiaotihei h-[33px] leading-[33px]">
          在建项目安全质量检查
        </div>
        <div class="w-full h-[100px] box-border flex flex-row flex-wrap">
          <div class="w-1/2 h-[100px] flex items-end">
            <img class="w-[111px] h-[100px]" src="@/assets/images/xiangmu/right-img1.png" alt="" srcset="" />
            <div class="ml-[6px]">
              <div class="w-[150px] font-light text-[14px] mt-[-30px]">
                排查记录
              </div>
              <div class="text-[14px]">
                <span
                  class="text-[#a3d2eb] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">876</span>
              </div>
            </div>
          </div>
          <div class="w-1/2 h-[100px] flex items-end ml-[-26px]">
            <img class="w-[111px] h-[100px]" src="@/assets/images/xiangmu/right-img2.png" alt="" srcset="" />
            <div class="ml-[6px]">
              <div class="w-[150px] font-light text-[14px] mt-[-30px]">
                隐患记录
              </div>
              <div class="text-[14px]">
                <span
                  class="text-[#da3832] text-[26px] leading-[50px] font-family-oswald-medium tracking-[1.3px] mr-[10px]">36,059</span>
              </div>
            </div>
          </div>
        </div>
        <div class="w-full h-[300px] flex justify-center items-center ml-[10px]">
          <StaffChart2 title="质量问题总数" total="211" :data="pieChart2Data" />
        </div>
        <div class="w-full h-[120px] flex justify-evenly items-center ml-[10px]">
          <div class="flex items-center">
            <div
              class="w-[120px] h-[120px] bg-[url('@/assets/images/xiangmu/waterball-bg1.png')] bg-no-repeat bg-cover flex justify-center items-center">
              <WaterBall width="110px" height="110px" value="0.89" waveColor="#6deaa5" />
            </div>
            <div class="ml-[10px]">
              <div class="text-[14px]">
                <span
                  class="text-[26px] text-[#a3d2eb] leading-[50px] font-family-oswald-medium tracking-[1.3px]">89</span><span>%</span>
              </div>
              <div class="text-[14px] font-light">整改率</div>
            </div>
          </div>
          <div class="flex items-center">
            <div
              class="w-[120px] h-[120px] bg-[url('@/assets/images/xiangmu/waterball-bg1.png')] bg-no-repeat bg-cover flex justify-center items-center">
              <WaterBall width="110px" height="110px" value="0.53" waveColor="#f0b354" />
            </div>
            <div class="ml-[10px]">
              <div class="text-[14px]">
                <span
                  class="text-[26px] text-[#f0b354] leading-[50px] font-family-oswald-medium tracking-[1.3px]">53</span><span>%</span>
              </div>
              <div class="text-[14px] font-light">整改及时率</div>
            </div>
          </div>
        </div>
        <div class="relative w-[400px] h-[300px] flex justify-center items-center ml-[10px]">
          <div
            class="absolute top-[10px] left-0 w-[391px] h-[27px] bg-[url('@/assets/images/zichan/title-bg.png')] bg-no-repeat bg-cover text-[14px] font-family-youshebiaotihei leading-[30px] pl-[30px] box-border">
            年质量问题及整改趋势
          </div>
          <LineChart :data="chartDate" :legend="legend" :xData="xData" width="100%" height="300px" />
        </div>
      </div>
    </div>
    <img class="pointer-events-auto cursor-pointer absolute top-[30px] left-[60px]"
      src="@/assets/images/xiangmu/back.png" alt="" srcset="" @click="back" />
  </div>
</template>
<script setup>
import { ref, onMounted } from "vue";
import StaffChart2 from "@/components/StaffChart2.vue";
import WaterBall from "@/components/WaterBall.vue";
import LineChart from "@/components/LineChart.vue";
import Pie3DChart2 from "@/components/Pie3DChart2.vue";
import { useRouter } from "vue-router";

const router = useRouter();
const iframe = ref(document.getElementById("iframe"));

const pieChartData = ref([
  {
    value: 2482,
    name: "正式员工",
    itemStyle: { color: "rgba(0, 255, 187, 1)" },
  },
  {
    value: 4168,
    name: "劳务派遣",
    itemStyle: { color: "rgba(224, 224, 224, 1)" },
  },
  {
    value: 1073,
    name: "其他用工形式人员",
    itemStyle: { color: "rgba(74, 144, 226, 1)" },
  },
  {
    value: 973,
    name: "合作单位人员",
    itemStyle: { color: "rgba(0, 216, 255, 1)" },
  },
]);

const pieChart2Data = ref([
  {
    value: 56,
    name: "待整改",
    itemStyle: { color: "rgba(217, 59, 53, 1)" },
  },
  {
    value: 79,
    name: "合格",
    itemStyle: { color: "rgba(0, 255, 187, 1)" },
  },
  {
    value: 27,
    name: "待核验",
    itemStyle: { color: "rgba(0, 216, 255, 1)" },
  },
  {
    value: 49,
    name: "已整改",
    itemStyle: { color: "rgba(255, 159, 67, 1)" },
  },
]);

const legend = ref(["问题数", "整改数"]);
const xData = ref([
  "3月",
  "4月",
  "5月",
  "6月",
  "7月",
  "8月",
  "9月",
  "10月",
  "11月",
  "12月",
  "1月",
  "2月",
]);
const chartDate = ref([
  {
    name: "问题数",
    itemStyle: { color: "#74fbfd" },
    data: [180, 120, 110, 150, 170, 240, 250, 350, 360, 320, 280, 180],
  },
  {
    name: "整改数",
    itemStyle: { color: "#d93b35" },
    data: [130, 100, 120, 140, 160, 220, 230, 280, 280, 260, 240, 180],
  },
]);

const pieData = ref([
  {
    value: 127,
    name: "已实施",
    unit: "个",
    itemStyle: { color: "rgba(85,184,242, 1)" },
  },
  {
    value: 14,
    name: "待实施",
    unit: "个",
    itemStyle: { color: "rgba(114,273,176, 1)" },
  },
]);

const pieData2 = ref([
  {
    value: 9,
    name: "正常",
    unit: "个",
    itemStyle: { color: "rgba(114,273,176, 1)" },
  },
  {
    value: 2,
    name: "异常",
    unit: "个",
    itemStyle: { color: "rgba(233, 54, 40, 1)" },
  },
]);

onMounted(() => {
  iframe.value = document.getElementById("iframe");
  window.addEventListener("message", (e) => {
    if (e.data.type === "clickProject") {
      console.log(e.data.data);
      localStorage.setItem("clickProject", e.data.data);
      router.push({
        path: "/projectDetail",
      });
    }
  });
});

const back = () => {
  localStorage.removeItem("clickCountry");
  router.replace("/project/index");
};
</script>
<style scoped lang="scss">
.project-container {
  pointer-events: none;
  z-index: 2;

  .left-container {
    background: url("@/assets/images/xiangmu/bg-left.png") no-repeat center center / 100% 100%,
      url("@/assets/images/xiangmu/left-bg.png") no-repeat center center / 100% 100%;
  }

  .right-container {
    background: url("@/assets/images/xiangmu/bg-right.png") no-repeat center center / 100% 100%,
      url("@/assets/images/xiangmu/right-bg.png") no-repeat center center / 100% 100%;
  }

  .rotate-perspective {
    perspective: 800px;

    .rotate-item1 {
      transform: rotate3d(0, 1, 0, 5.5deg);
    }

    .rotate-item2 {
      transform: rotate3d(0, 1, 0, -5.5deg);
    }
  }
}
</style>
