<template>
  <div class="chart-container">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
const chart = ref(null)

const initChart = () => {
  if (!chartRef.value) return
  
  chart.value = echarts.init(chartRef.value)
  
  const option = {
    backgroundColor: 'transparent',
    // title: {
    //   text: '14,664',
    //   subtext: '人员总数',
    //   left: 'center',
    //   top: 'center',
    //   textStyle: {
    //     color: '#fff',
    //     fontSize: 24,
    //     fontFamily: 'Oswald-Medium',
    //     marginTop: -10,
    //   },
    //   subtextStyle: {
    //     color: '#fff',
    //     fontSize: 14
    //   }
    // },
    series: [
      {
        type: 'pie',
        radius: ['45%', '65%'],
        center: ['50%', '50%'],
        startAngle: 90,
        data: [
          { value: 2482, name: '正式员工', itemStyle: { color: '#00ffbb' } },
          { value: 5968, name: '海外属地员工', itemStyle: { color: '#ff9f43' } },
          { value: 4168, name: '劳务派遣', itemStyle: { color: '#808080' } },
          { value: 1073, name: '其他用工形式人员', itemStyle: { color: '#4a90e2' } },
          { value: 973, name: '合作单位人员', itemStyle: { color: '#00d8ff' } }
        ],
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}\n{c}人',
          color: '#fff',
          fontSize: 14,
          lineHeight: 20
        },
        labelLine: {
          show: true,
          length: 20,
          length2: 30,
          lineStyle: {
            color: '#ffffff55'
          }
        }
      }
    ]
  }
  
  chart.value.setOption(option)
}

const handleResize = () => {
  if (chart.value) {
    chart.value.resize()
  }
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chart.value) {
    chart.value.dispose()
  }
})
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 250px;
  background: rgba(0,0,0,0);
  border-radius: 12px;
  background: url('@/assets/images/zl/top4-img.png') no-repeat center center;
}

.title {
  position: absolute;
  top: 20px;
  left: 20px;
  color: #fff;
  font-size: 24px;
  font-family: 'oswald-medium';
}

.chart {
  width: 100%;
  height: 100%;
}
</style> 