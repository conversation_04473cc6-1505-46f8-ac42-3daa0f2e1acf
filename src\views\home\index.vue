<template>
  <div>
    <video ref="videoRef"></video>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const videoRef = ref(null)
let isReversing = false
let reverseInterval = null

// 在组件挂载时添加监听
onMounted(() => {
  const video = videoRef.value
  if (video) {
    video.addEventListener('ended', handleVideoEnd)
  }
})

// 在组件卸载时清理
onUnmounted(() => {
  const video = videoRef.value
  if (video) {
    video.removeEventListener('ended', handleVideoEnd)
    if (reverseInterval) {
      clearInterval(reverseInterval)
      reverseInterval = null
    }
  }
})

// 处理视频结束事件
const handleVideoEnd = () => {
  const video = videoRef.value
  if (!video) return
  
  // 当正向播放结束时
  if (!isReversing) {
    // 开始倒放
    isReversing = true
    video.pause()
    
    // 设置到视频末尾
    video.currentTime = video.duration
    
    // 使用setInterval实现倒放效果
    reverseInterval = setInterval(() => {
      if (video.currentTime <= 0.03) {
        // 倒放到开头，清除interval并恢复正放
        clearInterval(reverseInterval)
        reverseInterval = null
        video.currentTime = 0
        isReversing = false
        video.play()
      } else {
        // 继续倒放
        video.currentTime -= 0.03 // 控制倒放速度
      }
    }, 20) // 控制倒放流畅度
  }
}
</script>