<template>
  <div class="chart-container">
    <!-- 上层饼图 -->
    <div
      class="chart chart-top"
      ref="echartRef1"
      :style="{ width: width, height: height }"
    ></div>
    <!-- 下层饼图 -->
    <div
      class="chart chart-bottom"
      ref="echartRef2"
      :style="{ width: width, height: height }"
    ></div>
    <div class="bg"></div>
  </div>
</template>

<script setup lang="ts">
  import * as echarts from "echarts";
  import "echarts-gl";
  import { getPie3D, getParametricEquation } from "./chart.js";
  import { ref, shallowRef, onMounted, nextTick, watch } from "vue";

  const props = defineProps({
    data: {
      type: Array,
      required: true,
      default: () => [],
    },
    internalDiameterRatio: {
      type: Number,
      default: 0,
    },
    distance: {
      type: Number,
      default: 180,
    },
    alpha: {
      type: Number,
      default: 26,
    },
    pieHeight: {
      type: Number,
      default: 150,
    },
    opacity: {
      type: Number,
      default: 0.6,
    },
    width: {
      type: Number,
      default: 140,
    },
    height: {
      type: Number,
      default: 140,
    },
  });

  const color = ["#4da7f1", "#7af6c3"];
  const optionData = ref(props.data);

  let option1 = {};
  let option2 = {};
  const echartRef1 = shallowRef<HTMLElement>();
  const echartRef2 = shallowRef<HTMLElement>();
  const echart1 = shallowRef<echarts.ECharts>();
  const echart2 = shallowRef<echarts.ECharts>();

  onMounted(() => {
    nextTick(() => {
      initCharts();
    });
    window.onresize = changeSize;
  });

  const initCharts = () => {
    if (optionData.value.length == 0) return;

    // 初始化上层饼图
    echart1.value = echarts.init(echartRef1.value);
    option1 = getPie3D(
      optionData.value,
      props.internalDiameterRatio,
      props.distance,
      props.alpha,
      props.pieHeight,
      props.opacity,
      1,
      optionData.value[0].unit
    );
console.log(option1);

    echart1.value.setOption(option1);

    // 初始化下层饼图
    echart2.value = echarts.init(echartRef2.value);
    option2 = getPie3D(
      optionData.value,
      props.internalDiameterRatio,
      props.distance,
      props.alpha,
      props.pieHeight,
      props.opacity,
      0,
      optionData.value[0].unit
    );

    // // 添加label配置
    // option2.series[0].label = {
    //   show: true,
    //   position: 'outside',
    //   alignTo: 'labelLine',
    //   distanceToLabelLine: 5,
    //   formatter: function (params) {
    //     if (params.name !== "") {
    //       return [
    //         `{a|${params.name}}\n{d|${params.value}}`
    //       ];
    //     } else {
    //       return "";
    //     }
    //   },
    //   rich: {
    //     a: {
    //       fontSize: 14,
    //       color: '#ffffff',
    //       fontWeight: 300,
    //       fontFamily: 'Regular',
    //       padding: [0, 0, 5, 0]
    //     },
    //     d: {
    //       fontSize: 20,
    //       fontWeight: 500,
    //       color: '#a1fcd7',
    //       fontFamily: 'Oswald Medium',
    //       padding: [5, 0, 0, 0]
    //     }
    //   }
    // };

    // option2.series[0].labelLine = {
    //   show: true,
    //   length: 15,
    //   length2: 50,
    //   minTurnAngle: 90,
    //   lineStyle: {
    //     color: '#ffffff',
    //     width: 1
    //   }
    // };

    echart2.value.setOption(option2);
  };

  const changeSize = () => {
    echart1.value && echart1.value.resize();
    echart2.value && echart2.value.resize();
  };

  // 添加对 props.data 的监听
  watch(
    () => props.data,
    (newData) => {
      optionData.value = newData;
      nextTick(() => {
        if (optionData.value.length == 0) return;

        // 重新初始化图表
        if (echart1.value && echart2.value) {
          // 更新上层饼图
          option1 = getPie3D(
            optionData.value,
            props.internalDiameterRatio,
            props.distance,
            props.alpha,
            props.pieHeight,
            props.opacity,
            1,
            optionData.value[0].unit
          );
          echart1.value.setOption(option1);

          // 更新下层饼图
          option2 = getPie3D(
            optionData.value,
            props.internalDiameterRatio,
            props.distance,
            props.alpha,
            props.pieHeight,
            props.opacity,
            0,
            optionData.value[0].unit
          );
          echart2.value.setOption(option2);
        }
      });
    },
    { deep: true }
  );
</script>
<style lang="scss" scoped>
  .chart-container {
    position: relative;
    width: 400px;
    height: 350px;
    perspective: 800px;
    overflow: visible; // 允许内容超出容器

    .chart {
      position: absolute;
      width: 100%;
      height: 150px;
      overflow: visible; // 允许内容超出容器
    }

    .chart-top {
      top: -13px;
      left: 5px;
      z-index: 2;
    }

    .chart-bottom {
      top: -5px;
      z-index: 1;
    }

    .bg {
      position: absolute;
      bottom: 28%;
      left: 50%;
      z-index: 0;
      width: 220px;
      height: 114px;
      background: no-repeat center;
      background-image: url("@/assets/images/xiangmu/3d-pie-bg.png");
      background-size: 100% 100%;
      transform: translateX(-50%);
    }
  }
</style>
