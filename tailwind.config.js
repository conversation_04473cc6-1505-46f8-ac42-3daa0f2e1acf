/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js, ts, jsx, tsx}",
  ],
  theme: {
    extend: {
      // text: {
      //   'text-shadow': "2px 2px 4px rgba(0, 0, 0, 0.5)"
      // },
      animation: {
        'slow': 'spin 5s linear infinite'
      },
      keyframes: {
        'slow': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(-360deg)' },
        },
      },
      fontFamily: {
        custom: ["Microsoft YaHei"],
        hyzyt: ["hyzyt"],
        led: ["led"]
      },
    },
  },
  plugins: [],
}

