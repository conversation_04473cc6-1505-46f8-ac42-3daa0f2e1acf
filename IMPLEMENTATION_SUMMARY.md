# 重点工程卡片切换功能实现总结

## 功能概述
在重点工程卡片的左上角添加了两个切换按钮：
- **国际工程**：播放视频内容，调用接口 `/globalManage/zjmanage/largescreen/allSpImportList`
- **国内工程**：播放监控内容，调用接口 `/globalManage/zjmanage/largescreen/allCameraImportList`

## 主要修改内容

### 1. 新增状态管理
```javascript
// 重点工程卡片的切换状态：'international' 或 'domestic'
const majorProjectType = ref('international');

// 重点工程数据相关状态
const majorInternationalProjects = ref([]);
const majorDomesticProjects = ref([]);
const isLoadingMajorData = ref(false);
```

### 2. 新增API接口函数
```javascript
// 获取重点工程国际项目列表
const fetchMajorInternationalProjects = async () => {
  // 调用 /globalManage/zjmanage/largescreen/allSpImportList
}

// 获取重点工程国内项目列表  
const fetchMajorDomesticProjects = async () => {
  // 调用 /globalManage/zjmanage/largescreen/allCameraImportList
}
```

### 3. 切换功能实现
```javascript
// 切换重点工程类型
const switchMajorProjectType = async (type) => {
  if (majorProjectType.value === type) return;
  
  majorProjectType.value = type;
  
  if (type === 'international') {
    if (majorInternationalProjects.value.length === 0) {
      await fetchMajorInternationalProjects();
    }
  } else if (type === 'domestic') {
    if (majorDomesticProjects.value.length === 0) {
      await fetchMajorDomesticProjects();
    }
  }
};
```

### 4. 计算属性
```javascript
// 当前显示的重点工程项目
const currentMajorProjects = computed(() => {
  return majorProjectType.value === 'international' 
    ? majorInternationalProjects.value 
    : majorDomesticProjects.value;
});

// 将重点工程项目按行分组显示（每行4个）
const displayedMajorProjectRows = computed(() => {
  // 处理数据分组逻辑
});
```

### 5. UI界面修改

#### 切换按钮样式
将切换按钮调整到左上角位置，不影响视频原有布局：

**位置布局：**
```css
.major-project-controls-top {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  gap: 10px;
  z-index: 10;
}
```

**选中状态样式：**
```css
.major-project-tab-small.active {
  width: 120px;
  height: 36px;
  background: rgba(70,118,226,0.3);
  box-shadow: inset 0px 0px 8px 1px #4676E2, inset 0px 0px 8px 1px #4676E2;
  border-radius: 4px;
  border: 1px solid #4676E2;
  color: #00FFFF;
}
```

**未选中状态样式：**
```css
.major-project-tab-small {
  width: 120px;
  height: 36px;
  background: rgba(70,118,226,0.15);
  box-shadow: inset 0px 0px 8px 1px rgba(70,118,226,0.5);
  border-radius: 4px;
  border: 1px solid rgba(70,118,226,0.4);
}
```

#### 内容区域
- **国际工程**：显示视频播放器，支持全屏播放
- **国内工程**：显示监控播放器，使用EZUIKit组件

### 6. 播放器管理
- 为重点工程添加了独立的播放器类型：`major-international` 和 `major-domestic`
- 实现了播放器的暂停/恢复机制
- 在卡片切换时正确管理播放器状态

### 7. 数据流程
1. 用户点击切换按钮
2. 调用 `switchMajorProjectType()` 函数
3. 根据类型加载对应数据（如果尚未加载）
4. 更新UI显示对应内容
5. 初始化相应的播放器

## 技术特点
- **按需加载**：只有在切换到对应类型时才加载数据
- **播放器优化**：独立管理不同类型的播放器，避免冲突
- **响应式设计**：使用Vue 3的响应式系统，确保UI及时更新
- **错误处理**：完善的错误处理和加载状态管理

## 使用方式
1. 进入视频展示页面
2. 点击重点工程卡片激活
3. 在卡片左上角可以看到"国际工程"和"国内工程"两个切换按钮
4. 点击按钮即可切换显示不同类型的内容

## 接口说明

### 国际工程接口
- **接口地址**：`/globalManage/zjmanage/largescreen/allSpImportList`
- **数据结构**：
```javascript
{
  "code": 0,
  "data": [
    {
      "xmmc": "项目全名",
      "xmjc": "项目简称",
      "id": "项目ID",
      "sp": "视频ID列表（逗号分隔）"
    }
  ],
  "msg": ""
}
```

### 国内工程接口
- **接口地址**：`/globalManage/zjmanage/largescreen/allCameraImportList`
- **数据结构**：
```javascript
{
  "code": 0,
  "data": [
    {
      "deviceSerial": "设备序列号",
      "channelNo": "通道号",
      "storeName": "项目名称",
      "channelStatus": "通道状态（1-在线，0-离线）",
      "storeId": "项目ID"
    }
  ],
  "msg": ""
}
```

## 界面调整
- **按钮位置**：从卡片中央移至左上角，避免影响视频布局
- **按钮尺寸**：调整为120px × 36px，更加紧凑
- **视觉层级**：使用z-index确保按钮在最上层显示

功能已完全实现，可以正常使用！
