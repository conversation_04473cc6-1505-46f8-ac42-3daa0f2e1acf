var isChina = false;
var PointMap = bad;
var country = gjxm2;
var isPingPu = false;

// 添加请求头方法
function getRequestHeaders() {
    const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'tenant-id': '1',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    };

    // 从 sessionStorage 获取 token
    const token = sessionStorage.getItem('token');
    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    return headers;
}

// 添加获取营地数据的函数
async function fetchCampData() {
    try {
        const response = await fetch('/admin-api/globalManage/zjmanage/largescreen/getYdxx', {
            method: 'GET',
            headers: getRequestHeaders()
        });
        const result = await response.json();
        if (result.code === 0) {
            const campDataArray = [];

            for (let i = 0; i < result.data.length; i++) {
                const item = result.data[i];
                // 添加安全检查
                const gb = item.gb || '';
                const dz = item.dz || '';

                campDataArray.push({
                    类型: "营地",
                    国家: dz + '：' + gb,
                    区域: dz,
                    项目名称: item.ydmc,
                    id: item.id,
                    所属公司: item.ssgs,
                    营地介绍: item.ydjs,
                    营地照片: item.ydzp,
                    营地视频: item.ydsp
                });
            }

            return campDataArray;
        }
        return [];
    } catch (error) {
        console.error('获取营地数据失败:', error);
        return [];
    }
}

// 添加获取国际项目数据的函数
async function fetchProjectData() {
    try {
        const response = await fetch('/admin-api/globalManage/zjmanage/largescreen/getXmxx', {
            method: 'GET',
            headers: getRequestHeaders()
        });
        const result = await response.json();
        if (result.code === 0) {
            console.log(result.data);
            const projectDataArray = [];

            for (let i = 0; i < result.data.length; i++) {
                const item = result.data[i];
                // 添加安全检查，防止undefined错误
                const gb = item.gb || '';
                const dz = item.dz || '';
                let country = dz + '：' + gb;
                projectDataArray.push({
                    类型: "项目",
                    国家: country,
                    区域: dz,
                    项目名称: item.xmmc,
                    项目名称简称: item.xmjc,
                    id: item.id,
                    项目编码: item.xmbm,
                    项目地址: item.xmdz,
                    项目金额: item.zbjemy,
                    项目开工日期: item.kgrq,
                    项目完工日期: item.jgrq,
                    总工期: item.htgq,
                    累计产值: item.ljwcczmy,
                    施工进度百分比: item.zsgjd,
                    累计收款: item.ljsk,
                    项目累计成本: item.ljcb,
                    项目照片: item.xczp,
                    项目视频: item.sp
                });
            }

            return projectDataArray;
        }
        return [];
    } catch (error) {
        console.error('获取国际项目数据失败:', error);
        return [];
    }
}

$(async function () {
    // 获取营地数据和国际项目数据
    const [campData, projectData] = await Promise.all([
        fetchCampData(),
        fetchProjectData()
    ]);
    console.log(campData)

    // 合并API获取的数据（国际项目和国际营地）
    const apiData = [...campData, ...projectData];

    // 使用本地gjxm2作为基础数据，添加API获取的国际数据
    // 不再使用未定义的chinaData
    country = gjxm2;

    // 添加API获取的数据
    apiData.forEach(item => {
        if (!country.some(c => c.id === item.id)) {
            country.push(item);
        }
    });
    console.log(country)
    window.addEventListener('message', function (e) {
        if (e.data.type === 'clickCountry') {
            $('#map').hide()
            $('#iframe').show()
            render.control.autoRotate = false
            window.parent.postMessage({ type: "clickCountry", data: e.data.data }, "*");

            // 隐藏tools-container
            $('.tools-container').hide()
        }
        if (e.data.type === 'initMap') {
            if (e.data.data == true) {
                render.earthGroup.visible = true
                for (let name in render.countryNames) {
                    render.countryNames[name].visible = true
                }
                $('.tools-container').show()
            }

            iframe.contentWindow.postMessage({ type: "initMap" }, "*");
        }
        if (e.data.type === 'showEarth') {
            if (e.data.data) {
                $('#map').slideDown()
                $('#iframe').slideUp()
                render.control.autoRotate = true
                render.setView({ target: { x: 0.05886816085593781, y: 0, z: 0.024592653022636708 }, position: { x: -0.37242845010420506, y: 1.8379279765476316e-16, z: -2.9458232605089685 } })
            } else {
                $('#map').slideUp()
                $('#iframe').slideDown()
                render.control.autoRotate = false
                disposeEarth();
            }
        }
        if (e.data.type === 'isChina') {
            window.parent.postMessage({ type: "isChina" }, "*");
            // 隐藏tools-container
            $('.tools-container').hide()
        }
        if (e.data.type === 'clickProject') {
            window.parent.postMessage({ type: "clickProject", data: e.data.data }, "*");
        }
    })
    window.addEventListener('beforeunload', () => {
        disposeEarth();
        stopAnimation();
    });
    $('.zhankai').click(function () {
        $('.zhankai').addClass('none')
        $('.zhedie').removeClass('none')
        $('#iframe').slideDown()
        $('#map').slideUp()

        setTimeout(() => {
            iframe.contentWindow.postMessage({ type: "showMap", data: true }, "*");
        }, 1000)

    })

    $('.zhedie').click(function () {
        $('.zhedie').addClass('none')
        $('.zhankai').removeClass('none')
        $('#iframe').slideUp()
        $('#map').slideDown()

        setTimeout(() => {
            iframe.contentWindow.postMessage({ type: "showMap", data: false }, "*");
        }, 1000)
    })

    // 右键冒泡禁止
    document.addEventListener('contextmenu', function (e) {
        e.preventDefault();
    });

    country.forEach((item) => {
        if (item["国家"]) {
            item["区域"] = item["国家"].split("：")[0];
            item["国家"] = item["国家"].split("：")[1];
        }
    });

    gjxm2.forEach((item) => {
        map[item.项目名称] = item.项目名称简称;
    });
    console.log(PointMap[0])
    // 性能优化分析:
    // 1. 当前两层循环嵌套导致时间复杂度为O(n*m)，其中n是区域数量，m是每个区域下的项目数量
    // 2. 每次循环都要访问PointMap[0][key].project，增加了内存访问开销
    // 3. 每次都要进行map[key2]的查找，如果map很大也会影响性能
    // 4. 在频繁下钻和回退时，这些操作会被重复执行，导致卡顿

    // 优化建议:
    // 1. 考虑在数据初始化时就完成项目名称的转换，避免运行时重复处理
    // 2. 如果必须运行时处理，可以缓存转换结果
    // 3. 考虑使用Map或Set等数据结构优化查找性能
    // 4. 减少不必要的对象属性访问和删除操作

    // 临时优化方案 - 使用缓存减少重复计算
    const processedProjects = new Set();
    for (const key in PointMap[0]) {
        const projects = PointMap[0][key].project;
        for (const key2 in projects) {
            if (processedProjects.has(key2)) continue;
            if (map[key2]) {
                const data = projects[key2];
                delete projects[key2];
                projects[map[key2]] = data;
                processedProjects.add(key2);
            }
        }
    }

    render = new ZTRender("map");

    const ambientLight = new THREE.AmbientLight(0xffffff, 2)
    render.scene.add(ambientLight)

    render.events = new ZTMapEvents(render.scene, render.camera, render.container);

    render.setView({ target: { x: 0.05886816085593781, y: 0, z: 0.024592653022636708 }, position: { x: -0.37242845010420506, y: 1.8379279765476316e-16, z: -2.9458232605089685 } })

    render.control.enableDamping = true
    render.control.dampingFactor = 0.03

    // 限制只能水平旋转
    render.control.minPolarAngle = Math.PI / 2
    render.control.maxPolarAngle = Math.PI / 2

    // 禁用平移
    render.control.enablePan = false

    // 启用自动旋转
    render.control.autoRotate = true
    render.control.autoRotateSpeed = 0.4 // 增加自动旋转速度

    render.control.maxAzimuthAngle = THREE.MathUtils.degToRad(-105);
    render.control.minAzimuthAngle = THREE.MathUtils.degToRad(80);

    render.control.maxPolarAngle = THREE.MathUtils.degToRad(180)
    render.control.minPolarAngle = THREE.MathUtils.degToRad(-180)

    // const earthTextureMap = new THREE.TextureLoader().load('textures/earth4.png')
    const earthTextureMapLine = new THREE.TextureLoader().load('textures/earth.png')
    // const earthTexturePointMap = new THREE.TextureLoader().load('textures/earth_point.png')

    // 创建球体几何体
    const geometry = new THREE.SphereGeometry(1, 64, 64)

    // 创建地球材质
    render.earthMaterial = new THREE.MeshStandardMaterial({
        map: earthTextureMapLine,
        transparent: true,
        opacity: 0.84,
        depthWrite: true,
        depthTest: true,
        side: 0,
        emissive: new THREE.Color('#001E52'),
        blending: 2,
        alphaTest: 0.2,
        roughness: 0.3,
        metalness: 1,
    })

    // 创建地球外部点材质
    // render.earthMaterialPoint = new THREE.MeshStandardMaterial({
    //     map: earthTexturePointMap,
    //     transparent: true,
    //     opacity: 1,
    //     depthWrite: true,
    //     side: 2,
    //     blending: 1,
    //     alphaTest: 0.1,
    // })

    // 创建地球内部模糊材质
    render.earthMaterialInside = new THREE.MeshStandardMaterial({
        color: new THREE.Color('#143276'),
        transparent: true,
        opacity: 0.7,
        depthWrite: true,
        side: 0,
        roughness: 0,
        metalness: 1,
        blending: 2,
    })

    // 创建地球网格
    let earth = new THREE.Mesh(geometry, render.earthMaterial)
    let earth2 = earth.clone(true)
    earth.renderOrder = 99
    earth2.renderOrder = 99
    earth2.blending = 1

    // 创建地球组
    render.earthGroup = new THREE.Group()
    render.scene.add(render.earthGroup)
    render.earthGroup.position.set(0, -0.2, 0)
    // render.earthGroup.rotation.y = Math.PI / 1.15

    // 添加地球
    render.earthGroup.add(earth)
    render.earthGroup.add(earth2)

    // 创建地球外部点并添加
    let earthPoint = earth.clone()
    earthPoint.material = render.earthMaterialPoint
    earthPoint.scale.set(1.01, 1.01, 1.01)
    // render.earthGroup.add(earthPoint)

    // 创建地球内部模糊并添加
    let earthInside = earth.clone()
    earthInside.material = render.earthMaterialInside
    earthInside.scale.set(0.99, 0.99, 0.99)
    render.earthGroup.add(earthInside)

    render.earthGroup.scale.y = 0.98

    render.earthGroup.visible = false


    render.events.on("click", render.earthGroup, function (o, p) {
        // console.log(o, p)
    })

    render.update = function () {
        checkLabelVisibility()
    }

    initCubeTexture(render, function () { });

    initCountries()

    window.parent.postMessage({ eve: "loadok" }, "*");

})

const initCubeTexture = (render, callback) => {
    var path = 'sky2/';
    var format = '.jpg';
    var urls = [
        // "posx"+ format, "negx"+ format,
        // "posy"+ format, "negy"+ format,
        // "posz"+ format, "negz"+ format
        "px" + format, "nx" + format,
        "py" + format, "ny" + format,
        "pz" + format, "nz" + format
        // "left"+ format, "right"+ format,
        // "up"+ format, "down"+ format,
        // "front"+ format, "back"+ format
    ];
    const loader = new THREE.CubeTextureLoader();
    loader.setPath(path);
    loader.load(urls, function (texture) {
        // texture.rotation = THREE.MathUtils.degToRad(90);
        render.sceneBacTexture = texture;
        render.scene.environment = render.sceneBacTexture;
        if (callback) callback();
    });
}

function initCountries() {
    render.countryNames = {}
    const countries = [
        {
            name: '中国',
            position: { x: -0.09790180581539544, y: 0.04182491128418711, z: -0.974359037121783 },
            num: 0
        },
        {
            name: '蒙古',
            position: { x: -0.02879074783741387, y: 0.2195047110290883, z: -0.9132835335380193 },
            num: 0
        },
        {
            name: '新加坡',
            position: { x: -0.045730659480918895, y: -0.2904931446663596, z: -1.0033810370434255 },
            num: 0,
        },
        {
            name: '帕劳',
            position: { x: -0.4718264325878686, y: -0.24824612805851168, z: -0.8914314998021308 },
            num: 0,
        },
        {
            name: '巴新',
            position: { x: -0.6649624180154542, y: -0.36951802394125466, z: -0.7382523161469782 },
            num: 0,
        },
        {
            name: '瓦努阿图',
            position: { x: -0.8809581004792976, y: -0.4662826208781172, z: -0.40907798634576964 },
            num: 0,
        },
        {
            name: '汤加',
            position: { x: -0.9435818630654431, y: -0.4885284914980018, z: -0.20428005110549885 },
            num: 0,
        },
        {
            name: '尼泊尔',
            position: { x: 0.2945126374949092, y: -0.02730272769611039, z: -0.9492808769542201 },
            num: 0,
        },
        {
            name: '沙特',
            position: { x: 0.8481781439919687, y: -0.06642810221759377, z: -0.5287350222133929 },
            num: 0,
        },
        {
            name: '阿联酋',
            position: { x: 0.7224196767094497, y: -0.06265365111166671, z: -0.6903022534462117 },
            num: 0,
        },
        {
            name: '科威特',
            position: { x: 0.7877217093255969, y: -0.01571116213069451, z: -0.6014010405126643 },
            num: 0,
        },
        {
            name: '伊拉克',
            position: { x: 0.8177302558230215, y: 0.04989312327558307, z: -0.5339633343314183 },
            num: 0,
        },
        {
            name: '亚美尼亚',
            position: { x: 0.7873922553862878, y: 0.11444296198809861, z: -0.5435214727392126 },
            num: 0,
        },
        {
            name: '埃塞俄比亚',
            position: { x: 0.8837597743629529, y: -0.20475921997365015, z: -0.487525730836092 },
            num: 0,
        },
        {
            name: '南苏丹',
            position: { x: 0.9550413266871058, y: -0.24189177538779527, z: -0.32212847054599836 },
            num: 0,
        },
        {
            name: '乌干达',
            position: { x: 0.9323330701143725, y: -0.30430860757459205, z: -0.3715654546270717 },
            num: 0,
        },
        {
            name: '刚果（布）',
            position: { x: 0.9980592683794685, y: -0.3188180485452608, z: -0.08821148125849017 },
            num: 0,
        },
        {
            name: '安哥拉',
            position: { x: 0.9811958473071508, y: -0.4144175283117643, z: -0.09185755703144383 },
            num: 0,
        },
        {
            name: '赞比亚',
            position: { x: 0.9427423615282872, y: -0.442237594329958, z: -0.2616358145767513 },
            num: 0,
        },
        {
            name: '莫桑比克',
            position: { x: 0.8710045240465948, y: -0.46453061228163023, z: -0.43127884098941005 },
            num: 0,
        },
        {
            name: '津巴布韦',
            position: { x: 0.9102595227657021, y: -0.49412594376994884, z: -0.31466029294242276 },
            num: 0,
        },
        {
            name: '博茨瓦纳',
            position: { x: 0.9301332814298912, y: -0.5237952111847924, z: -0.20910170029119082 },
            num: 0,
        },
        {
            name: '纳米比亚',
            position: { x: 0.9539041682825974, y: -0.5125114318848462, z: -0.08376027322244625 },
            num: 0,
        },
        {
            name: '毛里求斯',
            position: { x: 0.6770489772942261, y: -0.5103394654995161, z: -0.6787465822690005 },
            num: 0,
        },
        {
            name: '加纳',
            position: { x: 0.9885110715680575, y: -0.23305212601313152, z: 0.20194992370615872 },
            num: 0,
        },
        {
            name: '科特迪瓦',
            position: { x: 0.9662711829090498, y: -0.24160753679612895, z: 0.28985561686343203 },
            num: 0,
        },

    ]

    let markers = countries.map((item) => {
        const campProjects = country.filter(
            (ele) => ele.类型 === "营地" && ele.国家.includes(item.name)
        );

        return {
            ...item,
            type: campProjects.length ? 1 : 2,
        };
    });

    const countryProjects = gjxm2
        .filter((item) => item.类型 == "项目")
        .reduce((acc, project) => {
            const country = project.国家;
            if (country) {
                // 只统计有国家数据的项目
                acc[country] = (acc[country] || 0) + 1;
            }
            return acc;
        }, {});

    countryProjects["中国"] = china.length;

    for (let i = 0; i < markers.length; i++) {
        if (countryProjects[markers[i].name]) {
            markers[i].num = countryProjects[markers[i].name];
        }
    }

    for (let i = 0; i < markers.length; i++) {
        let country = markers[i]
        let html = `<div class="label-bg" onclick="clickCountry('${country.name}','${country.num}')">${country.name}<span class="label-num">(${country.num || ''})</span></div>`
        let dom = document.createElement('div')
        dom.innerHTML = html
        let lable = new CSS2DObject(dom)
        lable.position.set(country.position.x, country.position.y, country.position.z)
        lable.visible = false
        render.scene.add(lable)
        lable.userData = { basePosition: lable.position.clone() }
        render.countryNames[country.name] = lable
    }
}

function checkLabelVisibility() {
    const labels = render.earthGroup.children.filter(child => child instanceof CSS2DObject)

    for (const name in render.countryNames) {
        const label = render.countryNames[name]
        const worldPosition = label.userData.basePosition.clone()
        worldPosition.applyMatrix4(render.earthGroup.matrixWorld)

        const directionToCamera = new THREE.Vector3().subVectors(render.camera.position, worldPosition)
        const normalVector = worldPosition.clone().normalize()
        const dotProduct = normalVector.dot(directionToCamera)

        label.element.style.opacity = dotProduct > 1 ? '1' : '0'
        label.element.style.pointerEvents = dotProduct > 1 ? 'auto' : 'none'
    }
}

function clickCountry(name, num) {
    render.control.autoRotate = false

    let p = new THREE.Vector3(0, 0, 0);
    let q = new THREE.Quaternion(0, 0, 0, 1);
    let s = new THREE.Vector3(0, 0, 0);
    render.countryNames[name].matrixWorld.decompose(p, q, s);

    let p1 = new THREE.Vector3(0, 0, 0).lerpVectors(render.control.target.clone(), p.clone(), 2);
    render.viewAnimate({
        target: render.control.target, position: p1, time: 1000, callback: function () {
            $('#iframe').slideDown()
            $('#map').slideUp()

            setTimeout(() => {
                iframe.contentWindow.postMessage({ type: "clickCountry", data: { name, num } }, "*");
            }, 1000)
        }
    })

}
let animationFrameId = null;

function animate() {
    animationFrameId = requestAnimationFrame(animate);
    if (isVisible) {
        render.update();
    }
}

function stopAnimation() {
    if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
    }
}
function disposeEarth() {
    // 释放几何体
    render.earthGroup.traverse((obj) => {
        if (obj.geometry) {
            obj.geometry.dispose();
        }
        if (obj.material) {
            if (obj.material.map) {
                obj.material.map.dispose();
            }
            obj.material.dispose();
        }
    });

    // 清除标签
    for (const name in render.countryNames) {
        const label = render.countryNames[name];
        render.scene.remove(label);
        label.element.remove();
    }
    render.countryNames = {};

    // 停止渲染循环
    render.control.autoRotate = false;
}