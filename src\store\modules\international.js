import { defineStore } from 'pinia'
import businessRequest from '@/utils/businessRequest'

export const useInternationalStore = defineStore('international', {
    state: () => ({
        projects: [],
        loading: false,
        error: null
    }),

    getters: {
        // 获取处理后的项目数据
        processedProjects: (state) => {
            return state.projects.map(project => ({
                类型: "项目",
                项目名称: project.xmmc || "",
                项目名称简称: project.xmjc || "",
                单位: project.dz || "",  // 区域作为单位
                项目类型: "",
                国家: `${project.dz || ""}：${project.gb || ""}`,  // 格式：区域：国家
                项目地点: project.xmdz || "",
                项目合同额: project.zbjemy || "0",
                项目累计产值: project.ljwcczmy || "0",
                项目累收工程款: project.ljsk || "0",
                总工期: project.htgq || "",
                开工日期: project.kgrq || "",
                中方员工人数: "",
                外籍员工人数: "",
                id: project.id,
                xczp: project.xczp || ""
            }))
        }
    },

    actions: {
        async fetchProjects() {
            try {
                this.loading = true
                const response = await businessRequest.get('/admin-api/globalManage/zjmanage/largescreen/getXmxx')
                if (response.data.code === 0) {
                    this.projects = response.data.data
                } else {
                    throw new Error(response.data.msg || '获取数据失败')
                }
                this.error = null
            } catch (err) {
                console.error('获取项目数据失败:', err)
                this.error = err.message
            } finally {
                this.loading = false
            }
        }
    }
}) 