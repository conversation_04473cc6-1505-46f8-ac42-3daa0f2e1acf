import{d as e,e as n,M as r,o as t,h as l,i as a}from"./vendor-22dca409.js";const s={__name:"index",setup(s){const u=e(null);let o=!1,d=null;n((()=>{const e=u.value;e&&e.addEventListener("ended",i)})),r((()=>{const e=u.value;e&&(e.removeEventListener("ended",i),d&&(clearInterval(d),d=null))}));const i=()=>{const e=u.value;e&&(o||(o=!0,e.pause(),e.currentTime=e.duration,d=setInterval((()=>{e.currentTime<=.03?(clearInterval(d),d=null,e.currentTime=0,o=!1,e.play()):e.currentTime-=.03}),20)))};return(e,n)=>(t(),l("div",null,[a("video",{ref_key:"videoRef",ref:u},null,512)]))}};export{s as default};
