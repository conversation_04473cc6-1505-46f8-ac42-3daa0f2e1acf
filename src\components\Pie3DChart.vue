<template>
  <div class="chart-container">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

const chartRef = ref(null)
const chart = ref(null)

const initChart = () => {
  if (!chartRef.value) return

  chart.value = echarts.init(chartRef.value)

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      show: false
    },
    legend: {
      show: false
    },
    series: [
      // 主饼图
      {
        name: '项目分布',
        type: 'pie',
        // radius: ['45%', '65%'],
        center: ['50%', '50%'],
        startAngle: 270,
        label: {
          show: true,
          position: 'outside',
          formatter: params => {
            return `{a|${params.value}}{b|个}`
          },
          rich: {
            a: {
              fontSize: 16,
              color: '#a1fcd7',
              fontFamily: 'Oswald Medium',
              padding: [0, 4, 0, 0]
            },
            b: {
              fontSize: 12,
              color: '#fff',
              fontFamily: 'Regular'
            }
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 20,
          lineStyle: {
            color: '#ffffff40'
          }
        },
        itemStyle: {
          borderWidth: 0
        },
        data: [
          {
            value: 167,
            name: '国内',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                { offset: 0, color: 'rgba(91, 199, 244, 0.8)' },
                { offset: 1, color: 'rgba(91, 199, 244, 0.9)' }
              ])
            }
          },
          {
            value: 54,
            name: '国际',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                { offset: 0, color: 'rgba(106, 228, 177, 0.8)' },
                { offset: 1, color: 'rgba(106, 228, 177, 0.9)' }
              ])
            }
          }
        ]
      },
      // 底部阴影
      {
        type: 'pie',
        // radius: ['45%', '65%'],
        center: ['50%', '51%'],
        startAngle: 270,
        silent: true,
        label: {
          show: false
        },
        itemStyle: {
          color: 'rgba(0,0,0,0.1)'
        },
        data: [
          {
            value: 100,
            itemStyle: {
              color: 'rgba(0,0,0,0.1)'
            }
          }
        ]
      }
    ]
  }

  chart.value.setOption(option)
}

const handleResize = () => {
  chart.value?.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chart.value?.dispose()
})
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 200px;
  height: 200px;
  background: url('@/assets/images/xiangmu/3d-pie-bg.png') no-repeat center bottom;
  background-size: 80% auto;
}

.chart {
  width: 100%;
  height: 100%;
}
</style> 