<template>
  <div
    class="project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between"
  >
    <!-- 3d地球 -->
    <div class="map-container">
      <Earth3D />
    </div>
    
  </div>
</template>
<script setup>
  import Earth3D from "@/components/Earth3D/index2.vue";
</script>
<style scoped lang="scss">

  .map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('@/assets/images/textures/bg.png') no-repeat center center / cover;

  }
</style>
