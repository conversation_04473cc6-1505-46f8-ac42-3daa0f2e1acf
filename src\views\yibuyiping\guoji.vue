<template>
    <div class="guoji-container" :style="{ backgroundImage: `url(${currentImage})` }">
        <!-- 返回按钮 -->
        <!-- <img class="cursor-pointer absolute -top-[6px] left-[60px]" src="@/assets/images/xiangmu/back.png" alt=""
            @click="back" style="z-index: 3" /> -->
            <div class="back-button" @click="back">
            <img src="@/assets/images/header/back.png" alt="" />
            <span>返回</span>
        </div>
        <!-- 锚点1：上面中间部分 - 只在主页面显示 -->
        <div v-if="currentView === 'main'" class="anchor anchor-1" @click="handleAnchor1Click"></div>

        <!-- 锚点2：上面右边部分 - 只在主页面显示 -->
        <div v-if="currentView === 'main'" class="anchor anchor-2" @click="handleAnchor2Click"></div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

// 引入图片资源
import maoyi_main from '@/assets/images/yibuyiping/一部一屏-国际事业部-国际贸易.png';
import maoyi_detail from '@/assets/images/yibuyiping/一部一屏-国际事业部-国际贸易-查看详情.png';
import gongcheng_main from '@/assets/images/yibuyiping/一部一屏-国际事业部-国际工程.png';
import gongcheng_detail from '@/assets/images/yibuyiping/一部一屏-国际事业部-国际工程-查看详情.png';

const router = useRouter();

// 当前显示的模式：'maoyi' | 'gongcheng'
const currentMode = ref('maoyi');

// 当前显示的视图：'main' | 'detail'
const currentView = ref('main');

// 计算当前应该显示的图片
const currentImage = computed(() => {
    if (currentMode.value === 'maoyi') {
        return currentView.value === 'main' ? maoyi_main : maoyi_detail;
    } else {
        return currentView.value === 'main' ? gongcheng_main : gongcheng_detail;
    }
});

// 锚点1点击处理：切换模式
const handleAnchor1Click = () => {
    console.log('锚点1点击');
    if (currentMode.value === 'maoyi') {
        // 从国际贸易切换到国际工程
        currentMode.value = 'gongcheng';
    } else {
        // 从国际工程切换回国际贸易
        currentMode.value = 'maoyi';
    }
    currentView.value = 'main'; // 重置到主视图
};

// 锚点2点击处理：查看详情
const handleAnchor2Click = () => {
    console.log('锚点2点击');
    currentView.value = 'detail';
};

// 返回按钮
const back = () => {
    if (currentView.value === 'detail') {
        // 如果当前是详情页面，返回到主页面（保持当前模式）
        currentView.value = 'main';
    } else {
        // 如果当前是主页面，返回到主页面列表
        router.push({ name: 'yibuyipingIndex' });
    }
};
</script>

<style scoped lang="scss">
.back-button {
    position: absolute;
    top: -6px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
}
.guoji-container {
    pointer-events: all;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    position: absolute;
    top: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.anchor {
    position: absolute;
    cursor: pointer;
    z-index: 10;
}

.anchor-1 {
    // 锚点1：上面中间部分
    width: 300px;
    height: 50px;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
}

.anchor-2 {
    // 锚点2：上面右边部分
    width: 130px;
    height: 60px;
    top: 10%;
    right: 4%;
}
</style>