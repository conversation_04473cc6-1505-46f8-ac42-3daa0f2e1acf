// WebGL检测和错误处理工具

// 检测WebGL支持
export const checkWebGLSupport = () => {
    try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) return false;

        // 检测WebGL版本和扩展
        const version = gl.getParameter(gl.VERSION);
        const renderer = gl.getParameter(gl.RENDERER);
        console.log('WebGL版本:', version);
        console.log('WebGL渲染器:', renderer);

        // 检测最大纹理大小
        const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
        console.log('最大纹理大小:', maxTextureSize);

        // 检测是否支持必要的扩展
        const extensions = {
            angle_instanced_arrays: gl.getExtension('ANGLE_instanced_arrays'),
            oes_element_index_uint: gl.getExtension('OES_element_index_uint'),
            oes_standard_derivatives: gl.getExtension('OES_standard_derivatives')
        };

        console.log('WebGL扩展支持:', extensions);

        return true;
    } catch (e) {
        console.error('WebGL检测失败:', e);
        return false;
    }
};

// 检测设备性能
export const checkDevicePerformance = () => {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isLowEnd = isMobile || (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4);
    const hasLowMemory = navigator.deviceMemory && navigator.deviceMemory < 4;

    console.log('设备类型:', isMobile ? '移动设备' : '桌面设备');
    console.log('CPU核心数:', navigator.hardwareConcurrency || '未知');
    console.log('设备内存:', navigator.deviceMemory ? `${navigator.deviceMemory}GB` : '未知');

    return !isLowEnd && !hasLowMemory;
};

// 获取简化的3D配置（用于性能较低的设备）
export const getSimplified3DConfig = () => ({
    postEffect: {
        enable: false
    },
    temporalSuperSampling: {
        enable: false
    },
    viewControl: {
        autoRotate: false,
        damping: 0.5,
        rotateSensitivity: 1,
        zoomSensitivity: 1,
        panSensitivity: 1
    }
});

// ECharts 3D错误处理封装
export const safeInit3DChart = (container, option, fallbackCallback) => {
    try {
        // 检查WebGL支持和设备性能
        if (!checkWebGLSupport()) {
            console.warn('WebGL不支持，使用2D替代方案');
            if (fallbackCallback) fallbackCallback();
            return null;
        }

        if (!checkDevicePerformance()) {
            console.warn('设备性能较低，简化3D效果');
            // 简化配置
            const simplifiedConfig = getSimplified3DConfig();
            if (option.grid3D) {
                option.grid3D = { ...option.grid3D, ...simplifiedConfig };
            }
        }

        const chart = echarts.init(container);
        chart.setOption(option);
        return chart;
    } catch (error) {
        console.error('3D图表初始化失败:', error);
        if (fallbackCallback) fallbackCallback();
        return null;
    }
};

// 创建2D饼图替代方案
export const create2DPieChart = (container, data, config = {}) => {
    const chart = echarts.init(container);
    const total = data.reduce((sum, item) => sum + item.value, 0);

    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        title: {
            text: config.title || `Total\n${total}`,
            left: 'center',
            top: 'center',
            textAlign: 'center',
            textStyle: {
                fontSize: config.titleFontSize || 16,
                color: config.titleColor || '#FFFFFF',
                lineHeight: 20
            }
        },
        series: [
            {
                name: config.seriesName || 'Statistics',
                type: 'pie',
                radius: config.radius || ['50%', '70%'],
                center: config.center || ['50%', '50%'],
                data: data,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    show: config.showLabel || false
                },
                labelLine: {
                    show: config.showLabelLine || false
                }
            }
        ]
    };

    chart.setOption(option);
    return chart;
}; 