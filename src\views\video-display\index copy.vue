<template>
    <div class="xianchang">
        <!-- 添加返回按钮，根据showBackButton显示或隐藏 -->
        <div v-if="showBackButton" class="back-button" @click="handleBackToEarth">
            <img src="@/assets/images/header/back.png" alt="" />
            <span>返回</span>
        </div>
        
        <!-- 显示国家/地区名称 -->
        <div v-if="showBackButton && province" 
             class="absolute w-full flex justify-center font-family-youshebiaotihei tracking-[8px] text-[30px] country-title mt-[70px]" style="top:10%">
            {{ province }}
        </div>
        
        <div class="map">
            <iframe src="./zjsj-yydp/xcsp.html" ref="iframe"
                class="w-full h-full iframe-container" @load="onIframeLoad"></iframe>
        </div>
    </div>

    <!-- 摄像头列表弹窗 -->
    <div class="camera-popup" v-if="showCameraList">
        <div class="popup-header">
            <span>{{ province }}项目信息</span>
            <img class="close-btn" src="@/assets/images/xianchangVideo/close.png" alt="关闭" @click="closeCameraList" />
        </div>
        <div class="popup-body">
            <!-- 摄像头列表 -->
            <div v-if="cameraList.length > 0" class="camera-list">
                <div v-for="camera in cameraList" :key="camera.channelId" class="camera-item">
                    <div class="camera-name" :title="camera.channelName">{{ camera.channelName }}</div>
                    <span class="view-btn" :class="{ 'disabled': camera.channelStatus !== 1 }"
                        @click="camera.channelStatus === 1 ? viewVideo(camera) : null">
                        查看视频
                    </span>
                </div>
            </div>

            <!-- 无数据 -->
            <div v-else class="no-data">
                <p>暂无监控设备数据</p>
            </div>
        </div>
    </div>

    <!-- 视频播放弹窗 -->
    <div class="video-popup" v-if="showVideoPlayer">
        <div class="video-container" @click.stop>
            <div class="chart-header">
                <span>{{ currentCamera?.channelName || '现场监控视频' }}</span>
                <img class="close-icon" src="@/assets/images/xianchangVideo/close.png" alt="关闭"
                    @click="closeVideoPlayer" />
            </div>
            <div class="chart-body">
                <!-- 加载状态 -->
                <div v-if="isVideoLoading" class="loading-overlay">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">正在加载监控视频...</p>
                </div>

                <!-- 视频内容区域 -->
                <div class="chart-content">
                    <!-- EZUIKit视频播放器容器 -->
                    <div class="videoCard" id="videoPlayerModal" />
                </div>
            </div>
        </div>
    </div>

    <!-- 国外项目视频播放弹窗 -->
    <div class="international-video-popup" v-if="showInternationalVideoPlayer">
        <div class="international-video-container" @click.stop>
            <div class="chart-headerVideo">
                <span>{{ currentProvinceName || '项目视频' }}</span>
                <img class="close-icon" src="@/assets/images/xianchangVideo/close.png" alt="关闭"
                    @click="closeInternationalVideoPlayer" />
            </div>
            <div class="chart-body">
                <!-- 加载状态 -->
                <div v-if="isInternationalVideoLoading" class="loading-overlay">
                    <div class="loading-spinner"></div>
                    <p class="loading-text">正在加载项目视频...</p>
                </div>

                <!-- 视频内容区域 -->
                <div class="chart-content" v-if="!isInternationalVideoLoading">
                    <!-- 主视频播放区域 -->
                    <div class="main-video-area">
                        <video v-if="internationalVideos.length > 0 && !hasVideoError"
                            :src="internationalVideos[currentInternationalVideoIndex]" controls autoplay muted
                            class="international-video-player" @error="handleVideoError"
                            @loadstart="handleVideoLoadStart"></video>
                        <div v-else class="no-video">
                            <p>暂无监控设备数据</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import axios from 'axios';
import { ElMessage, ElTooltip } from "element-plus";
import {
    fixEZUIKitUnloadError,
    safeDestroyEZUIKit,
    cleanupEZUIKitResources,
    safeInitEZUIKit,
    setupEZUIKitVisibilityHandler
} from '@/utils/ezuikitFix';
import { initErrorFix, safeCleanup } from '@/utils/chartErrorFix';


// 添加iframe的ref引用
const iframe = ref(null);
// 控制返回按钮显示
const showBackButton = ref(false);

// 摄像头列表相关状态
const showCameraList = ref(false);
const cameraList = ref([]);
const cameraListLoading = ref(false);
const currentProjectName = ref('');
const currentProjectCode = ref('');
const currentProvinceName = ref('');

// 当前层级状态
const currentLevel = ref('');

// 视频播放器相关状态
const showVideoPlayer = ref(false);
const currentCamera = ref(null);
let currentVideoPlayer = null;
const isVideoLoading = ref(false);
let visibilityCleanup = null;

// 国外项目视频播放器相关状态
const showInternationalVideoPlayer = ref(false);
const internationalVideos = ref([]);
const currentInternationalVideoIndex = ref(0);
const isInternationalVideoLoading = ref(false);
const currentInternationalProjectInfo = ref(null);
const hasVideoError = ref(false);

// 动态获取的AccessToken
let dynamicAccessToken = '';

// 从项目名称中提取省份名称的函数
const getProvinceFromProjectName = (projectName) => {
    if (!projectName) return '';

    // 定义省份关键词映射
    const provinceMap = {
        '北京': '北京市',
        '天津': '天津市',
        '河北': '河北省',
        '山西': '山西省',
        '内蒙古': '内蒙古自治区',
        '辽宁': '辽宁省',
        '吉林': '吉林省',
        '黑龙江': '黑龙江省',
        '上海': '上海市',
        '江苏': '江苏省',
        '浙江': '浙江省',
        '安徽': '安徽省',
        '福建': '福建省',
        '江西': '江西省',
        '山东': '山东省',
        '河南': '河南省',
        '湖北': '湖北省',
        '湖南': '湖南省',
        '广东': '广东省',
        '广西': '广西壮族自治区',
        '海南': '海南省',
        '重庆': '重庆市',
        '四川': '四川省',
        '贵州': '贵州省',
        '云南': '云南省',
        '西藏': '西藏自治区',
        '陕西': '陕西省',
        '甘肃': '甘肃省',
        '青海': '青海省',
        '宁夏': '宁夏回族自治区',
        '新疆': '新疆维吾尔自治区'
    };

    // 遍历省份关键词，查找匹配的省份
    for (const [keyword, fullName] of Object.entries(provinceMap)) {
        if (projectName.includes(keyword)) {
            return fullName;
        }
    }

    // 如果没有找到匹配的省份，返回项目名称的完整字体
    return projectName;
};

// 判断是否为国外项目的函数
const isInternationalProject = (projectName) => {
    if (!projectName) return false;

    // 定义中国省份关键词
    const chineseProvinces = [
        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '香港', '澳门', '台湾'
    ];

    // 如果项目名称包含中国省份，则认为是国内项目
    for (const province of chineseProvinces) {
        if (projectName.includes(province)) {
            return false;
        }
    }

    // 如果项目名称包含常见的国外标识，则认为是国外项目
    const internationalKeywords = [
        '埃塞', '肯尼亚', '坦桑尼亚', '乌干达', '赞比亚', '南非', '尼日利亚',
        '巴基斯坦', '印度', '孟加拉', '斯里兰卡', '缅甸', '泰国', '老挝',
        '柬埔寨', '越南', '马来西亚', '印尼', '菲律宾', '新加坡',
        '俄罗斯', '哈萨克斯坦', '乌兹别克斯坦', '土库曼斯坦', '吉尔吉斯斯坦',
        '阿富汗', '伊朗', '伊拉克', '土耳其', '沙特', '阿联酋', '卡塔尔',
        '塞尔维亚', '匈牙利', '波兰', '德国', '法国', '意大利', '希腊',
        '智利', '阿根廷', '巴西', '秘鲁', '墨西哥', '加拿大', '美国',
        '英国', '澳大利亚', '新西兰', '日本', '韩国'
    ];

    for (const keyword of internationalKeywords) {
        if (projectName.includes(keyword)) {
            return true;
        }
    }

    // 如果都不匹配，则通过项目名称的特征来判断
    // 如果包含"银行"、"LED"等关键词且不在中国省份中，很可能是国外项目
    const potentialInternationalKeywords = ['银行', 'LED', '大屏', '维修', '建设'];
    const hasInternationalKeywords = potentialInternationalKeywords.some(keyword =>
        projectName.includes(keyword)
    );

    // 如果有这些关键词且没有中国省份标识，可能是国外项目
    if (hasInternationalKeywords) {
        return true;
    }

    return false;
};

// 背景样式计算属性
const backgroundStyle = computed(() => {
    return {
        backgroundImage: 'url("@/assets/images/xiangmu/wangge.png")',
        backgroundSize: '100% 100%',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center center'
    };
});

// 处理iframe消息的函数
const handleIframeMessage = (event) => {
    // 详细日志输出，帮助调试
    console.log('📨 接收到postMessage事件:', {
        origin: event.origin,
        source: event.source,
        data: event.data,
        eventType: typeof event.data
    });

    // 安全检查：验证消息来源（可选，根据实际情况调整）
    // if (event.origin !== 'http://192.168.0.117:8080') {
    //     console.warn('❌ 消息来源不受信任:', event.origin);
    //     return;
    // }

    const message = event.data;

    // 更详细的数据类型检查
    if (!message) {
        console.log('⚠️ 收到空消息');
        return;
    }

    // 检查是否是字符串类型的消息，可能需要解析
    if (typeof message === 'string') {
        try {
            const parsedMessage = JSON.parse(message);
            console.log('📋 解析字符串消息成功:', parsedMessage);
            handleParsedMessage(parsedMessage);
            return;
        } catch (error) {
            console.log('📋 字符串消息无法解析为JSON:', message);
        }
    }

    // 直接处理对象类型的消息
    if (typeof message === 'object') {
        console.log('📋 处理对象消息:', message);
        handleParsedMessage(message);
    }
};
const province = ref('');
// 处理解析后的消息
const handleParsedMessage = (message) => {
    console.log('🔄 处理消息:', message);

    // 处理不同类型的消息
    if (message && message.eve) {
        console.log('✅ 检测到eve属性:', message.eve);
        switch (message.eve) {
            case 'clickTitle':
                console.log('🎯 处理点击标题事件:', message);
                currentLevel.value = message.level;
                province.value = message.title;
                if (message.level === 'country' || message.level === 'province') {
                    showBackButton.value = true;
                } else {
                    showBackButton.value = false;
                }
                break;
            case 'clickSxt':
                console.log('🎯 处理点击项目散点事件:', message);
                currentProjectName.value = message.name;
                currentProjectCode.value = message.code;
                // 从项目名称中提取省份名称
                currentProvinceName.value = getProvinceFromProjectName(message.name);
                console.log('📍 提取的省份名称:', currentProvinceName.value);

                // 判断是否为国外项目，如果是国外项目则获取营地视频，否则获取摄像头列表
                if (isInternationalProject(message.name)) {
                    console.log('🌍 这是国外项目，获取营地视频');
                    fetchInternationalProjectVideos(message.code);
                } else {
                    console.log('🏠 这是国内项目，获取摄像头列表');
                    fetchCameraList(message.code);
                }
                break;
            default:
                console.log('❓ 收到未知类型的iframe消息:', message);
        }
    } else {
        console.log('⚠️ 消息格式不符合预期（缺少eve属性）:', message);
        // 输出消息的所有属性，帮助调试
        if (typeof message === 'object') {
            console.log('🔍 消息属性:', Object.keys(message));
        }
    }
};

// 获取摄像头列表
const fetchCameraList = async (projectCode) => {
    try {
        cameraListLoading.value = true;
        showCameraList.value = true;

        const response = await axios.get('/admin-api/globalManage/zjmanage/largescreen/cameraList', {
            params: { id: projectCode },
            headers: {
                'Authorization': `Bearer ${sessionStorage.getItem('token')}`
            }
        });

        if (response.data.code === 0) {
            cameraList.value = response.data.data || [];
            console.log('摄像头列表获取成功:', cameraList.value);
        } else {
            // ElMessage.error(response.data.msg || '获取摄像头列表失败');
            cameraList.value = [];
        }
    } catch (error) {
        console.error('获取摄像头列表失败:', error);
        // ElMessage.error('获取摄像头列表失败: ' + error.message);
        cameraList.value = [];
    } finally {
        cameraListLoading.value = false;
    }
};

// 关闭摄像头列表弹窗
const closeCameraList = () => {
    showCameraList.value = false;
    cameraList.value = [];
    currentProjectName.value = '';
    currentProjectCode.value = '';
    currentProvinceName.value = '';
};

// 获取国外项目视频
const fetchInternationalProjectVideos = async (projectCode) => {
    try {
        isInternationalVideoLoading.value = true;
        showInternationalVideoPlayer.value = true;
        hasVideoError.value = false;

        // 先获取token
        const tokenRes = await axios.get('/admin-api/globalManage/zjmanage/largescreen/getToken', {
            headers: {
                'Authorization': `Bearer ${sessionStorage.getItem('token')}`
            }
        });

        if (tokenRes.data.code !== 0 || !tokenRes.data.data) {
            throw new Error(tokenRes.data.msg || '获取token失败');
        }

        const token = tokenRes.data.data;

        await getInternationalProjectVideos(projectCode, token)
    } catch (error) {
        console.error('获取国外项目视频失败:', error);
        // ElMessage.error('获取项目视频失败: ' + error.message);
        internationalVideos.value = [];
    } finally {
        isInternationalVideoLoading.value = false;
    }
};

// 获取国外项目视频URLs
const getInternationalProjectVideos = async (videoIds, token) => {
    if (!videoIds || !token) return;

    try {
        const userId = "941981453197164545"; // 固定的userId

        // 处理视频ID列表
        // const videoIdArray = videoIds.split(',').filter(id => id.trim());
        const videoUrls = [];

        if (videoIds) {
            console.log('视频ID:', videoIds);
            // 构建视频URL
            videoUrls.push(`http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${videoIds}?access_token=${token}&userid=${userId}`);
        }

        if (videoUrls.length > 0) {
            internationalVideos.value = videoUrls;
            currentInternationalVideoIndex.value = 0;
            console.log('国外项目视频URLs:', videoUrls);
        } else {
            // ElMessage.warning('该项目暂无有效视频数据');
            internationalVideos.value = [];
        }
    } catch (err) {
        console.error("处理国外项目视频失败:", err);
        // ElMessage.error('处理视频数据失败: ' + err.message);
        internationalVideos.value = [];
    }
};

// 关闭国外项目视频播放器
const closeInternationalVideoPlayer = () => {
    showInternationalVideoPlayer.value = false;
    internationalVideos.value = [];
    currentInternationalVideoIndex.value = 0;
    currentInternationalProjectInfo.value = null;
    isInternationalVideoLoading.value = false;
    hasVideoError.value = false;
};

// 处理视频加载错误
const handleVideoError = (event) => {
    console.error('视频加载失败:', event);
    hasVideoError.value = true;
};

// 处理视频开始加载
const handleVideoLoadStart = () => {
    hasVideoError.value = false;
};

// 切换国外项目视频
const changeInternationalVideo = (index) => {
    if (index >= 0 && index < internationalVideos.value.length) {
        currentInternationalVideoIndex.value = index;
        hasVideoError.value = false;
    }
};

// 判断是否需要显示tooltip（当文字超出容器宽度时）
const shouldShowTooltip = (text) => {
    if (!text) return false;
    // 简化逻辑：超过15个字符就显示tooltip，确保用户能看到完整信息
    return text.length > 15;
};

// 获取萤石云Token
const getHikToken = async () => {
    try {
        const response = await axios.get('/admin-api/globalManage/zjmanage/largescreen/getHikToken', {
            headers: {
                'Authorization': `Bearer ${sessionStorage.getItem('token')}`
            }
        });

        if (response.data.code === 0) {
            dynamicAccessToken = response.data.data;
            console.log('获取萤石云Token成功');
            return dynamicAccessToken;
        } else {
            throw new Error(response.data.msg || '获取Token失败');
        }
    } catch (error) {
        console.error('获取萤石云Token失败:', error);
        // ElMessage.error('获取萤石云Token失败: ' + error.message);
        throw error;
    }
};

// EZUIKit主题配置
const themeData = {
    autoFocus: 5,
    poster: "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
    header: {
        color: "#1890ff",
        activeColor: "#FFFFFF",
        backgroundColor: "#000000",
        btnList: []
    },
    footer: {
        color: "#FFFFFF",
        activeColor: "#1890FF",
        backgroundColor: "#00000051",
        btnList: [
            {
                iconId: "play",
                part: "left",
                defaultActive: 1,
                memo: "播放",
                isrender: 1,
            },
            {
                iconId: "capturePicture",
                part: "left",
                defaultActive: 0,
                memo: "截屏按钮",
                isrender: 1,
            },
            {
                iconId: "sound",
                part: "left",
                defaultActive: 0,
                memo: "声音按钮",
                isrender: 1,
            },
            {
                iconId: "recordvideo",
                part: "left",
                defaultActive: 0,
                memo: "录制按钮",
                isrender: 1,
            },
            {
                iconId: "expend",
                part: "right",
                defaultActive: 0,
                memo: "全局全屏按钮",
                isrender: 1,
            },
        ],
    },
};

// 初始化视频播放器
const initVideoPlayer = async (camera) => {
    try {
        isVideoLoading.value = true;

        // 销毁已存在的播放器
        if (currentVideoPlayer) {
            safeDestroyEZUIKit(currentVideoPlayer);
            currentVideoPlayer = null;
        }

        // 清理之前的可见性监听器
        if (visibilityCleanup) {
            visibilityCleanup();
            visibilityCleanup = null;
        }

        // 获取Token
        const token = await getHikToken();

        // 等待DOM元素准备好
        await nextTick();

        console.log('开始初始化EZUIKit播放器...');

        // 构建视频URL - 处理不同的字段名可能性
        const deviceSerial = camera.deviceSerial || camera.deviceId || camera.serialNumber || camera.sn;
        const channelNo = camera.channelNo || camera.channelId || camera.channel || '1'; // 默认通道1

        if (!deviceSerial) {
            throw new Error('设备序列号不存在，无法播放视频');
        }

        const videoUrl = `ezopen://open.ys7.com/${deviceSerial}/${channelNo}.live`;
        console.log('视频URL:', videoUrl);
        console.log('设备信息:', { deviceSerial, channelNo, camera });

        // 使用安全的初始化方法 - 检查EZUIKit是否可用
        if (typeof EZUIKit === 'undefined') {
            throw new Error('EZUIKit库未加载，请确保ezuikit.js已正确引入');
        }

        const playerConfig = {
            staticPath: "/ezuikit_static/v65",
            id: "videoPlayerModal",
            accessToken: token,
            url: videoUrl,
            quality: 6,
            audio: false,
            themeData: themeData,
            useHardDev: true,
            width: 700,
            height: 500
        };

        console.log('EZUIKit配置:', playerConfig);
        currentVideoPlayer = await safeInitEZUIKit(playerConfig);

        console.log('EZUIKit播放器初始化完成');

        // 设置页面可见性处理器
        visibilityCleanup = setupEZUIKitVisibilityHandler ? setupEZUIKitVisibilityHandler(currentVideoPlayer) : null;

        // 延迟隐藏加载状态
        setTimeout(() => {
            isVideoLoading.value = false;
        }, 2000);

    } catch (error) {
        console.error('初始化视频播放器失败:', error);
        isVideoLoading.value = false;

        // ElMessage({
        //     message: '视频播放器初始化失败: ' + error.message,
        //     type: 'error'
        // });
    }
};

// 查看视频方法
const viewVideo = async (camera) => {
    if (camera.channelStatus !== 1) {
        // ElMessage.warning('该摄像头当前不在线，无法查看视频');
        return;
    }

    console.log('点击查看视频:', camera);
    currentCamera.value = camera;
    showVideoPlayer.value = true;

    // 等待弹窗DOM渲染完成后初始化播放器
    await nextTick();
    setTimeout(() => {
        initVideoPlayer(camera);
    }, 200);
};

// 关闭视频播放器
const closeVideoPlayer = () => {
    // 销毁播放器
    if (currentVideoPlayer) {
        safeDestroyEZUIKit(currentVideoPlayer);
        currentVideoPlayer = null;
    }

    // 清理可见性监听器
    if (visibilityCleanup) {
        visibilityCleanup();
        visibilityCleanup = null;
    }

    showVideoPlayer.value = false;
    currentCamera.value = null;
    isVideoLoading.value = false;
};

// 获取状态样式类
const getStatusClass = (status) => {
    switch (status) {
        case 1: return 'online';
        case 0: return 'offline';
        case -1: return 'error';
        default: return 'unknown';
    }
};

// 获取状态文本
const getStatusText = (status) => {
    switch (status) {
        case 1: return '在线';
        case 0: return '离线';
        case -1: return '异常';
        default: return '未知';
    }
};

// 发送token到iframe的函数
const sendTokenToIframe = () => {
    if (iframe.value && iframe.value.contentWindow) {
        const token = sessionStorage.getItem('token');
        if (token) {
            try {
                iframe.value.contentWindow.postMessage(
                    {
                        type: "token",
                        data: token,
                    },
                    "*"
                );
                console.log('✅ Token已成功发送到iframe');
            } catch (error) {
                console.error('❌ 发送token失败:', error);
            }
        }
    } else {
        // 如果iframe还没准备好，稍后重试
        setTimeout(sendTokenToIframe, 500);
    }
};

// iframe加载完成的处理函数
const onIframeLoad = () => {
    // iframe加载完成后，稍等一下再发送token，确保iframe内部也准备好了
    setTimeout(() => {
        sendTokenToIframe();

        // 额外发送一个测试消息，确认通信正常
        if (iframe.value?.contentWindow) {
            iframe.value.contentWindow.postMessage(
                { test: 'parent test message', eve: 'parentTest' },
                '*'
            );
        }
    }, 300);
};

// 处理返回地球的函数
const handleBackToEarth = () => {
    // 这里可以添加返回地球的逻辑
    showBackButton.value = false;

    // 关闭所有弹窗
    if (showCameraList.value) {
        closeCameraList();
    }

    if (showVideoPlayer.value) {
        closeVideoPlayer();
    }

    if (showInternationalVideoPlayer.value) {
        closeInternationalVideoPlayer();
    }

    // 向iframe发送返回消息
    if (iframe.value) {
        iframe.value.contentWindow.postMessage(
            {
                eve: "cancle"
            },
            "*"
        );
    }
};

onMounted(() => {
    // 初始化错误修复系统
    try {
        initErrorFix();
        fixEZUIKitUnloadError();
        console.log('✅ 错误修复系统初始化完成');
    } catch (error) {
        console.warn('⚠️ 错误修复系统初始化失败:', error);
    }

    // 添加监听iframe发送的消息
    window.addEventListener('message', handleIframeMessage, false);

    // 等待DOM渲染完成后再发送token
    nextTick(() => {
        // 延迟一下确保iframe完全加载
        setTimeout(() => {
            sendTokenToIframe();
        }, 1000);
    });
});

// 组件卸载时清理资源
onUnmounted(() => {
    console.log('🧹 组件卸载，开始清理资源...');

    // 使用安全清理方法
    safeCleanup(() => {
        // 清理摄像头播放器
        if (currentVideoPlayer) {
            safeDestroyEZUIKit(currentVideoPlayer);
            currentVideoPlayer = null;
        }
    }, 'video player cleanup');

    safeCleanup(() => {
        // 清理国外项目视频播放器
        closeInternationalVideoPlayer();
    }, 'international video player cleanup');

    safeCleanup(() => {
        // 清理可见性监听器
        if (visibilityCleanup) {
            visibilityCleanup();
            visibilityCleanup = null;
        }
    }, 'visibility cleanup');

    safeCleanup(() => {
        // 清理所有EZUIKit相关资源
        cleanupEZUIKitResources();
    }, 'EZUIKit resources cleanup');

    safeCleanup(() => {
        // 移除消息监听器
        window.removeEventListener('message', handleIframeMessage);
    }, 'message listener cleanup');

    console.log('✅ 资源清理完成');
});
</script>

<style scoped>
.xianchang {
    pointer-events: all;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: pointer;
    z-index: 1;
}

.map {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: all;
    background: url("@/assets/images/xiangmu/wangge.png") no-repeat center center / 100% 100%;
}

.map iframe {
    width: 100%;
    height: 100%;
    resize: none;
}

/* 返回按钮样式 */
.back-button {
    position: absolute;
    top: 30px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
font-weight: normal;
font-size: 16px;
text-shadow: 0px 0px 4px #0085FF;
text-align: right;
font-style: normal;
text-transform: none;
}

.back-button:hover {
    opacity: 0.8;
    transform: translateX(-2px);
}

.back-button img {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.back-button span {
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    white-space: nowrap;
}

/* 视频模式时不显示点击手势 */
.xianchang.video-mode {
    cursor: default;
}

/* 关闭按钮样式 */
.close-icon {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: opacity 0.3s;
}

.close-icon:hover {
    opacity: 0.7;
}

.cardPop {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 1200px;
    height: 80%;
    max-height: 800px;
    z-index: 10;
    box-sizing: border-box;
}

/* 摄像头列表容器样式 */
.camera-list-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

/* 加载状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
}

/* 摄像头网格布局 */
.camera-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    padding: 20px;
    height: 100%;
    overflow-y: auto;
    /* background: rgba(0, 0, 0, 0.2);
    border-radius: 6px; */
}

/* 摄像头列表布局 */
.camera-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0;
    height: 100%;
    overflow-y: auto;
}

/* 摄像头行样式 */
.camera-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.camera-item:last-child {
    border-bottom: none;
}

/* 摄像头名称样式 */
.camera-name {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 20px;
    cursor: default;
    transition: color 0.3s ease;
}

/* 查看视频文字按钮 */
.view-btn {
    color: #00FFFF;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
    user-select: none;
}

.view-btn:hover:not(.disabled) {
    color: #66FFFF;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.6);
}

.view-btn.disabled {
    color: #999999;
    cursor: not-allowed;
    text-shadow: none;
}

.view-btn:active:not(.disabled) {
    color: #00CCCC;
}

/* 无数据提示 */
.no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(255, 255, 255, 0.6);
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
}

.no-data-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-data p {
    font-size: 16px;
    margin: 0;
}



/* 视频容器样式 - 改为响应式尺寸，完全适应父容器 */
.video-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    /* 使用百分比而不是固定像素 */
    max-width: 700px;
    /* 设置最大宽度防止过大 */
    height: 60%;
    /* 使用百分比高度 */
    max-height: 600px;
    /* 设置最大高度 */
    z-index: 10;
    box-sizing: border-box;
}

/* 图表头部样式 - 完整借鉴资产页面 */
.chart-header {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding-left: 50px;
    background: url('@/assets/images/zichan/box-title.png') no-repeat center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
}

.chart-headerVideo {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding-left: 50px;
    background: url('@/assets/images/xianchangVideo/bgTitle.png') no-repeat center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
}

.chart-headerVideo span {
    font-family: "Alimama", sans-serif;
    font-weight: 700;
    font-size: 20px;
    background-image: linear-gradient(to bottom,
            #006FD0 0%,
            #FFFFFF 50%,
            #006FD0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.chart-header span {
    font-family: "Alimama", sans-serif;
    font-weight: 700;
    font-size: 20px;
    background-image: linear-gradient(to bottom,
            #006FD0 0%,
            #FFFFFF 50%,
            #006FD0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* 图表主体样式 - 针对国外项目视频优化 */
.international-video-container .chart-body {
    width: 100%;
    height: calc(100% - 50px);
    padding: 10px;
    position: relative;
    background: url('@/assets/images/xianchangVideo/bgPop.png') no-repeat center;
    background-size: 100% 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* 图表内容区域 - 针对国外项目视频优化 */
.international-video-container .chart-content {
    width: 100%;
    height: 100%;
    position: relative;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
}

/* EZUIKit视频播放器容器 - 确保完全适应容器 */
.videoCard {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    overflow: hidden;
    background: #000;
    position: relative;
    /* 改为相对定位 */
    box-sizing: border-box;
    min-height: 200px;
    /* 设置最小高度 */
}

/* 加载状态覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    border-radius: 6px;
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 加载文本 */
.loading-text {
    color: white;
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    text-align: center;
}

/* 借鉴资产页面的字体和单位样式 */
.chart-title-num {
    font-family: TCloudNumber;
    font-weight: 700;
    font-size: 18px;
    background-image: linear-gradient(to bottom,
            #FFE49A 0%,
            #FFFFFF 50%,
            #FFE49A 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.unit {
    font-family: TCloudNumber;
    font-weight: 400;
    font-size: 12px;
    color: rgba(243, 247, 255, 0.7);
    line-height: 14px;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

/* 响应式设计 - 调整移动端尺寸 */
@media (max-width: 768px) {
    .cardPop {
        width: 95%;
        height: 85%;
    }

    .camera-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        padding: 15px;
    }

    .camera-preview {
        height: 140px;
    }

    .video-modal-content {
        width: 95%;
        height: 85%;
    }

    .chart-header {
        height: 35px;
        line-height: 35px;
        padding-left: 40px;
    }

    .chart-header span {
        font-size: 16px;
    }

    .chart-body {
        height: calc(100% - 35px);
        padding: 12px;
    }
}

/* 隐藏滚动条但保留滚动功能 */
.camera-grid::-webkit-scrollbar {
    display: none;
}

.popup-body::-webkit-scrollbar {
    display: none;
}

.camera-list::-webkit-scrollbar {
    display: none;
}

/* 为Firefox浏览器隐藏滚动条 */
.camera-grid,
.popup-body,
.camera-list {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* EZUIKit播放器样式优化 - 严格限制尺寸 */
:deep(.ez-player) {
    border-radius: 6px !important;
    overflow: hidden !important;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
}

:deep(.ez-player-container) {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    box-sizing: border-box !important;
}

:deep(.ez-player-video) {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
    box-sizing: border-box !important;
}

/* 强制限制所有EZUIKit相关元素的尺寸 */
:deep(.ezuikit-video-player) {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
}

:deep(.ezuikit-video-player *) {
    max-width: 100% !important;
    max-height: 100% !important;
    box-sizing: border-box !important;
}

/* 国外项目视频播放弹窗样式 */
.international-video-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 1400px;
    height: 80%;
    max-height: 900px;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: all;
}

.international-video-container {
    width: 100%;
    height: 100%;
    background: url('@/assets/images/zichan/box-content.png') no-repeat center/cover;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 主视频播放区域 */
.main-video-area {
    flex: 1;
    width: 100%;
    background: #000;
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    margin: 0;
    padding: 0;
}

/* 国外项目视频播放器样式 */
.international-video-player {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain;
    background: #000;
    border: none;
    outline: none;
    border-radius: 6px;
    display: block;
    max-width: 100%;
    max-height: 100%;
    vertical-align: middle;
    /* 确保视频在容器中居中显示 */
    position: relative;
    z-index: 1;
}

/* 确保视频控制条样式 */
.international-video-player::-webkit-media-controls-panel {
    background: rgba(0, 0, 0, 0.8);
}

.international-video-player::-webkit-media-controls {
    border-radius: 0 0 6px 6px;
}

/* 兼容Firefox */
.international-video-player::-moz-media-controls {
    background: rgba(0, 0, 0, 0.8);
}

/* 视频加载时的样式 */
.international-video-player:not([src]) {
    background: #000 url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjQ1IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIgb3BhY2l0eT0iMC4zIi8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZmlsbD0iI2ZmZiIgZm9udC1zaXplPSIxNCIgZm9udC1mYW1pbHk9IkFyaWFsIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBvcGFjaXR5PSIwLjciPuWKoOi9veS4rTwvdGV4dD4KPC9zdmc+') center center no-repeat;
}

.no-video {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
}

/* 视频控制区域 */
.video-controls {
    width: 100%;
    padding: 15px 0;
    background: rgba(0, 0, 0, 0.3);
}

.video-thumbnails {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
    max-height: 120px;
    overflow-y: auto;
}

.video-thumbnail {
    width: 120px;
    height: 80px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: #000;
}

.video-thumbnail:hover {
    border-color: rgba(0, 255, 255, 0.6);
    transform: scale(1.05);
}

.video-thumbnail.active {
    border-color: #00FFFF;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.thumbnail-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.thumbnail-overlay span {
    color: white;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* 项目信息显示区域 */
.project-info {
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 0 0 6px 6px;
    max-height: 150px;
    overflow-y: auto;
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #00FFFF;
    font-weight: 500;
    white-space: nowrap;
    margin-right: 8px;
    min-width: 80px;
}

.info-value {
    color: rgba(255, 255, 255, 0.9);
    flex: 1;
    line-height: 1.4;
    word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .international-video-popup {
        width: 95%;
        height: 85%;
        max-width: none;
        max-height: none;
    }

    .international-video-container .chart-body {
        padding: 8px;
    }

    .international-video-player {
        border-radius: 4px;
    }
}

@media (min-width: 1920px) {
    .international-video-popup {
        width: 80%;
        height: 75%;
        max-width: 1600px;
        max-height: 1000px;
    }
}

/* 大屏幕优化 */
@media (min-width: 2560px) {
    .international-video-popup {
        width: 70%;
        height: 70%;
        max-width: 2000px;
        max-height: 1200px;
    }
}

/* 国家/地区标题样式 */
.country-title {
    text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.8);
    color: white;
    z-index: 3;
    pointer-events: none;
}

/* 隐藏视频缩略图的滚动条 */
.video-thumbnails::-webkit-scrollbar {
    display: none;
}

.project-info::-webkit-scrollbar {
    width: 4px;
}

.project-info::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.project-info::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.3);
    border-radius: 2px;
}

.project-info::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 255, 0.5);
}



/* 云台控制按钮样式（参考示例代码） */
.videoControlbtn {
    width: 180px;
    height: 180px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    border: 5px solid rgba(0, 0, 0, 0.5);
    position: absolute;
    right: -200px;
    bottom: 50px;
    z-index: 1000;
}

.box_one,
.box_tow,
.box_three {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: rgba(83, 86, 87, 0.7);
    border: 5px solid rgba(0, 0, 0, 0.5);
}

.box_one {
    width: 140px;
    height: 140px;
    position: relative;
}

.iconBtn {
    position: absolute;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
}

.iconBtn:hover {
    color: rgba(201, 197, 197, 0.5);
}

.iconBtn:active {
    color: rgba(255, 255, 255, 0.7);
}

.box_tow {
    width: 65px;
    height: 65px;
    border: 5px solid rgba(0, 0, 0, 0.5);
}

.box_three {
    width: 45px;
    height: 45px;
    border: 5px solid rgba(55, 156, 158, 0.5);
    font-size: 22px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
}

.iconBtn2 {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
}

.iconBtn2:hover {
    color: rgba(201, 197, 197, 0.5);
}

.iconBtn2:active {
    color: rgba(255, 255, 255, 0.7);
}

/* 摄像头列表弹窗样式 */
.camera-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    max-width: 90%;
    height: 410px;
    max-height: 80%;
    background: url('@/assets/images/zichan/box-content.png') no-repeat center/cover;
    border-radius: 8px;
    z-index: 9999;
    overflow: hidden;
    pointer-events: all;
}

.popup-header {
    height: 50px;
    background: url('@/assets/images/zichan/box-title.png') no-repeat center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    padding-left: 40px;
    position: relative;
}

.popup-header span {
    font-family: "Alimama", sans-serif;
    font-weight: 700;
    font-size: 18px;
    background-image: linear-gradient(to bottom, #006FD0 0%, #FFFFFF 50%, #006FD0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.close-btn {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: opacity 0.3s;
}

.close-btn:hover {
    opacity: 0.7;
}

.popup-body {
    height: calc(100% - 50px);
    padding: 20px;
    overflow-y: auto;
    background: url('@/assets/images/xianchangVideo/bgPop.png') no-repeat center;
    background-size: 100% 100%;
}

/* 视频播放弹窗样式 */
.video-popup {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    max-width: 90%;
    height: 730px;
    max-height: 80%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: all;
}

.video-container {
    width: 800px;
    max-width: 90%;
    height: 600px;
    max-height: 80%;
    background: url('@/assets/images/zichan/box-content.png') no-repeat center/cover;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

/* 图表头部样式 */
.chart-header {
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding-left: 60px;
    background: url('@/assets/images/zichan/box-title.png') no-repeat center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;
}

.chart-header span {
    font-family: "Alimama", sans-serif;
    font-weight: 700;
    font-size: 18px;
    background-image: linear-gradient(to bottom, #006FD0 0%, #FFFFFF 50%, #006FD0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* 图表主体样式 */
.chart-body {
    width: 100%;
    height: calc(100% - 50px);
    padding: 15px;
    position: relative;
    background: url('@/assets/images/xianchangVideo/bgPop.png') no-repeat center;
    background-size: 100% 100%;
    box-sizing: border-box;
    overflow: hidden;
}

/* 图表内容区域 */
.chart-content {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
    box-sizing: border-box;
    overflow: hidden;
}

/* EZUIKit视频播放器容器 */
.videoCard {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    overflow: hidden;
    background: #000;
    position: relative;
    box-sizing: border-box;
    min-height: 400px;
}

/* 加载状态覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    border-radius: 6px;
}

/* EZUIKit播放器样式优化 */
:deep(.ez-player) {
    border-radius: 6px !important;
    overflow: hidden !important;
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
}

:deep(.ez-player-container) {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    box-sizing: border-box !important;
}

:deep(.ez-player-video) {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
    box-sizing: border-box !important;
}
</style>