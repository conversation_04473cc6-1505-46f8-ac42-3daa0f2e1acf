<template>
    <div class="zhiliang-container">
        <!-- <img class="pointer-events-auto cursor-pointer absolute top-[30px] left-[60px]"
            src="@/assets/images/xiangmu/back.png" alt="" @click="back" style="z-index: 3" /> -->
            <div class="back-button" @click="back">
            <img src="@/assets/images/header/back.png" alt="" />
            <span>返回</span>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const back = () => {
    router.push({ name: 'yibuyipingDetail' });
};

</script>

<style scoped lang="scss">
.back-button {
    position: absolute;
    top: 30px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
}
.zhiliang-container {
    pointer-events: all;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-image: url('@/assets/images/yibuyiping/zhiliang.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    position: absolute;
    top: 10px;
}
</style>