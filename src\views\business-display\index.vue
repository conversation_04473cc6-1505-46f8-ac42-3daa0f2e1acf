<template>
  <div class="business-display-container">
    <!-- 简化的头部 -->
    <div class="simple-header">
      <!-- 中间标题 -->
      <div class="header-center">
        <div class="title-wrapper">
          <img src="@/assets/images/header/ywbj.png" alt="背景" class="title-bg" />
        </div>
      </div>
      
      <!-- 右上角控制区域 -->
      <div class="header-controls">
        <!-- 展开/收起控制 -->
        <div class="control-item" @click="toggleExpand">
          <span class="control-text">{{ isExpanded ? '收起' : '展开' }}</span>
        </div>
        
        <!-- 切换到现场视频的图标 -->
        <div class="control-item video-switch" @click="goToVideo" title="现场视频">
          <svg class="video-icon" viewBox="0 0 24 24" width="24" height="24">
            <path fill="currentColor" d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- 业务布局内容 - 完全复制项目首页的内容 -->
    <div class="project-container absolute top-0 left-0 pt-[120px] px-[20px] box-border w-full h-full bg-no-repeat bg-cover flex justify-between" 
         :class="{ 'expanded': isExpanded }">
      <div class="leftList" v-if="!isShow">
        <div class="items" v-for="item in itemList" :key="item.id">
          <div class="item-img">
            <RotatingCircle style="width: 130px;height: 130px;"><svg-icon :name="item.svgName" color="#fff"
                style="width: 48px;height: 48px;" /></RotatingCircle>
          </div>
          <div class="item-content">
            <div class="item-title">{{ item.name }}</div>
            <div class="item-amount">
              <span class="text-[36px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
                <count-to v-if="item.id === 3 || item.id === 4 || item.id === 5" :start-val="0" :end-val="item.amount"
                  :duration="2000" :decimals="0" :autoplay="true" separator="," />
                <template v-else>{{ formattedNumber(item.amount) }}</template>
              </span><span class="bgfont text-[26px]">{{ item.unit }}</span>
            </div>
            <!-- Personnel breakdown for Overseas Personnel -->
            <div v-if="item.id == '6'" class="personnel-breakdown">
              <div class="breakdown-row">
                <span class="breakdown-label">{{ $t('project.chinese') }}</span>
                <span class="breakdown-value">{{ formattedNumber(foreignData?.中方 || 0) }}</span>
                <span class="breakdown-unit">{{ $t('project.people') }}</span>
              </div>
              <div class="breakdown-row">
                <span class="breakdown-label">{{ $t('project.foreign') }}</span>
                <span class="breakdown-value">{{ formattedNumber(foreignData?.外籍 || 0) }}</span>
                <span class="breakdown-unit">{{ $t('project.people') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="rightList" v-if="!isShow">
        <div class="items" v-for="item in itemListRight" :key="item.id">
          <div class="item-img">
            <RotatingCircle style="width: 100px;height: 100px;"><svg-icon :name="item.svgName" color="#fff"
                style="width: 48px;height: 48px;" /></RotatingCircle>
          </div>
          <div class="item-content">
            <div class="item-title">{{ item.name }}</div>
            <div class="item-amount">
              <span class="text-[36px] font-extrabold text-[#EEF4FF] mr-2 bgfont">
                <count-to v-if="item.id === 3 || item.id === 4 || item.id === 5" :start-val="0"
                  :end-val="parseAmount(item.amount)" :duration="2000" :decimals="0" :autoplay="true" separator="," />
                <template v-else>{{ formattedNumber(item.amount) }}</template>
              </span><span class="bgfont text-[26px]">{{ item.unit }}</span>
            </div>
            <div></div>
          </div>
          <div class="absolute right-3 top-20">
            <div v-if="item.id == '6'">
              <span
                style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;margin-right: 6px;">{{
                  $t('project.formalEmployees') }}</span>
              <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
                formattedNumber(innerData?.正式员工 || 0) }}</span>{{ $t('project.people') }}
            </div>
            <div v-if="item.id == '6'">
              <span
                style="background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%); border-radius: 0px 0px 0px 0px;margin-right: 6px;">{{
                  $t('project.otherEmployment') }}</span>
              <span class="bgfont" style="font-weight: bold;font-size: 22px;">{{
                formattedNumber(innerData?.其他形式用工 || 0) }}</span>{{ $t('project.people') }}
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- 地球/地图显示 - 独立显示层 -->
    <div class="earth-map-container">
      <iframe src="./zjsj-yydp/index.html" id="iframe" class="w-full h-full iframe-container"></iframe>
    </div>
  </div>
</template>

<script setup>
import { CountTo } from 'vue3-count-to';
import RotatingCircle from '@/components/RotatingCircle.vue';
import { onMounted, ref, onUnmounted, computed, watch } from "vue"; 
import { useRouter, useRoute } from "vue-router";
import { useI18n } from 'vue-i18n';
import country from "@/assets/images/layout/country.png";
import project from "@/assets/images/layout/project.png";
import amount from "@/assets/images/layout/amount.png";
import produce from "@/assets/images/layout/produce.png";
import engineering from "@/assets/images/layout/engineering.png";
import people from "@/assets/images/layout/people.png";
import request from '@/utils/request';

const router = useRouter();
const route = useRoute();
const { t, locale } = useI18n();

// 简化头部的状态
const isExpanded = ref(false);

// 原项目页面的状态
const iframe = ref(null);
const showBack = ref(false);
const isShow = ref(false);
const foreignData = ref(null);
const innerData = ref(null);

// 展开/收起功能
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 跳转到现场视频页面（新的副本）
const goToVideo = () => {
  router.push('/video-display');
};

// 境外数据列表 - 计算属性
const itemList = computed(() => {
  if (!foreignData.value) return [];
  return [
    {
      id: 1,
      src: country,
      name: t('project.dataFields.overseasBusinessCountries'),
      amount: Number(foreignData.value.境外业务布局国家 || 0),
      unit: t('project.units.project'),
      svgName: "home-1"
    },
    {
      id: 2,
      src: project,
      name: t('project.dataFields.overseasProjects'),
      amount: Number(foreignData.value.境外在建项目 || 0),
      unit: t('project.units.project'),
      svgName: "home-2"
    },
    {
      id: 3,
      src: amount,
      name: t('project.dataFields.contractAmount'),
      amount: Number(foreignData.value.合同总额 || 0),
      unit: t('project.units.hundredMillionUSD'),
      svgName: "home-3-l"
    },
    {
      id: 6,
      src: people,
      name: t('project.dataFields.overseasPersonnel'),
      amount: Number(foreignData.value.项目总人数 || 0),
      unit: t('project.units.person'),
      svgName: "home-6"
    }
  ];
});

// 境内数据列表 - 计算属性
const itemListRight = computed(() => {
  if (!innerData.value) return [];
  return [
    {
      id: 1,
      src: country,
      name: t('project.dataFields.domesticBusinessProvinces'),
      amount: Number(innerData.value.境内业务省份 || innerData.value.在建项目总数 || 0),
      unit: t('project.units.project'),
      svgName: "home-1"
    },
    {
      id: 2,
      src: project,
      name: t('project.dataFields.domesticProjectsWithDesign'),
      amount: Number(innerData.value.国内在建项目 || innerData.value.在建项目总数 || 0),
      unit: t('project.units.project'),
      svgName: "home-2"
    },
    {
      id: 3,
      src: amount,
      name: t('project.dataFields.contractAmount'),
      amount: parseAmount(innerData.value.合同总额 || innerData.value.在建项目合同总额 || 0),
      unit: t('project.units.hundredMillionYuan'),
      svgName: "home-3-r"
    },
    {
      id: 4,
      src: produce,
      name: t('project.dataFields.currentYearCollectionRate'),
      amount: '81.52',
      unit: '%',
      svgName: "home-4-r"
    }
  ];
});

const formattedNumber = (value) => {
  if (typeof value == "number") {
    return value.toLocaleString();
  } else {
    return value;
  }
};

const parseAmount = (value) => {
  if (typeof value === 'string') {
    return Number(value.replace(/,/g, ''));
  }
  return value;
};

const getForeignInfo = async () => {
  try {
    const response = await request.get('/globalManage/zjmanage/largescreen/getForeignInfo');
    if (response.code === 0 && response.data.length > 0) {
      foreignData.value = response.data[0];
      console.log('境外数据', foreignData.value)
    }
  } catch (error) {
    console.error('获取境外数据失败:', error);
  }
};

const getInnerInfo = async () => {
  try {
    const response = await request.get('/globalManage/zjmanage/largescreen/getInnerInfo', {
      params: { gb: '中国' }
    });
    console.log('中国')
    if (response.code === 0 && response.data.length > 0) {
      innerData.value = response.data[0];
    }
  } catch (error) {
    console.error('获取境内数据失败:', error);
  }
};

onMounted(async () => {
  // 初始化iframe
  iframe.value = document.getElementById("iframe");
  
  // 获取数据
  await getForeignInfo();
  await getInnerInfo();
  
  // 向iframe发送token等初始化消息
  if (iframe.value) {
    console.log(iframe.value)

    setTimeout(() => {
        iframe.value.contentWindow.postMessage(
        {
          type: "token",
          data: sessionStorage.getItem('token'),
        },
        "*"
      );
      console.log('发送token')
    }, 2000);
  }
});
</script>

<style lang="scss" scoped>
.business-display-container {
  width: 100%;
  height: 100%;
  background: #0a0f1a;
  position: relative;
  overflow: hidden;
}
.item-title {
  font-size: 30px;
}

.simple-header {
  width: 100%;
  height: 122px;
  position: fixed;
  top: 0px;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-center {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-bg {
  height: 120px;
}

.title-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  width: 100%;
  text-align: center;
}

.platform-title {
  font-size: 40px;
  color: #EFF8FC;
  font-family: YouSheBiaoTiHei, sans-serif;
  font-weight: 400;
  letter-spacing: 3px;
  text-shadow: inset 0px 0px 1px rgba(255, 255, 255, 0.8), 
               0px 0px 7px rgba(130, 165, 255, 0.54), 
               0px 2px 0px rgba(19, 80, 143, 0.66);
  white-space: nowrap;
}

.header-controls {
  position: absolute;
  top: 19px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 101;
}

.control-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  pointer-events: auto;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.control-text {
  color: white;
  font-size: 18px;
  font-family: Alibaba PuHuiTi 2.0, sans-serif;
  font-weight: normal;
}

.video-switch {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
  }
}

.video-icon {
  color: white;
  width: 24px;
  height: 24px;
}

/* 项目页面的原始样式 */
.project-container {
  pointer-events: none;
  background: transparent;
  z-index: 3;
  transition: all 0.5s ease;
  
  &.expanded {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  .leftList {
    display: flex;
    flex-direction: column;
    position: relative;
    justify-content: space-around;
    height: 80%;
    top: 40px;
    z-index: 99;
    margin-left: 240px;
    pointer-events: auto;

    .item-img {
      position: relative;
      top: -16px;
    }

    .items {
      display: flex;
      gap: 20px;
      position: relative;

      .item-amount {
        font-family: "xiaoweiLogo";
      }
    }
  }

  .rightList {
    display: flex;
    flex-direction: column;
    position: relative;
    justify-content: space-around;
    height: 80%;
    top: 60px;
    z-index: 99;
    margin-right: 240px;
    padding: 0 20px;
    pointer-events: auto;

    .item-img {
      position: relative;
      top: -16px;
    }

    .items {
      display: flex;
      gap: 20px;
      position: relative;

      .item-amount {
        font-family: "xiaoweiLogo";
      }
    }
  }

  .rotate-perspective {
    perspective: 800px;

    .rotate-item1 {
      transform: rotate3d(0, 1, 0, 5.5deg);
    }

    .rotate-item2 {
      transform: rotate3d(0, 1, 0, -5.5deg);
    }
  }
}

.earth-map-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: all;
  background: url("@/assets/images/xiangmu/wangge.png") no-repeat center center / 100% 100%;
  
  .iframe-container {
    width: 100%;
    height: 100%;
    border: none;
  }
}

.bgfont {
  font-family: TCloudNumber;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.personnel-breakdown {
  margin-top: 10px;
  width: 100%;
}

.breakdown-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
  margin: 4px auto;
}

.breakdown-label {
  background: linear-gradient(180deg, rgba(0,132,255,0) 0%, #0084FF 50%, rgba(0,132,255,0) 100%);
  border-radius: 0px 0px 0px 0px;
  font-size: 18px;
  padding: 2px 8px;
  color: #fff;
  flex-shrink: 0;
}

.breakdown-value {
  font-weight: bold;
  font-size: 22px;
  color: #fff;
  margin: 0 10px;
  flex-grow: 1;
  text-align: right;
}

.breakdown-unit {
  font-size: 16px;
  color: #fff;
  flex-shrink: 0;
}
</style> 