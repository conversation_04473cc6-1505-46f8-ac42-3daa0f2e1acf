/**
 * 
 * @returns 动画类
 */
class Animations {
  /**
   * 设置元素的位移及旋转动画
   * @param {*} object 元素对象
   * @param {*} positions 元素位置 数组
   * @param {*} rotates 元素角度 数组
   * @param {*} deltas 元素动画时间 数组
   * @param {*} params 其它参数
   * @returns 
   */
  setPositionsAndRotates = (
    object,
    positions,
    rotates,
    deltas,
    params
  ) => {
    if (!params) params = {};
    if (!params.datas) params.datas = [];
    //设置位置和角度
    let _scope = object;
    let length = positions.length;
    if (length < 1) return;
    // if (!object._tweens) object._tweens = [];
    // let prev = object._tweens[object._tweens.length - 1];
    object._tweens = [];
    let prev = null;
    let start = positions[0];
    for (let i = 1; i < length; i++) {
      let tween = this.getTween({
        obj: start,
        to: positions[i],
        delta: deltas[i],
        object: _scope,
        rotate: rotates[i],
        param: params,
        data: params.datas[i],
        index: i,
        total: length,
        callback: function (param) {
          if (param.object && param.object._tweens) {
            if (param.object._tweens[0].isOver) {
              if (param.param.callback) param.param.callback(param.object);
            }
            param.object._tweens[0].stop();
            param.object._tweens.splice(0, 1);
            param.object._currTween = param.object._tweens[0];
            if(param.object._tweens[0])param.object._tweens[0].start();
          }
          // if (param && param.object && param.rotate) {
          //     if (param.rotate.x) param.object.rotation.x = param.rotate.x;
          //     if (param.rotate.y) param.object.rotation.y = param.rotate.y;
          //     if (param.rotate.z) param.object.rotation.z = param.rotate.z;
          // }
        },
        update: function (obj, param) {
          // _scope.getGeometry().setCoordinates(obj);
          if (param.param.update)
            param.param.update(obj, param.data, param.index, param.total,param.object,obj);
          // window.map.getView().animate({ center: obj });
        },
      });
      if (!prev) {
        tween.start();
      } else {
        prev.chain(tween);
        // prev.nextTween = tween;
      }
      prev = tween;
      if(!object._currTween) object._currTween = tween;
      start = positions[i];
      if (i == length - 1) {
        tween.isOver = true;
      }
      _scope._tweens.push(tween);
    }
  };
  //设置位移动画
  setPosition = (object, param) => {
    if (!param) param = {};
    if (object._tween) object._tween.stop();
    object._tween = this.getTween({
      obj: param.start,
      to: param.position,
      delta: param.delta,
      object: object,
      param: param,
      callback: function (param) {
        if (param.param.callback) param.param.callback(param);
      },
      update: function (obj, param) {
        if (param.param.update) param.param.update(obj, param);
      },
    }).start();
  };
  getTween = (param) => {
    let obj = param.obj,
      to = param.to,
      delta = param.delta,
      callback = param.callback,
      update = param.update;
    return new TWEEN.Tween(obj)
      .to(to, delta || 80) //Infinity    一直循环
      .easing(TWEEN.Easing.Linear.None)
      .onComplete(function () {
        if (callback) callback(param);
      })
      .onUpdate(function (_obj) {
        if (update) update(obj, param);
      });
      // .onStop(function () {
      //   if (callback) callback(param);
      // })
  };
  updateTween = () => {
      if (TWEEN && TWEEN.update) TWEEN.update();
      this.animateId = window.requestAnimationFrame(this.updateTween);
  };
  //停止动画
  stop = () => {
      if (this.animateId) window.cancelAnimationFrame(this.animateId);
  };
  //暂停动画
  pause = (object) => {
    if(object&&object._currTween)object._currTween.pause();
  };
  //播放动画
  play = (object) => {
    if(object&&object._currTween)object._currTween.resume();
  };
  //移除所有动画
  removeAnimates = (object) => {
    if (object._tweens) {
      for (let i = 0; i < object._tweens.length; i++){
        object._tweens[i].stop();
      }
      object._tweens = [];
    }
  }
  update = ()=> {
    TWEEN.update();
  }
}
