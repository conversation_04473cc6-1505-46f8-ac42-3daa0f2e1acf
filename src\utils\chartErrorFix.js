/**
 * 修复Chrome扩展message port错误的工具
 * 用于处理 "Unchecked runtime.lastError: The message port closed before a response was received." 错误
 */

/**
 * 全局错误处理器，过滤Chrome扩展相关的错误
 */
export function setupGlobalErrorHandler() {
    // 处理未捕获的错误
    window.addEventListener('error', (event) => {
        const message = event.message || event.error?.message || ''

        if (isChromeExtensionError(message)) {
            console.warn('Chrome扩展错误已被忽略:', message)
            event.preventDefault()
            return false
        }
    })

    // 处理Promise rejection
    window.addEventListener('unhandledrejection', (event) => {
        const message = event.reason?.message || event.reason || ''

        if (isChromeExtensionError(message)) {
            console.warn('Chrome扩展Promise错误已被忽略:', message)
            event.preventDefault()
            return false
        }
    })

    // 包装console.error以过滤扩展错误
    const originalConsoleError = console.error
    console.error = (...args) => {
        const message = args.join(' ')
        if (isChromeExtensionError(message)) {
            console.warn('Chrome扩展console错误已被过滤:', message)
            return
        }
        originalConsoleError.apply(console, args)
    }
}

/**
 * 检查是否为Chrome扩展相关错误
 * @param {string} message - 错误消息
 * @returns {boolean}
 */
function isChromeExtensionError(message) {
    const chromeExtensionErrorPatterns = [
        /The message port closed before a response was received/i,
        /Extension context invalidated/i,
        /Could not establish connection/i,
        /chrome-extension:\/\//i,
        /runtime\.lastError/i,
        /chrome\.runtime/i
    ]

    return chromeExtensionErrorPatterns.some(pattern => pattern.test(message))
}

/**
 * 安全的异步操作包装器
 * @param {Function} asyncFn - 异步函数
 * @param {string} operation - 操作名称
 * @returns {Promise}
 */
export async function safeAsyncOperation(asyncFn, operation = 'unknown') {
    try {
        return await asyncFn()
    } catch (error) {
        const message = error.message || error.toString()

        if (isChromeExtensionError(message)) {
            console.warn(`${operation}操作中的Chrome扩展错误已被忽略:`, message)
            return null
        }

        // 重新抛出非扩展相关的错误
        throw error
    }
}

/**
 * 安全的ECharts初始化包装器
 * @param {Function} initFunction - 初始化函数
 * @param {string} chartName - 图表名称
 * @returns {any}
 */
export function safeChartInit(initFunction, chartName = 'chart') {
    return safeAsyncOperation(
        () => Promise.resolve(initFunction()),
        `${chartName}初始化`
    )
}

/**
 * 清理函数包装器，确保清理过程中的错误不会影响用户体验
 * @param {Function} cleanupFn - 清理函数
 * @param {string} context - 清理上下文
 */
export function safeCleanup(cleanupFn, context = 'component') {
    try {
        cleanupFn()
    } catch (error) {
        const message = error.message || error.toString()

        if (isChromeExtensionError(message)) {
            console.warn(`${context}清理过程中的Chrome扩展错误已被忽略:`, message)
        } else {
            console.error(`${context}清理过程中发生错误:`, error)
        }
    }
}

/**
 * 延迟执行函数，避免在组件卸载过程中执行可能出错的操作
 * @param {Function} fn - 要执行的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {number} 定时器ID
 */
export function delayedExecution(fn, delay = 100) {
    return setTimeout(() => {
        safeAsyncOperation(fn, 'delayed execution')
    }, delay)
}

/**
 * 组件卸载时的安全清理函数
 * @param {Array} cleanupTasks - 清理任务数组
 */
export function safeUnmount(cleanupTasks = []) {
    cleanupTasks.forEach((task, index) => {
        safeCleanup(task, `cleanup task ${index + 1}`)
    })
}

/**
 * 初始化错误修复系统
 */
export function initErrorFix() {
    // 设置全局错误处理器
    setupGlobalErrorHandler()

    // 监听页面卸载事件
    window.addEventListener('beforeunload', () => {
        console.log('页面即将卸载，准备清理资源...')
    })

    console.log('Chrome扩展错误修复系统已初始化')
} 