@font-face {
  font-family: "hyzyt";
  src: url("./HYk1gj.woff") format("woff"),
    url("./HYk1gj.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "ShuHei";
  src: url("./alimamashuheiti.ttf");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "Logo";
  src: url("./xiaowei.woff") format("woff"),
    url("./xiaowei.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Alimama";
  src: url("./AlimamaShuHeiTi-Bold.ttf");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("./YouSheBiaoTiHei-2.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "pingfang";
  src: url("./pinfang.ttf");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "MicrosoftYaHei";
  src: url("./MicrosoftYaHei.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Oswald Medium";
  src: url("./Oswald-Medium.ttf");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "xiaoweiLogo";
  src: url("./xiaoweiLogo.otf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "PangMenZhengDao";
  src: url("./庞门正道标题体.ttf");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "TCloudNumber";
  src: url("./TCloudNumberVF.ttf");
  font-weight: normal;
  font-style: normal;
}
