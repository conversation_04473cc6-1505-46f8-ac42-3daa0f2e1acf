<template>
  <div class="login-bg">
    <div class="login-panel">
      <div class="login-subtitle">{{ $t('login.subtitle') }}</div>
      <div class="login-title">{{ $t('login.title') }}</div>
      <el-form ref="loginFormRef" :model="param" :rules="rules" class="login-form" status-icon>
        <el-form-item prop="username">
          <el-input v-model="param.username" :placeholder="$t('login.username')">
            <template #prefix>
              <svg-icon name="account" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="param.password" :type="passwordType" :placeholder="$t('login.password')"
            @keyup.enter="submitForm">
            <template #prefix>
              <svg-icon name="pwd" />
            </template>
            <template #suffix>
              <el-icon @click="switchPass" style="cursor: pointer">
                <component :is="passwordLock ? 'View' : 'Hide'" />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input v-model="param.captcha" :placeholder="$t('login.captcha')" class="captcha-input"
              @keyup.enter="submitForm">
              <template #prefix>
                <el-icon>
                  <Document />
                </el-icon>
              </template>
            </el-input>
            <div class="captcha-display" @click="refreshCaptcha">
              <span class="captcha-text">{{ captchaText }}</span>
            </div>
          </div>
        </el-form-item>
        <div class="login-options">
          <el-checkbox v-model="param.remember">{{ $t('login.rememberPassword') }}</el-checkbox>
          <a class="forgot" href="#">{{ $t('login.forgotPassword') }}</a>
        </div>
        <el-button type="primary" class="login-btn" :loading="btnLoading" @click="submitForm">{{ $t('login.loginButton')
        }}</el-button>
        <!-- <div class="login-protocol">
          <el-checkbox v-model="param.agree">我已阅读并同意 <a href="#">服务协议</a>、
            <a href="#">隐私协议</a>
          </el-checkbox>
        </div> -->
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import request, { setupTokenRefresh } from '@/utils/request'

const { t } = useI18n()
const btnLoading = ref(false)
const loginFormRef = ref(null)
const passwordLock = ref(true)
const passwordType = ref('password')
const captchaText = ref('')

const param = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: true,
  agree: true,
})

const rules = computed(() => ({
  username: [
    { required: true, message: t('login.usernameRequired'), trigger: 'blur' },
    { min: 3, max: 20, message: t('login.usernameLengthError'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('login.passwordRequired'), trigger: 'blur' },
    { min: 6, max: 20, message: t('login.passwordLengthError'), trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: t('login.captchaRequired'), trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value.toLowerCase() !== captchaText.value.toLowerCase()) {
          callback(new Error(t('login.captchaError')))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  agree: [{ required: true, validator: (_, v) => v, message: '请同意协议', trigger: 'change' }]
}))

// 生成随机验证码
const generateCaptcha = () => {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaText.value = generateCaptcha()
  param.captcha = ''
}

const switchPass = () => {
  if (passwordLock.value) {
    passwordType.value = 'text'
  } else {
    passwordType.value = 'password'
  }
  passwordLock.value = !passwordLock.value
}

const router = useRouter()

const submitForm = async () => {
  if (!loginFormRef.value) return

  try {
    btnLoading.value = true
    await loginFormRef.value.validate()

    // 验证码校验
    if (param.captcha.toLowerCase() !== captchaText.value.toLowerCase()) {
      ElMessage.error(t('login.captchaError'))
      refreshCaptcha()
      return
    }

    const res = await request.post('/system/auth/login', {
      username: param.username,
      password: param.password,
    })

    if (res.code === 0) {
      localStorage.setItem("currentActiveTab", '业务布局')
      // 使用 setupTokenRefresh 函数来设置 token
      setupTokenRefresh(res.data.accessToken, res.data.refreshToken)
      ElMessage.success(t('login.loginSuccess'))
      router.push('/project/index')
    } else {
      ElMessage.error(res.msg || t('login.loginFailed'))
      refreshCaptcha() // 登录失败时刷新验证码
    }
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error(t('login.loginError'))
    refreshCaptcha() // 登录失败时刷新验证码
  } finally {
    btnLoading.value = false
  }
}

// 组件挂载时生成验证码
onMounted(() => {
  refreshCaptcha()
})
</script>

<style scoped>
.login-bg {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/login/bg.png') no-repeat;
  background-size: 100% 100%;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10%;
  position: relative;
  pointer-events: all;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

.login-left {
  flex: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 600px;
  height: 100vh;
  position: relative;
}

.tech-decoration {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.circle-1 {
  width: 300px;
  height: 300px;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 200px;
  height: 200px;
  animation: float 8s ease-in-out infinite;
  animation-delay: -2s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  animation: float 10s ease-in-out infinite;
  animation-delay: -4s;
}

@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
  }

  100% {
    transform: translateY(0px) rotate(360deg);
  }
}

.login-panel {
  flex: 1;
  max-width: 500px;
  min-width: 350px;
  padding: 80px 60px 80px 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
  background: rgb(52, 122, 203, 0.1);
  border-radius: 16px 16px 16px 16px;
  border: 1px solid rgba(255, 255, 255, 0.38);
}

.login-title {
  font-family: "YouSheBiaoTiHei";
  font-weight: 400;
  font-size: 38px;
  color: #fff;
  text-align: center;
  letter-spacing: 1px;
  margin-bottom: 30px;
}

.login-subtitle {
  color: #b0c4de;
  font-size: 12px;
  text-align: center;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

.login-form {
  width: 100%;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

.captcha-container {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.captcha-input {
  flex: 2;
}

.captcha-display {
  flex: 1;
  height: 40px;
  background: rgb(255, 255, 255, 0.7);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.captcha-display:hover {
  background: #fff;
  transform: translateY(-1px);
}

.captcha-text {
  font-size: 22px;
  font-weight: bold;
  color: #087EEA;
  letter-spacing: 3px;
  font-family: 'Courier New', monospace;
}

.role-group {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 8px;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  font-size: 13px;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

.forgot {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  opacity: 0.5;
}

.login-btn {
  width: 100%;
  margin-bottom: 18px;
  height: 40px;
  font-size: 20px;
  letter-spacing: 13px;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
  background: #087EEA;
}

.login-protocol {
  font-size: 12px;
  color: #b0c4de;
  text-align: left;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

.login-protocol a {
  color: #fff;
  margin: 0 2px;
  text-decoration: underline;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

:deep(.el-radio) {
  color: #fff;
  margin-right: 0;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #11bcd4 !important;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #11bcd4 !important;
  background-color: #11bcd4 !important;
}

:deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: none;
  height: 40px;
}

:deep(.el-input__inner) {
  color: #fff;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

:deep(.el-input__prefix) {
  color: #b0c4de;
}

:deep(.el-checkbox__label) {
  color: rgba(255, 255, 255, 0.5);
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}

:deep(.el-checkbox.is-checked .el-checkbox__input) {
  border-color: #2656F5 !important;
  background-color: #2656F5 !important;
}

:deep(.el-checkbox.is-checked .el-checkbox__inner) {
  border-color: #2656F5 !important;
  background-color: #2656F5 !important;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: rgba(255, 255, 255, 0.5) !important;
  font-family: 'Alibaba-PuHuiTi-Medium', sans-serif;
}
</style>