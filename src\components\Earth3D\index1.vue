<template>
  <div class="earth-container" ref="container">
    <div class="color-picker">
      <label>地球颜色：</label>
      <input type="color" v-model="earthColor" @input="updateEarthColor">
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import earthTexture from '@/assets/images/textures/earth5.png'
import earthTexture2 from '@/assets/images/textures/earth_point.png'

// 声明响应式引用
const container = ref(null)
const animationFrameId = ref(null)
const earthColor = ref('#000d1a')

// 声明Three.js相关变量（非响应式）
let _scene, _camera, _renderer, _earth, _earth2, _earth3, _controls
const autoRotationDelay = 2000 // 2秒后恢复自转
const autoRotateSpeed = 0.001 // 自转速度

// 初始化Three.js场景
const initThree = () => {

  window.THREE = THREE

  // 创建场景
  _scene = new THREE.Scene()
  window.scene = _scene

  // 创建相机
  _camera = new THREE.PerspectiveCamera(
    45,
    container.value.clientWidth / container.value.clientHeight,
    0.1,
    1000
  )
  window.camera = _camera
  // _camera.position.z = 2

  // 创建渲染器
  _renderer = new THREE.WebGLRenderer({ 
    antialias: true,
    alpha: true  // 启用透明背景
  })
  _renderer.setSize(container.value.clientWidth, container.value.clientHeight)
  _renderer.setPixelRatio(window.devicePixelRatio)
  _renderer.setClearColor(0x000000, 0)  // 设置透明背景
  container.value.appendChild(_renderer.domElement)

  // 创建地球
  createEarth()

  // 添加光源
  addLights()

  // 添加控制器
  _controls = new OrbitControls(_camera, _renderer.domElement)
  _controls.enableDamping = true
  _controls.dampingFactor = 0.05
  _controls.rotateSpeed = 1.0 // 增加旋转速度
  _controls.minDistance = 3
  _controls.maxDistance = 10
  // 限制只能水平旋转
  _controls.minPolarAngle = Math.PI / 2
  _controls.maxPolarAngle = Math.PI / 2
  // 禁用平移
//   _controls.enableZoom = false
  // _controls.enablePan = false
  // 启用自动旋转
  _controls.autoRotate = true
  _controls.autoRotateSpeed = 0.4 // 增加自动旋转速度

  // 添加控制器事件监听
  _controls.addEventListener('start', () => {
    _controls.autoRotate = false
  })

  _controls.addEventListener('end', () => {
    setTimeout(() => {
      _controls.autoRotate = true
    }, autoRotationDelay)
  })

  // 创建环线
  createRings()

  // // 添加星空背景
  // createStars()
}

// 创建地球
const createEarth = () => {
  // 创建球体几何体
  const geometry = new THREE.SphereGeometry(1, 64, 64)

  // 加载地球纹理
  const texture = new THREE.TextureLoader().load(earthTexture)
  const texture2 = new THREE.TextureLoader().load(earthTexture2)

  window.material = new THREE.MeshStandardMaterial({
    map: texture,
    transparent: true,
    opacity: 1,
    // alphaTest: 0.7,
    depthWrite: false,
    // emissive: new THREE.Color(earthColor.value),
    side: 2,
  })

  window.material2 = new THREE.MeshStandardMaterial({
    map: texture2,
    transparent: true,
    opacity: 0.5,
    depthWrite: false,
    emissive: new THREE.Color(earthColor.value),
    side: 2,
  })

  // 创建地球网格
  _earth = new THREE.Mesh(geometry, window.material)
  _earth2 = _earth.clone()
  _earth3 = _earth.clone()
  _earth3.name = 'earth3'
  _earth2.material.blending = 2
  _earth3.scale.set(1.35, 1.35, 1.35)
  _earth3.material = window.material2
  // 创建一个组来包含地球
  _renderer.earthGroup = new THREE.Group()
  _renderer.earthGroup.add(_earth)
  _renderer.earthGroup.add(_earth2)
  _renderer.earthGroup.add(_earth3)

  _renderer.earthGroup.position.set(0, -0.2, 0)
 
  _scene.add(_renderer.earthGroup)
  
}

// 添加光源
const addLights = () => {
  // 添加环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 1)
  _scene.add(ambientLight)

  // 添加点光源
  // const pointLight = new THREE.PointLight(0xffffff, 1)
  // pointLight.position.set(-10, 10, 10)
  // _scene.add(pointLight)

  // window.pointLight = pointLight
  window.ambientLight = ambientLight
}

// 创建星空背景
const createStars = () => {
  const starsGeometry = new THREE.BufferGeometry()
  const starsMaterial = new THREE.PointsMaterial({
    color: 0xffffff,
    size: 0.02,
    sizeAttenuation: true
  })

  const starsVertices = []
  for (let i = 0; i < 10000; i++) {
    const x = THREE.MathUtils.randFloatSpread(2000)
    const y = THREE.MathUtils.randFloatSpread(2000)
    const z = THREE.MathUtils.randFloatSpread(2000)
    starsVertices.push(x, y, z)
  }

  starsGeometry.setAttribute(
    'position',
    new THREE.Float32BufferAttribute(starsVertices, 3)
  )
  const stars = new THREE.Points(starsGeometry, starsMaterial)
  _scene.add(stars)
}

// 创建环线
const createRings = () => {
  // 定义环线数量和间距
  const numRings = 10 // 环线数量
  const startY = 0.95 // 最上面的环的位置
  const endY = -0.95 // 最下面的环的位置
  const step = (endY - startY) / (numRings - 1) // 环线之间的间距
  const radiusOffset = 0.15 // 环线相对地球的额外半径

  // 创建渐变纹理
  const createGradientTexture = () => {
    const canvas = document.createElement('canvas')
    canvas.width = 256
    canvas.height = 1
    const context = canvas.getContext('2d')
    
    // 创建渐变
    const gradient = context.createLinearGradient(0, 0, canvas.width, 0)
    gradient.addColorStop(0, 'rgba(0, 208, 255, 0)')
    gradient.addColorStop(0.4, 'rgba(0, 208, 255, 1)')
    gradient.addColorStop(0.5, 'rgba(0, 208, 255, 1)')
    gradient.addColorStop(0.6, 'rgba(0, 208, 255, 1)')
    gradient.addColorStop(1, 'rgba(0, 208, 255, 0)')
    
    context.fillStyle = gradient
    context.fillRect(0, 0, canvas.width, canvas.height)
    
    return new THREE.CanvasTexture(canvas)
  }

  const glowTexture = createGradientTexture()
  glowTexture.wrapS = THREE.RepeatWrapping
  glowTexture.wrapT = THREE.RepeatWrapping

  for (let i = 0; i < numRings; i++) {
    const y = startY + step * i
    // 根据y位置计算环的半径 (使用毕达哥拉斯定理)，并添加额外半径
    const baseRadius = Math.sqrt(1 - y * y)
    const radius = baseRadius * (1 + radiusOffset)
    
    // 创建环线几何体
    const ringGeometry = new THREE.RingGeometry(radius - 0.01, radius, 64)
    
    // 创建基础环线材质
    const ringMaterial = new THREE.MeshBasicMaterial({
      color: 0x0092ff,
      side: THREE.DoubleSide,
      transparent: true,
      opacity: 0.3
    })

    // 创建光晕环线材质
    const glowMaterial = new THREE.MeshBasicMaterial({
      map: glowTexture.clone(), // 每个环使用独立的纹理实例
      side: THREE.DoubleSide,
      transparent: true,
      opacity: 0.4,
      blending: THREE.AdditiveBlending
    })
    
    // 创建基础环线和光晕环线
    const ring = new THREE.Mesh(ringGeometry, ringMaterial)
    const glowRing = new THREE.Mesh(
      new THREE.RingGeometry(radius - 0.01, radius, 64),
      glowMaterial
    )
    
    // 创建一个组来包含环
    _renderer.ringGroup = new THREE.Group()
    _renderer.ringGroup.add(ring)
    _renderer.ringGroup.add(glowRing)
    
    // 设置环的位置和旋转
    _renderer.ringGroup.position.y = y * (1 + radiusOffset)
    ring.rotation.x = -Math.PI / 2
    glowRing.rotation.x = -Math.PI / 2
    
    // 存储初始偏移量，使每个环的光晕位置不同
    glowMaterial.map.offset.x = i / numRings
    
    // 将材质添加到一个数组中，以便在动画循环中更新
    if (!window.glowMaterials) window.glowMaterials = []
    window.glowMaterials.push(glowMaterial)


    _renderer.earthGroup.add(_renderer.ringGroup)
    
    // // 添加到场景
    // _scene.add(_renderer.ringGroup)
  }
}

// 动画循环
const animate = () => {
  animationFrameId.value = requestAnimationFrame(animate)

  // 更新控制器
  if (_controls) {
    _controls.update()
  }

  // 更新光晕动画
  if (window.glowMaterials) {
    window.glowMaterials.forEach(material => {
      material.map.offset.x -= 0.008  // 调整这个值可以改变光晕移动速度
    })
  }

  _renderer.render(_scene, _camera)
}

// 窗口大小改变处理
const onWindowResize = () => {
  _camera.aspect = container.value.clientWidth / container.value.clientHeight
  _camera.updateProjectionMatrix()
  _renderer.setSize(container.value.clientWidth, container.value.clientHeight)
}

// 更新地球颜色
const updateEarthColor = () => {
  if (window.material) {
    window.material.emissive = new THREE.Color(earthColor.value)
  }
}

// 生命周期钩子
onMounted(() => {
  initThree()
  animate()
  window.addEventListener('resize', onWindowResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', onWindowResize)
  
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
  }
  
  // 清理Three.js资源
  if (_earth) {
    _earth.geometry.dispose()
    _earth.material.dispose()
  }
  if (_renderer) {
    _renderer.dispose()
  }
})
</script>

<style scoped>
.earth-container {
  width: 100%;
  height: 100%;
  position: relative;
  pointer-events: all;
  /* filter: brightness(2); */
}

.color-picker {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 10;
}

.color-picker label {
  color: white;
  font-size: 14px;
}

.color-picker input[type="color"] {
  width: 50px;
  height: 30px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

canvas {
  position: relative;
  pointer-events: auto !important;
}
</style> 