<template>
  <div ref="chartRef" :style="{ width: width, height: height }"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import axios from 'axios'

const props = defineProps({
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '400px'
  },
  data: {
    type: Array,
    default: () => []
  },
  legend: {
    type: Array,
    default: () => []
  },
  xData: {
    type: Array,
    default: () => []
  },
  xDateUnit: {
    type: String,
    default: ''
  },
  smooth: {
    type: Boolean,
    default: true
  },
  showArea: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref(null)
let chart = null

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  const option = {
    backgroundColor: 'transparent',
    legend: {
      itemHeight: 2,
      itemWidth: 20,
      data: props.legend,
      textStyle: {
        color: '#fff'
      },
      // icon: 'circle',
      right: 10,
      top: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          show: false,
          color: '#fff'
        },
        shadowStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: 'rgba(255, 255, 255, 0)' },
              { offset: 0.3, color: 'rgba(255, 255, 255, 0)' },
              { offset: 1, color: 'rgba(255, 255, 255, 0.18)' }
            ]
          ),
        },
        lineStyle: {
          width: 1
        }
      },
      backgroundColor: 'rgba(0,0,0,0.3)',
      textStyle: {
        color: '#fff'
      },
      padding: [10, 10],
      formatter: function(params) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach(item => {
          result += `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:6px;height:6px;background-color:${item.borderColor};"></span>`;
          result += `${item.seriesName}: ${item.value}<br/>`;
        });
        return result;
      }
    },
    grid: {
      top: '18%',
      left: '0%',
      right: '0%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.xData,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#fff',
        formatter: (value) => {
          return value + props.xDateUnit
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      interval: 100,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    series: props.data.map((item, index) => ({
      name: item.name,
      type: 'line',
      smooth: props.smooth,
      symbol: 'circle',
      symbolSize: 1,
      showSymbol: false,
      lineStyle: {
        width: 1,
        color: item.itemStyle.color
      },
      itemStyle: {
        color: '#fff',
        shadowColor: item.itemStyle.color,
        shadowOffsetX: 0,
        shadowOffsetY: 0,
        opacity: 1,
        shadowBlur: 6,
        borderColor: item.itemStyle.color,
        borderWidth: 1
      },
      areaStyle: props.showArea ? {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: getRgbaFromColor(item.itemStyle.color, 0.3)
          },
          {
            offset: 1,
            color: getRgbaFromColor(item.itemStyle.color, 0)
          }
        ])
      } : undefined,
      data: item.data,
    }))
  }

  // // 渐变柱状图
  // option.series.push({
  //   type: 'bar',
  //   barWidth: '1%',
  //   itemStyle: {
  //     normal: {
  //         color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
  //             offset: 0,
  //             color: 'rgba(255,255,255,0.0)'
  //         }, {
  //             offset: 1,
  //             color: 'rgba(255,255,255,0.6)'
  //         }]),
  //         barBorderRadius: 11,
  //     }
  //   },
  //   data: [0,400,0,0,0,0,0,0,0,0,0,0]
  // })
  chart?.setOption(option)
}

// 添加颜色处理函数
const getRgbaFromColor = (color, alpha) => {
  // 解析rgba格式
  const match = color.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/);
  if (match) {
    const [_, r, g, b] = match;
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  }
  return color;
};

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', chart?.resize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', chart?.resize)
})
</script>