import { ref, onMounted, markRaw } from "vue"
import * as echarts from "echarts";
import { getImg, getPercent, formatNumber } from "@/utils/method";

export function useRight() {
  const pandianList = ref([
    {
      id: 1,
      title: '本年盘点进度',
      num: 89.8,
      unit: '%'
    },
    {
      id: 2,
      title: '本年划拨',
      num: 3376,
      unit: '万元'
    },
    {
      id: 3,
      title: '本年转让',
      num: 18493,
      unit: '万元'
    },
  ])
  const zichanInfo = ref([
    {
      id: 1,
      name: '中江国际南非项目部',
      num: 23570
    },
    {
      id: 2,
      name: '中江国际科特迪瓦项目部',
      num: 21778
    },
    {
      id: 3,
      name: '中江国际加蓬项目部',
      num: 17092
    },
    {
      id: 4,
      name: '中江国际塔吉克斯坦项目部',
      num: 9778
    },
    {
      id: 5,
      name: '中江国际莫桑比克项目组',
      num: 3216
    },
    {
      id: 6,
      name: '中江国际乌干达项目组',
      num: 2254
    },
    {
      id: 7,
      name: '中江国际刚果金项目组',
      num: 2023
    },
    
  ])
  const totalValue = ref()
  const useRate = ref()
  const datalist = [
    {id: 1, name: '使用', value: 1573217},
    {id: 2, name: '维修', value: 219273},
    {id: 3, name: '报废', value: 83389},
    {id: 4, name: '空闲', value: 699724},
  ]
  totalValue.value = datalist.reduce((sum, obj) => sum + obj.value, 0)
  useRate.value = (datalist[0].value / totalValue.value * 100).toFixed(1)
  let colorList = [
    "#27C7FF",
    "#FFB300",
    "#F11E1E",
    "#00FFFF",
  ];
  const convertToRGBA = (color, opacity) => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

  const colors = colorList.map(color => convertToRGBA(color, 0.3));
  let data = [];
  let data2 = []
  for (let i = 0; i < datalist.length; i++) {
    data.push(
      {
        value: datalist[i].value,
        name: datalist[i].name,
        itemStyle: {
          // shadowBlur: 20,
          color: colorList[i],
          // shadowColor: colorList[i],
        },
      },
      {
        value: 30000,
        name: "",
        itemStyle: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: "rgba(0, 0, 0, 0)",
          borderColor: "rgba(0, 0, 0, 0)",
          borderWidth: 0,
        },
      }
    );
    data2.push(
      {
        value: datalist[i].value,
        name: datalist[i].name,
        itemStyle: {
          // shadowBlur: 20,
          color: colors[i],
          // shadowColor: colorList[i],
        },
      },
      {
        value: 30000,
        name: "",
        itemStyle: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          color: "rgba(0, 0, 0, 0)",
          borderColor: "rgba(0, 0, 0, 0)",
          borderWidth: 0,
        },
      }
    );
    
  }
  
  const chartRef = ref()
  const pieChart = ref()

  function initCharts() {
    if(!pieChart.value)
        pieChart.value = markRaw(echarts.init(chartRef.value));
    else pieChart.value.clear();
    pieChart.value.setOption({
      // color: colorList,
      tooltip: {
        show: false,
      },
      graphic: [
        {
          type: 'text',
          left: '43%',
          top: '38%',
          style: {
            text: useRate.value,
            fill: '#fff',
            fontSize: 38  ,
            fontWeight: 400,
            fontFamily:  'Logo'
          }
        },
        {
          type: 'text',
          left: '56%',
          top: '46%',
          style: {
            text: '%',
            fill: '#fff',
            fontSize: 14,
            fontWeight: 400,
          }
        },
        {
          type: 'text',
          left: '43%',
          top: '55%',
          style: {
            text: '资产使用率',
            fill: '#fff',
            fontSize: 14,
            fontWeight: 400,
            fontFamily:  'ShuHeiTi'
          }
        },
      ],
      series: [
        {
          //外环大小
          type: 'pie',
          radius: ['48%', '55%'],
          center: ["50%", "50%"],
          z: 10,
          clockwise:false,
          label: {
            show: true,
            lineHeight: 10,
            color: "inherit",
            formatter: function (params) {
              if (params.name !== "") {
                let data = datalist;
                let total = 0;
                let tarValue;
                for (let i = 0; i < data.length; i++) {
                  total += data[i].value;
                  if (data[i].name == params.name) {
                    tarValue = data[i].value;
                  }
                }
                let v = tarValue;
                let per = getPercent(v, total, 1);
                return [
                   `{a|${params.name}}\n` +  `{dot|}\n` + `{d|${formatNumber(params.value)} 万元}` ,
                ];
              } else {
                return "";
              }
            },
            rich: {
              dot: {
                backgroundColor: "auto",
                height: 5,
                width: 5,
                padding: [5, -5, -5, -5],
                borderRadius: 5,
              },
              a: {
                fontSize: 14,
                color: '#ffffff',
                fontWeight: 400,
                fontFamily: 'Regular',
                left: 50,
                padding: [-20, -50, -20, -50],
                width: 30,
              },
              d: {
                fontSize: 14,
                fontWeight: 400,
                color: '#fff',
                width: 30,
                padding: [-10, -70, -20, -50],
              },
              f: {
                fontSize: 14,
                fontWeight: 400,
                color: '#fff',
                padding: [-20, -80, -20, -80],
                width: 30
              },
              },
          },
          labelLine: {
            show: true,
            length: 20,
            length2: 80,
            smooth: 0,
            
          },
          emphasis: {
            label: {
              show: true,
              fontSize: "14",
            },
          },
          data: data,
        },
        {
          name:'',
          radius: ['40%','55%'],
          center: ["50%", "50%"],
          type:'pie',
          gap: 1.71,
          clockwise:false,
          itemStyle: {
            color: function (params) {
              return colorList[params.dataIndex % colorList.length] + "40";
            },
            
          },
          label: {
            show:false
          },
          emphasis: {
            label: {
              show:false
            }
          },
          labelLine: {
            show:false,
          },
          animation:true,
          tooltip: {
            show:true
          },
          silent: true,
          data: data2
        }
      ],
    });
  }
  onMounted(() => {
    initCharts()
  })


  return {
    pandianList, zichanInfo, chartRef, pieChart,
  }
}