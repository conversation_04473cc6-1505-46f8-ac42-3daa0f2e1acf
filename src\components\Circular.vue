<template>
  <div :style="{ width: width, height: height, position: 'relative' }">
    <div
      class="chart"
      ref="chartRef"
      :style="{ width: width, height: height }"
    ></div>
    <div class="legend">
      <div v-for="(item, index) in list" :key="index">
        {{ item.name }} <span>{{ item.value }}</span
        >{{ unit }}
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch } from "vue";
  import * as echarts from "echarts";
  import { getImg } from "@/utils/method";
  const props = defineProps({
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "100%",
    },
    unit: {
      type: String,
      default: "(万元)",
    },
    list: {
      type: Array,
      default: () => [],
    },
  });
  let data = [
    {
      name: "去年",
      value: 754,
    },
    {
      name: "今年",
      value: 611,
    },
  ];
  const chartRef = ref(null);
  let chart = null;

  const initChart = () => {
    if (!chartRef.value) return;
    chart = echarts.init(chartRef.value);
    updateChart();
  };

  const updateChart = () => {
    let arrName = getArrayValue(data, "name");
    let arrValue = getArrayValue(data, "value");
    let sumValue = eval(arrValue.join("+"));
    let objData = array2obj(data, "name");
    let optionData = getData(data);
    function getArrayValue(array, key) {
      var key = key || "value";
      var res = [];
      if (array) {
        array.forEach(function (t) {
          res.push(t[key]);
        });
      }
      return res;
    }

    function array2obj(array, key) {
      var resObj = {};
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i];
      }
      return resObj;
    }

    function getData(data) {
      var res = {
        series: [],
        yAxis: [],
      };
      for (let i = 0; i < data.length; i++) {
        // console.log([70 - i * 15 + '%', 67 - i * 15 + '%']);
        res.series.push({
          name: "",
          type: "pie",
          clockWise: true, //顺时加载
          hoverAnimation: false, //鼠标移入变大
          radius: [73 - i * 15 + "%", 68 - i * 15 + "%"],
          center: ["60%", "50%"],
          label: {
            show: false,
          },
          itemStyle: {
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            borderWidth: 5,
          },

          data: [
            {
              value: data[i].value,
              name: data[i].name,
            },
            {
              value: sumValue - data[i].value,
              name: "",
              itemStyle: {
                color: "rgba(0,0,0,0)",
                borderWidth: 0,
              },
              tooltip: {
                show: false,
              },
              hoverAnimation: false,
            },
          ],
        });
        res.series.push({
          name: "",
          type: "pie",
          silent: true,
          z: 1,
          clockWise: true, //顺时加载
          hoverAnimation: false, //鼠标移入变大
          radius: [73 - i * 15 + "%", 68 - i * 15 + "%"],
          center: ["60%", "50%"],
          label: {
            show: false,
          },
          itemStyle: {
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            borderWidth: 5,
          },
          data: [
            {
              value: 7.5,
              itemStyle: {
                color: "rgb(3, 31, 62)",
                borderWidth: 0,
              },
              tooltip: {
                show: false,
              },
              hoverAnimation: false,
            },
            {
              value: 2.5,
              name: "",
              itemStyle: {
                color: "rgba(0,0,0,0)",
                borderWidth: 0,
              },
              tooltip: {
                show: false,
              },
              hoverAnimation: false,
            },
          ],
        });
        res.yAxis.push(((data[i].value / sumValue) * 100).toFixed(2) + "%");
      }
      return res;
    }

    let option = {
      tooltip: {
        show: true,
        trigger: "item",
        formatter: "{a}<br>{b}:{c}({d}%)",
      },
      color: [
        "RGB(0,142,255)",
        "RGB(0,152,101)",
        "rgb(22, 75, 205)",
        "rgb(52, 52, 176)",
      ],
      grid: {
        top: "0%",
        bottom: "0%",
        left: "0%",
        containLabel: false,
      },
      yAxis: [
        {
          type: "category",
          inverse: true,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          data: optionData.yAxis,
        },
      ],
      xAxis: [
        {
          show: false,
        },
      ],
      graphic: {
        elements: [
          {
            type: "image",
            style: {
              image: getImg("jingying/211.png"),
              width: 92,
              height: 92,
            },
            top: "center",
            left: "42%",
          },
        ],
      },
      series: optionData.series,
    };
    chart?.setOption(option);
  };

  // 监听数据变化
  watch(
    () => props.list,
    (newVal) => {
      data = newVal;
      updateChart();
    },
    { deep: true }
  );

  onMounted(() => {
    initChart();
    window.addEventListener("resize", chart?.resize);
  });

  onUnmounted(() => {
    chart?.dispose();
    window.removeEventListener("resize", chart?.resize);
  });
</script>
<style lang="scss" scoped>
  .legend {
    width: 180px;
    height: 70px;
    position: absolute;
    top: 25px;
    left: 0px;
    div {
      font-size: 14px;
      color: #fff;
      span {
        font-family: Oswald Medium;
        font-size: 20px;
        color: #b3e5ff;
        margin-right: 10px;
      }
    }
  }
</style>
