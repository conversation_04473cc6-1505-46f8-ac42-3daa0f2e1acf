<template>
  <div class="content-container">
    <!-- 3D旋转卡片 -->
    <div class="center_contion">
      <!-- 两个底座 -->
      <div class="base base-small"></div>
      <div class="base base-large"></div>
      <div class="light"></div>

      <!-- 保留原有内容 -->
      <div class="wrapper">
        <div class="box" ref="box">
          <div class="ball ball_1" @click="handleDetail('2025')" :class="index === 0 ? 'active' : ''">
            <p class="count-p">
              <count-to :start-val="0" :end-val="yearData['2025']" :duration="2000" :decimals="2" :autoplay="true"
                separator="," class="count-number" /><span>亿元</span>
            </p>
            <p class="data">{{ $t('business.revenue2025') }}</p>
          </div>
          <div class="ball ball_2" @click="handleDetail('2024')" :class="index === 1 ? 'active' : ''">
            <p class="count-p">
              <count-to :start-val="0" :end-val="yearData['2024']" :duration="2000" :decimals="2" :autoplay="true"
                separator="," class="count-number" /><span>亿元</span>
            </p>
            <p class="data">{{ $t('business.revenue2024') }}</p>
          </div>
          <div class="ball ball_3" @click="handleDetail('2023')" :class="index === 2 ? 'active' : ''">
            <p class="count-p">
              <count-to :start-val="0" :end-val="yearData['2023']" :duration="2000" :decimals="2" :autoplay="true"
                separator="," class="count-number" /><span>亿元</span>
            </p>
            <p class="data">{{ $t('business.revenue2023') }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { CountTo } from 'vue3-count-to';
import request from '@/utils/request'

const router = useRouter();
const yearData = ref({
  '2023': 0,
  '2024': 0,
  '2025': 0
});
const index = ref(0);
const x = ref(0);
const timer = ref(null);
const box = ref(null);

// 获取数据的函数
async function fetchBusinessData() {
  try {
    const res = await request.get('/globalManage/group/T-group-business/page', {
      params: {
        pageSize: 50
      }
    });
    console.log(res);

    if (res.code === 0) {
      // 处理年度营收数据
      res.data.list.forEach(item => {
        if (item.businessName === '年度营收') {
          yearData.value[item.year] = item.thisyearAmount;
        }
      });
    }
  } catch (error) {
    console.error('获取数据失败:', error);
  }
}

// 跳转到对应的业务页面
function handleDetail(year) {
  router.push({
    path: '/business/business1',
    query: { year }
  });
}

// 旋转到下一个卡片
function roll() {
  if (!box.value) return;

  x.value = x.value - 120;
  index.value = index.value === 2 ? 0 : index.value + 1;

  box.value.style.transform = `rotateX(-8deg) rotateY(${x.value}deg)`;
}

// 旋转到指定索引的卡片
function rotateTo(targetIndex) {
  if (!box.value) return;

  clearTimeout(timer.value);

  const diff = (targetIndex - index.value + 3) % 3;

  if (diff === 1) {
    x.value -= 120;
  } else if (diff === 2) {
    x.value -= 240;
  }

  index.value = targetIndex;
  box.value.style.transform = `rotateX(-8deg) rotateY(${x.value}deg)`;

  timer.value = setTimeout(() => {
    startAutoRotate();
  }, 4000);
}

// 开始自动旋转
function startAutoRotate() {
  clearInterval(timer.value);
  timer.value = setInterval(roll, 4000);
}

// 组件挂载时获取数据
onMounted(async () => {
  await fetchBusinessData();
});

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  clearInterval(timer.value);
});
</script>

<style lang="scss" scoped>
.content-container {
  pointer-events: all;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/images/jingying/groupAssetsBg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 0;
}

.business-assets-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.business-assets-wrapper {
  display: flex;
  justify-content: space-around;
  width: 80%;
  max-width: 1200px;
}

.business-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 20px;
}

.item-base {
  width: 240px;
  height: auto;
}

.item-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -30%);
  text-align: center;
  color: #fff;
}

.item-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
}

.unit {
  font-size: 14px;
  margin-left: 4px;
}

.item-label {
  font-size: 16px;
}

/* 添加3D旋转卡片样式 */
.center_contion {
  width: 37%;
  height: 35%;
  position: absolute;
  left: 20%;
  transform: translateX(-50%);
  /* 移除原来的背景图 */
  /* background-image: url("@/assets/images/jingying/Frame 1321316728.png"); */
  display: flex;
  justify-content: center;
  position: relative;
}

/* 添加中间偏上的图标 */
.center_contion::before {
  content: "";
  width: 100%;
  height: 80%;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url("@/assets/images/jingying/assets.png");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 3;
}

/* 底座共有样式 */
.center_contion .base {
  // position: absolute;
  // background-image: url("@/assets/images/jingying/assetsBase.png");
  // background-size: contain;
  // background-position: center bottom;
  // background-repeat: no-repeat;
  // transform: perspective(800px) rotateX(60deg);
  // left: 50%;
  // z-index: 1;
}

/* 小底座 - 旋转 */
.center_contion .base-small {
  width: 640px;
  height: 640px;
  bottom: -320px;
  left: 50%;
  margin-left: -320px;
  background-image: url("@/assets/images/jingying/assetsBase1.png");
  background-position: center center;
  transform-origin: center center;
  transform: perspective(800px) rotateX(75deg);
  animation: rotate 8s linear infinite;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  z-index: 2;
  position: absolute;
  top: -24%;
}

/* 大底座 - 静止 */
.center_contion .base-large {
  width: 760px;
  height: 760px;
  bottom: -320px;
  left: 50%;
  margin-left: -380px;
  background-image: url("@/assets/images/jingying/assetsBase2.png");
  background-position: center center;
  background-size: 100% 100%;
  transform-origin: center center;
  transform: perspective(800px) rotateX(75deg);
  z-index: 1;
  position: absolute;
  top: -33%;
}

/* 高光效果 */
.center_contion .light {
  width: 760px;
  height: 200px;
  position: absolute;
  bottom: -67px;
  left: 50%;
  margin-left: -380px;
  background-image: url("@/assets/images/jingying/light.png");
  background-size: contain;
  background-position: center bottom;
  background-repeat: no-repeat;
  z-index: 3;
}

/* 旋转动画 */
@keyframes rotate {
  from {
    transform: perspective(800px) rotateX(75deg) rotateZ(0deg);
  }

  to {
    transform: perspective(800px) rotateX(75deg) rotateZ(360deg);
  }
}

.wrapper {
  margin-top: 16%;
  perspective: 3000px;
  perspective-origin: 599px 146px;
  position: relative;
  z-index: 4;
}

.box {
  width: 450px;
  transform: rotateY(0deg) rotateX(-8deg);
  transform-style: preserve-3d;
  transition-duration: 1s;
  margin: 0 auto;
}

.ball {
  width: 260px;
  height: 200px;
  text-align: center;
  position: absolute;
  cursor: pointer;
  background-size: 100% 100%;
  color: #fff;
  background-image: url("/src/assets/images/jingying/groupAssetsItem.png");

  .count-p {
    font-family: TCloudNumber;
    font-weight: bold;
    font-size: 30px;
    color: #FFFFFF;
    line-height: 56px;
    text-align: center;
    font-style: normal;
    text-transform: none;

    :deep(.count-number) {
      font-family: TCloudNumber !important;
      font-weight: bold !important;
      font-size: 30px !important;
      color: #FFFFFF !important;
      line-height: 56px !important;
      text-align: center !important;
      font-style: normal !important;
      text-transform: none !important;
    }

    span {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 20px;
      color: rgba(229, 245, 255, 0.8);
      line-height: 23px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .data {
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 22px;
    color: #E5F5FF;
    line-height: 36px;
    text-shadow: 2px 2px 5px rgba(17, 20, 22, 0.22), 0px 0px 10px rgba(73, 223, 255, 0.34);
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .icon {
    width: 75px;
    height: 75px;
    margin-top: 40px;
  }
}

.ball:nth-child(1) {
  transform: rotateY(350deg) translateZ(652px) translateY(121px)translateX(305px);
}

.ball:nth-child(2) {
  position: absolute;
  left: 143%;
  top: 20px;
}

.ball:nth-child(3) {
  position: absolute;
  left: -102%;
  top: 20px;
}

.active {
  width: 240px;
  height: 200px;

  .count-p {
    text-align: center;
    font-size: 46px;
    font-weight: bold;
    color: #fff;
    text-indent: 10px;

    :deep(.count-number) {
      font-size: 46px !important;
      font-weight: bold !important;
      color: #fff !important;
      text-align: center !important;
    }
  }

  .icon {
    width: 86px !important;
    height: 86px !important;
    margin-left: 6px;
  }

  .data {
    padding-left: 10px;
  }
}
</style>
