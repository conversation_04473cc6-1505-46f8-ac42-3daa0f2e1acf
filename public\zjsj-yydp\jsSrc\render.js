var TWEEN = TWEEN || function() {
    var a = [];
    return {
        REVISION: "7",
        getAll: function() {
            return a
        },
        removeAll: function() {
            a = []
        },
        add: function(c) {
            a.push(c)
        },
        remove: function(c) {
            c = a.indexOf(c);
            -1 !== c && a.splice(c, 1)
        },
        update: function(c) {
            if (0 === a.length)
                return !1;
            var f = 0
              , g = a.length;
            for (c = void 0 !== c ? c : Date.now(); f < g; )
                a[f].update(c) ? f++ : (a.splice(f, 1),
                g--);
            return !0
        }
    }
}();
TWEEN.Tween = function(a) {
    var c = {}
      , f = {}
      , g = 1E3
      , l = 0
      , p = null
      , d = TWEEN.Easing.Linear.None
      , w = TWEEN.Interpolation.Linear
      , e = []
      , m = null
      , A = !1
      , C = null
      , B = null;
    this.to = function(a, b) {
        null !== b && (g = b);
        f = a;
        return this
    }
    ;
    this.start = function(e) {
        TWEEN.add(this);
        A = !1;
        p = void 0 !== e ? e : Date.now();
        p += l;
        for (var b in f)
            if (null !== a[b]) {
                if (f[b]instanceof Array) {
                    if (0 === f[b].length)
                        continue;
                    f[b] = [a[b]].concat(f[b])
                }
                c[b] = a[b]
            }
        return this
    }
    ;
    this.stop = function() {
        TWEEN.remove(this);
        return this
    }
    ;
    this.delay = function(a) {
        l = a;
        return this
    }
    ;
    this.easing = function(a) {
        d = a;
        return this
    }
    ;
    this.interpolation = function(a) {
        w = a;
        return this
    }
    ;
    this.chain = function() {
        e = arguments;
        return this
    }
    ;
    this.onStart = function(a) {
        m = a;
        return this
    }
    ;
    this.onUpdate = function(a) {
        C = a;
        return this
    }
    ;
    this.onComplete = function(a) {
        B = a;
        return this
    }
    ;
    this.update = function(l) {
        if (l < p)
            return !0;
        !1 === A && (null !== m && m.call(a),
        A = !0);
        var b = (l - p) / g, b = 1 < b ? 1 : b, r = d(b), v;
        for (v in c) {
            var y = c[v]
              , q = f[v];
            a[v] = q instanceof Array ? w(q, r) : y + (q - y) * r
        }
        null !== C && C.call(a, r);
        if (1 == b) {
            null !== B && B.call(a);
            b = 0;
            for (r = e.length; b < r; b++)
                e[b].start(l);
            return !1
        }
        return !0
    }
}
;
TWEEN.Easing = {
    Linear: {
        None: function(a) {
            return a
        }
    },
    Quadratic: {
        In: function(a) {
            return a * a
        },
        Out: function(a) {
            return a * (2 - a)
        },
        InOut: function(a) {
            return 1 > (a *= 2) ? .5 * a * a : -.5 * (--a * (a - 2) - 1)
        }
    },
    Cubic: {
        In: function(a) {
            return a * a * a
        },
        Out: function(a) {
            return --a * a * a + 1
        },
        InOut: function(a) {
            return 1 > (a *= 2) ? .5 * a * a * a : .5 * ((a -= 2) * a * a + 2)
        }
    },
    Quartic: {
        In: function(a) {
            return a * a * a * a
        },
        Out: function(a) {
            return 1 - --a * a * a * a
        },
        InOut: function(a) {
            return 1 > (a *= 2) ? .5 * a * a * a * a : -.5 * ((a -= 2) * a * a * a - 2)
        }
    },
    Quintic: {
        In: function(a) {
            return a * a * a * a * a
        },
        Out: function(a) {
            return --a * a * a * a * a + 1
        },
        InOut: function(a) {
            return 1 > (a *= 2) ? .5 * a * a * a * a * a : .5 * ((a -= 2) * a * a * a * a + 2)
        }
    },
    Sinusoidal: {
        In: function(a) {
            return 1 - Math.cos(a * Math.PI / 2)
        },
        Out: function(a) {
            return Math.sin(a * Math.PI / 2)
        },
        InOut: function(a) {
            return .5 * (1 - Math.cos(Math.PI * a))
        }
    },
    Exponential: {
        In: function(a) {
            return 0 === a ? 0 : Math.pow(1024, a - 1)
        },
        Out: function(a) {
            return 1 === a ? 1 : 1 - Math.pow(2, -10 * a)
        },
        InOut: function(a) {
            return 0 === a ? 0 : 1 === a ? 1 : 1 > (a *= 2) ? .5 * Math.pow(1024, a - 1) : .5 * (-Math.pow(2, -10 * (a - 1)) + 2)
        }
    },
    Circular: {
        In: function(a) {
            return 1 - Math.sqrt(1 - a * a)
        },
        Out: function(a) {
            return Math.sqrt(1 - --a * a)
        },
        InOut: function(a) {
            return 1 > (a *= 2) ? -.5 * (Math.sqrt(1 - a * a) - 1) : .5 * (Math.sqrt(1 - (a -= 2) * a) + 1)
        }
    },
    Elastic: {
        In: function(a) {
            var c, f = .1;
            if (0 === a)
                return 0;
            if (1 === a)
                return 1;
            !f || 1 > f ? (f = 1,
            c = .1) : c = .4 * Math.asin(1 / f) / (2 * Math.PI);
            return -(f * Math.pow(2, 10 * --a) * Math.sin(2 * (a - c) * Math.PI / .4))
        },
        Out: function(a) {
            var c, f = .1;
            if (0 === a)
                return 0;
            if (1 === a)
                return 1;
            !f || 1 > f ? (f = 1,
            c = .1) : c = .4 * Math.asin(1 / f) / (2 * Math.PI);
            return f * Math.pow(2, -10 * a) * Math.sin(2 * (a - c) * Math.PI / .4) + 1
        },
        InOut: function(a) {
            var c, f = .1;
            if (0 === a)
                return 0;
            if (1 === a)
                return 1;
            !f || 1 > f ? (f = 1,
            c = .1) : c = .4 * Math.asin(1 / f) / (2 * Math.PI);
            return 1 > (a *= 2) ? -.5 * f * Math.pow(2, 10 * --a) * Math.sin(2 * (a - c) * Math.PI / .4) : .5 * f * Math.pow(2, -10 * --a) * Math.sin(2 * (a - c) * Math.PI / .4) + 1
        }
    },
    Back: {
        In: function(a) {
            return a * a * (2.70158 * a - 1.70158)
        },
        Out: function(a) {
            return --a * a * (2.70158 * a + 1.70158) + 1
        },
        InOut: function(a) {
            return 1 > (a *= 2) ? .5 * a * a * (3.5949095 * a - 2.5949095) : .5 * ((a -= 2) * a * (3.5949095 * a + 2.5949095) + 2)
        }
    },
    Bounce: {
        In: function(a) {
            return 1 - TWEEN.Easing.Bounce.Out(1 - a)
        },
        Out: function(a) {
            return a < 1 / 2.75 ? 7.5625 * a * a : a < 2 / 2.75 ? 7.5625 * (a -= 1.5 / 2.75) * a + .75 : a < 2.5 / 2.75 ? 7.5625 * (a -= 2.25 / 2.75) * a + .9375 : 7.5625 * (a -= 2.625 / 2.75) * a + .984375
        },
        InOut: function(a) {
            return .5 > a ? .5 * TWEEN.Easing.Bounce.In(2 * a) : .5 * TWEEN.Easing.Bounce.Out(2 * a - 1) + .5
        }
    }
};
TWEEN.Interpolation = {
    Linear: function(a, c) {
        var f = a.length - 1
          , g = f * c
          , l = Math.floor(g)
          , p = TWEEN.Interpolation.Utils.Linear;
        return 0 > c ? p(a[0], a[1], g) : 1 < c ? p(a[f], a[f - 1], f - g) : p(a[l], a[l + 1 > f ? f : l + 1], g - l)
    },
    Bezier: function(a, c) {
        var f = 0, g = a.length - 1, l = Math.pow, p = TWEEN.Interpolation.Utils.Bernstein, d;
        for (d = 0; d <= g; d++)
            f += l(1 - c, g - d) * l(c, d) * a[d] * p(g, d);
        return f
    },
    CatmullRom: function(a, c) {
        var f = a.length - 1
          , g = f * c
          , l = Math.floor(g)
          , p = TWEEN.Interpolation.Utils.CatmullRom;
        return a[0] === a[f] ? (0 > c && (l = Math.floor(g = f * (1 + c))),
        p(a[(l - 1 + f) % f], a[l], a[(l + 1) % f], a[(l + 2) % f], g - l)) : 0 > c ? a[0] - (p(a[0], a[0], a[1], a[1], -g) - a[0]) : 1 < c ? a[f] - (p(a[f], a[f], a[f - 1], a[f - 1], g - f) - a[f]) : p(a[l ? l - 1 : 0], a[l], a[f < l + 1 ? f : l + 1], a[f < l + 2 ? f : l + 2], g - l)
    },
    Utils: {
        Linear: function(a, c, f) {
            return (c - a) * f + a
        },
        Bernstein: function(a, c) {
            var f = TWEEN.Interpolation.Utils.Factorial;
            return f(a) / f(c) / f(a - c)
        },
        Factorial: function() {
            var a = [1];
            return function(c) {
                var f = 1, g;
                if (a[c])
                    return a[c];
                for (g = c; 1 < g; g--)
                    f *= g;
                return a[c] = f
            }
        }(),
        CatmullRom: function(a, c, f, g, l) {
            a = .5 * (f - a);
            g = .5 * (g - c);
            var p = l * l;
            return (2 * c - 2 * f + a + g) * l * p + (-3 * c + 3 * f - 2 * a - g) * p + a * l + c
        }
    }
};
var mouse;                  //鼠标位置
var requestAnimaFrame = function (callback) {
    return window.requestAnimationFrame(callback) ||
        window.webkitRequestAnimationFrame(callback) ||
        window.mozRequestAnimationFrame(callback) ||
        window.oRequestAnimationFrame(callback) ||
        window.msRequestAnimationFrame(callback) ||
        function (callback) {
            setInterval(callback, 1000 / 60);
        }
};

/**
 * 渲染类
 */
var ZTRender=function(container_){
    this.mapLenth = 20037508.3427892*2;     //地图长度
    this.scene=new THREE.Scene();
    //时间记录（轨迹控制）
    this.clock = new THREE.Clock();
    this.control=null;
    this.renderEnabled = true;       //是否渲染
    this.editSceneBackgroundType={img:0,color:1}         //更改场景背景类型(颜色/图片)
    this.infoWindos = {};                    //存放所有lable标签
    var scope = this;
    this.update = null;
    this.lodLayer = new THREE.LOD();            //细节图层
    this.scene.add(this.lodLayer);
    this.lastTime = 0;             //上一次渲染的时间
    this.frameRate = 60;              // 目标帧率
    this.frameTime = 1 / this.frameRate;     // 计算目标帧时间（秒）
    // this.ObjectAssign = function(prototype){
    //     Object.assign(prototype,{
    //         setProperties : function(properties_){
    //             if(!properties_)  return;
    //             this.userData = properties_;
    //         },
    //         getProperties : function(){
    //             return this.userData;
    //         }
    //     });
    // }
    let licenceTime = "2025-11-05";
    this.checkOverTime = function(){
        if(Date.now()>=new Date(licenceTime)){
            this.scene.visible = false;
            this.renderEnabled = false;
            this.container.insertAdjacentHTML("beforeend","<div style='position: absolute;top: 50%;left: calc(50% - 240px);font-size: 40px;color: #f00;'>许可已到期，请联系厂商！</div>")
        }
    }
    this.setTargetFPS = function(fps){
        this.frameRate = fps || 40;              // 目标帧率
        this.frameTime = 1 / this.frameRate;     // 计算目标帧时间（秒）
    }
    this.getView = function(){
        var target = this.control.target;
        var position = this.control.object.position;
        var str = "{target:{x:"+target.x+",y:"+target.y+",z:"+target.z+"},position:{x:"+position.x+",y:"+position.y+",z:"+position.z+"}}";
        console.log(str);
    }
    this.setView = function(view){
        this.control.target.set(view.target.x,view.target.y,view.target.z);
        this.control.object.position.set(view.position.x,view.position.y,view.position.z);
    }
    // this.THREE = function(){
    //     this.mapLayer = new THREE.mapLayer(this.render,this.camera,this.control);
    //     this.scene.add(this.mapLayer);
    // }
    /**
     * 清除渲染器
     * @param {*} renderer 
     */
    this.clearRenderer = function(renderer){
        renderer.dispose();
        renderer.forceContextLoss();
        renderer.context = null;
        renderer.domElement = null;
        renderer = null;
    }
    this.readFile=function(url,callback){           //读取文件
        var request = new XMLHttpRequest();
        request.open("get", url);/*设置请求方法与路径*/
        request.send(null);/*不发送数据到服务器*/
        request.onload = function () {/*XHR对象获取到返回信息后执行*/
            if (request.status == 200) {/*返回状态为200，即为数据获取成功*/
                if(callback) callback(request.responseText);
            }
        }
    }
    this.getInitIntersect = function(x,y,targetList){       
        if (!targetList) return;
        var Mouse = new THREE.Raycaster();
        Mouse.setFromCamera(new THREE.Vector2(( x / (this.events.container.innerWidth||this.events.container.clientWidth) ) * 2 - 1,- ( y / (this.events.container.innerHeight||this.events.container.clientHeight) ) * 2 + 1), this.camera);
        var intersects = Mouse.intersectObjects(targetList,true);        
        return intersects;
    }
    /**
    * 将THREE.js三维坐标转换成屏幕上的二维坐标
    * @param THREE.Vector3 vector THREE.js三维坐标
    * @return {x:int,y:int} 屏幕坐标
    */
    this.transToScreenCoord =function(vector) {
        vector = new THREE.Vector3(vector.x,vector.y,vector.z);
        var screenCoord = {};
        vector.project(this.camera);
        screenCoord.x = (0.5 + vector.x / 2) * this.container.clientWidth; 
        screenCoord.y = (0.5 - vector.y / 2) * this.container.clientHeight;
        return screenCoord;
    }
    this.transToTHREECoord=function(x,y,list){
        var point;
        var mouse ={};
        mouse.x = ( x / this.render.domElement.clientWidth ) * 2 - 1;
        mouse.y = - ( y / this.render.domElement.clientHeight ) * 2 + 1;
        var vector = new THREE.Vector3(mouse.x, mouse.y,1.0).unproject(this.camera);//得到那个点了
        raycaster = new THREE.Raycaster(this.camera.position, vector.sub(this.camera.position).normalize());//起始位置，方向 pos-this.camerapos
        raycaster.setFromCamera( mouse, this.camera );
        //射线原理拾取目标
        var intersects = raycaster.intersectObjects(list||this.scene.children,true );
        if(intersects.length>0){
            point=intersects[0].point;
        }
        if(!point)point=new THREE.Vector3(0,0,0);
        return point;
    }
    this.skyConfig = {
        turbidity: 10,
        rayleigh: 2,
        mieCoefficient: 0.005,
        mieDirectionalG: 0.8,
        inclination: 0, // elevation / inclination
        azimuth: 0.205, // Facing front,
        exposure: 0.28
    }
    this.initSky = function() {         //初始化天空
        this.sky = new Sky();
        this.sky.scale.setScalar( this.mapLenth*5 );
        this.scene.add( this.sky );
        this.sun = new THREE.Vector3();
        this.skyUpdate();
    }
    this.skyUpdate = function() {
        var uniforms = scope.sky.material.uniforms;
        uniforms[ "turbidity" ].value = scope.skyConfig.turbidity;
        uniforms[ "rayleigh" ].value = scope.skyConfig.rayleigh;
        uniforms[ "mieCoefficient" ].value = scope.skyConfig.mieCoefficient;
        uniforms[ "mieDirectionalG" ].value = scope.skyConfig.mieDirectionalG;

        var theta = Math.PI * ( scope.skyConfig.inclination - 0.5 );
        var phi = 2 * Math.PI * ( scope.skyConfig.azimuth - 0.5 );

        scope.sun.x = Math.cos( phi );
        scope.sun.y = Math.sin( phi ) * Math.sin( theta );
        scope.sun.z = Math.sin( phi ) * Math.cos( theta );

        uniforms[ "sunPosition" ].value.copy( scope.sun );

        // scope.render.toneMappingExposure = scope.skyConfig.exposure;
        scope.render.render( scope.scene, scope.camera );

    }
    this.sunStartTime = "9:00:00";
    this.sunEndTime = "18:00:00";
    this.updateSun = function(){
        if(!this.sky) return;
        const currTime = new Date().getTime();
        if(!this.sky.startTime){
            this.sky.startTime = new Date().getTime();
        }else{
            if( currTime - this.sky.startTime<60000){
                return;
                // this.scene.environment = pmremGenerator.fromScene( this.sky ).texture;
            }
        }
        
        this.sky.startTime = currTime;
        // this.skyConfig.azimuth = 0.5 - ((24-new Date().getHours())*60 - new Date().getMinutes())*(0.5/(24*60));
        const date = new Date();
        const start = new Date(date.getFullYear() +"-"+date.getMonth()+"-"+date.getDate()+" "+this.sunStartTime+"").getTime();
        const end = new Date(date.getFullYear() +"-"+date.getMonth()+"-"+date.getDate()+" "+this.sunEndTime+"").getTime();
        const endHour = (end - new Date(date.getFullYear() +"-"+date.getMonth()+"-"+date.getDate()+" 00:00:00"))/3600000;
        const length = Math.abs(end - start);
        const hour = length/3600000;
        this.skyConfig.azimuth = 0.5 - ((endHour-new Date().getHours())*60 - new Date().getMinutes())*(0.5/(hour*60));
        this.skyConfig.theta = Math.PI * ( this.skyConfig.inclination - 0.5 );
        this.skyConfig.phi = 2 * Math.PI * ( this.skyConfig.azimuth - 0.5 );

        this.sun.x = Math.cos( this.skyConfig.phi );
        this.sun.y = Math.sin( this.skyConfig.phi ) * Math.sin( this.skyConfig.theta );
        this.sun.z = Math.sin( this.skyConfig.phi ) * Math.cos( this.skyConfig.theta );

        this.sky.material.uniforms[ 'sunPosition' ].value.copy( this.sun );
        if(this.water)this.water.material.uniforms[ 'sunDirection' ].value.copy( this.sun ).normalize();
    }
    //调整视野
    this.viewAnimate = function (param,num) {
        var time = 1000;
        if (param.time) time = param.time;
        if (param.target) {
            var tween1 = new TWEEN.Tween(scope.control.target)
                .to(param.target, time).easing(param.easing || TWEEN.Easing.Quadratic.InOut)        //TWEEN.Easing.Quadratic.InOut
                .onComplete(
                    function () {
                        if (param.callback) param.callback(param.num);
                    }
                )
                .start();
        }
        if (param.position) {
            var tween = new TWEEN.Tween(scope.control.object.position)
                .to(param.position, time).easing(param.easing || TWEEN.Easing.Quadratic.InOut) //TWEEN.Easing.Linear.None
                .onComplete(
                    function () {
                        if (!param.target && param.callback) param.callback(param.num);
                    }
                )
                .start();
        }
        
    }
    /**设置弹框
     * html: dom元素
     */
    this.setInfoWindow = function(html,position,param){
        if(!param) param = {};
        this.removeInfoWindow(this.getInfoWindowByName(param.name));
        if(!position) position = {};
        var x = position.x || 0;
        var y = position.y || 0;
        var z = position.z || 0;
        var dom = document.createElement( 'div' );
        dom.innerHTML = html;
        var lable = new CSS2DObject( dom );
        lable.name = param.name;
        lable.param = param;
        lable.position.set( x,y,z );
        if(param.object)param.object.add( lable );
        else this.scene.add( lable );
        this.infoWindos[lable.name] = lable;
        lable.layers.set( 0 );
        return lable;
    },
    this.getInfoWindowByName = function(name){
        return this.infoWindos[name];
    }
    this.removeInfoWindow = function(infowindow){
        if(infowindow&&infowindow instanceof CSS2DObject){
            if(infowindow.parent){
                infowindow.parent.remove(infowindow);
            }else{
                scope.scene.remove(infowindow);
            }
        }
    },
    this.clearInfoWindows = function(){
        for(var key in this.infoWindos){
            this.removeInfoWindow(this.infoWindos[key]);
            delete this.infoWindos[key];
        }
    }
    /**
     * 初始化基本数据
     */
    return this.initRender(container_);
}  
/**
 * //初始化三维场景渲染      
 */
ZTRender.prototype.initRender=function(container_){     
    var scope=this;
    //初始化三维场景渲染
    this.container = null;
    if(container_)this.container=document.getElementById(container_);
    else{
        this.container = document.createElement( 'div' );
        document.body.appendChild( this.container );
        this.container.style.position="absolute";
        this.container.style.top="0";
        this.container.style.left="0";
        this.container.style.width="100%";
        this.container.style.height="100%";
    }
    scope.camera= new THREE.PerspectiveCamera( 45, this.container.clientWidth/this.container.clientHeight,0.1, scope.mapLenth );
    scope.camera.position.set(0,scope.mapLenth,0);
    // scope.scene.fog=new THREE.Fog(0xc0c5cf,5000,10000);
    scope.render=new THREE.WebGLRenderer({antialias:true,alpha: true});

    // scope.render.shadowMapEnabled = true;
    // scope.render.shadowMapSoft = true;//关键
    scope.render.shadowMap.enabled = true;
    scope.render.shadowMap.type = THREE.PCFSoftShadowMap;//关键
    // scope.render.outputEncoding = THREE.sRGBEncoding;
    // scope.render.textureEncoding = THREE.sRGBEncoding;
    // scope.render.gammaOutput = true;
    // scope.render.gammaFactor = 2.2;

    scope.render.setPixelRatio( window.devicePixelRatio );
    scope.render.setSize(this.container.clientWidth,this.container.clientHeight);
    this.container.appendChild(scope.render.domElement);

    // scope.control = new THREE.OrbitControls(scope.camera,scope.render.domElement);
    scope.control = new THREE.OrbitControls(scope.camera,scope.container);
    scope.control.minPolarAngle = 0; // radians
    scope.control.maxPolarAngle = THREE.MathUtils.degToRad(85);
    // scope.control.maxDistance = this.mapLenth;
    scope.control.screenSpacePanning = false;
    scope.control.mouseButtons.LEFT = THREE.MOUSE.PAN;
	scope.control.mouseButtons.RIGHT = THREE.MOUSE.ROTATE;

	scope.control.touches.ONE = THREE.TOUCH.PAN;
	scope.control.touches.TWO = THREE.TOUCH.DOLLY_ROTATE;
    scope.control.zoomSpeed = 1;
    // scope.control.listenToKeyEvents(scope.render.domElement);

    scope.labelRenderer = new CSS2DRenderer();
    scope.labelRenderer.setSize( this.container.clientWidth,this.container.clientHeight );
    scope.labelRenderer.domElement.style.position = 'absolute';
    scope.labelRenderer.domElement.style.top = '0px';
    scope.labelRenderer.domElement.style.pointerEvents = "none";
    this.container.appendChild( scope.labelRenderer.domElement );

    if(THREE.TransformControls){
        // scope.transformControl = new THREE.TransformControls( scope.camera, scope.render.domElement );
        scope.transformControl = new THREE.TransformControls( scope.camera, scope.container );
        scope.transformControl.enabled = false;
        // scope.transformControl.addEventListener( 'change', render );
        scope.transformControl.addEventListener( 'dragging-changed', function ( event ) {
            if(scope.transformControl.dragChangeCallback) scope.transformControl.dragChangeCallback();
            scope.control.enabled = ! event.value;
    
        } );
        scope.scene.add(scope.transformControl);
    }
    
    // 设置光源
    scope.ambientLight = new THREE.AmbientLight(0xffffff,0.5);
    scope.scene.add(scope.ambientLight);
    // window 失去焦点，停止输出
    // scope.onblurTimeOut = null;
    // window.onblur = function() {
    //     if(scope.onblurTimeOut) clearTimeout(scope.onblurTimeOut);
    //     scope.onblurTimeOut = setTimeout(() => {
    //         scope.renderEnabled = false;
    //     }, 10000);
    // };
    // window 每次获得焦点
    // window.onfocus = function() {
    //     if(scope.onblurTimeOut) clearTimeout(scope.onblurTimeOut);
    //     scope.renderEnabled = true;
    // }
    //窗口改变事件
    window.addEventListener( 'resize', function(){
        scope.camera.aspect = scope.container.clientWidth / scope.container.clientHeight;
        scope.camera.updateProjectionMatrix();
        scope.render.setSize( scope.container.clientWidth, scope.container.clientHeight );
        if(scope.labelRenderer)scope.labelRenderer.setSize( scope.container.clientWidth, scope.container.clientHeight );
    }, false );
    scope.renderer();
    // this.ObjectAssign(THREE.Object3D.prototype);
}
/**
 *  实时渲染
 */
ZTRender.prototype.renderer=function() {      
    const delta = this.clock.getDelta(); //获取距离上次请求渲染的时间
	this.lastTime += delta;
    this.checkOverTime();
	if (this.lastTime > this.frameTime && this.renderEnabled) {
		this.control.update(delta);
        if(this.update) this.update();
        if(this.sky&&this.startUpdateSun){this.updateSun();}
        if(this.mapLayer)this.mapLayer.update();
        this.lodLayer.update(this.camera);
        if(TWEEN) TWEEN.update();
        this.render.render(this.scene, this.camera);
        if(this.labelRenderer){
            this.labelRenderer.render(this.scene, this.camera);
        }
		this.lastTime = (this.lastTime % this.frameTime);
	}
    //帧循环
    requestAnimaFrame(this.renderer.bind(this));
}
