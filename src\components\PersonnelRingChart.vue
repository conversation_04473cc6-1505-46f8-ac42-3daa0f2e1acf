<template>
    <div class="chart-container">
        <div ref="chartRef" class="chart"></div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import * as echarts from 'echarts'
import centerImage from '@/assets/images/yibuyiping/renyuanguanli-center.png'

const { t } = useI18n()

const props = defineProps({
    width: {
        type: String,
        default: '100%'
    },
    height: {
        type: String,
        default: '200px'
    },
    data: {
        type: Array,
        default: () => [
            { name: '施工人员', value: 17, itemStyle: { color: '#0085FF' } },
            { name: '安管人员', value: 8, itemStyle: { color: '#87DBFF' } },
            { name: '管理人员', value: 3, itemStyle: { color: '#EEC01B' } }
        ]
    }
})

const chartRef = ref(null)
let chart = null

const initChart = () => {
    if (!chartRef.value) return

    chart = echarts.init(chartRef.value)
    updateChart()
}

const updateChart = () => {
    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'item',
            formatter: `{a} <br/>{b}: {c}${t('oneOfficeOneScreen.detail.units.people')} ({d}%)`,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: 'rgba(255, 255, 255, 0.2)',
            textStyle: {
                color: '#fff'
            }
        },
        legend: {
            orient: 'vertical',
            right: '19%',
            top: 'middle',
            itemWidth: 14,
            itemHeight: 14,
            itemGap: 15,
            textStyle: {
                color: '#fff',
                fontSize: 12
            },
            formatter: function (name) {
                // 查找对应数据项
                const data = props.data.find(item => item.name === name);
                if (data) {
                    // 格式化为"名称 数值个"
                    return `${name}  ${data.value}${t('oneOfficeOneScreen.detail.units.people')}`;
                }
                return name;
            }
        },
        graphic: [{
            type: 'image',
            id: 'centerImage',
            z: 10,
            style: {
                image: centerImage,
                width: 120,
                height: 120
            },
            left: '16%',
            top: '7%',
            position: null,
            origin: [0.5, 0.5]
        }],
        series: [
            {
                name: t('oneOfficeOneScreen.detail.leftPanel.personnelManagement'),
                type: 'pie',
                radius: ['45%', '70%'],
                center: ['30%', '50%'],  // 将圆环位置偏左
                startAngle: 90,
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 2,
                    borderColor: 'rgba(0, 0, 0, 0.2)',
                    borderWidth: 1
                },
                label: {
                    show: false  // 隐藏标签，由图例替代
                },
                labelLine: {
                    show: false
                },
                emphasis: {
                    scale: true,
                    scaleSize: 5,
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                data: props.data
            }
        ]
    }

    chart.setOption(option)
}

const handleResize = () => {
    if (chart) {
        chart.resize()
    }
}

watch(() => props.data, () => {
    updateChart()
}, { deep: true })

onMounted(() => {
    initChart()
    window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
    if (chart) {
        chart.dispose()
    }
    window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.chart-container {
    width: 100%;
    height: 100%;
    position: relative;

    .chart {
        width: 100%;
        height: 100%;
    }
}
</style>