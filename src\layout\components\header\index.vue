<template>
  <div class="header-container relative z-[3]">
    <!-- <img
      class="absolute top-[-115px] left-[50%] translate-x-[-50%] w-[1920px] h-[395px]"
      :src="activeTabImg"
      alt=""
      srcset=""
    />
    <img
      class="absolute top-[-39px] w-full"
      src="@/assets/images/layout/title.png"
      alt=""
      srcset=""
    />
    <div
      class="absolute w-full text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title"
    >
      {{ $t('common.header.title') }}
    </div> -->
    <div class="absolute top-[10px] w-full flex justify-center" style="right: 28%;">
      <div
        class="tab-item w-[8%] text-center font-family-youshebiaotihei pointer-events-auto cursor-pointer text-shadow text-tab"
        :class="{ 'tab-item-active': currentActiveTab === $t('nav.businessLayout') }" :style="{
          backgroundImage: `url(${currentActiveTab === $t('nav.businessLayout') ? leftActive : leftDefalut})`,
          fontSize: currentLanguage === 'en' ? '17px' : '22px',
          lineHeight: currentLanguage === 'en' ? '14px' : 'normal'
        }" @click="activeTab($t('nav.businessLayout'))">
        {{ $t('nav.businessLayout') }}
      </div>
      <div
        class="tab-item w-[8%] text-center font-family-youshebiaotihei pointer-events-auto cursor-pointer text-shadow text-tab"
        :class="{ 'tab-item-active': currentActiveTab === $t('nav.groupManagement') }" :style="{
          backgroundImage: `url(${currentActiveTab === $t('nav.groupManagement') ? leftActive : leftDefalut})`,
          fontSize: currentLanguage === 'en' ? '17px' : '22px',
          lineHeight: currentLanguage === 'en' ? '14px' : 'normal'
        }" @click="activeTab($t('nav.groupManagement'))">
        {{ $t('nav.groupManagement') }}
      </div>
      <div
        class="tab-item w-[8%] text-center font-family-youshebiaotihei pointer-events-auto cursor-pointer text-shadow text-tab"
        :class="{ 'tab-item-active': currentActiveTab === $t('nav.groupAssets') }" :style="{
          backgroundImage: `url(${currentActiveTab === $t('nav.groupAssets') ? leftActive : leftDefalut})`,
          fontSize: currentLanguage === 'en' ? '17px' : '22px',
          lineHeight: currentLanguage === 'en' ? '14px' : 'normal'
        }" @click="activeTab($t('nav.groupAssets'))">
        {{ $t('nav.groupAssets') }}
      </div>
    </div>
    
    <!-- 中间标题 -->
    <div class="absolute top-[0px] w-full flex justify-center items-center">
      <div class="relative">
        <img src="@/assets/images/header/bg.png" alt="背景" class="h-[90px]" />
        <div class="absolute -top-[16px] left-0 w-full h-full flex items-center justify-center">
          <span class="text-[24px] text-white font-family-youshebiaotihei biaoti">{{ $t('common.header.title') }}</span>
        </div>
      </div>
    </div>

    <div class="absolute top-[10px] w-full flex justify-center" style="left: 28%;">
      <div
        class="tab-item w-[8%] text-center font-family-youshebiaotihei pointer-events-auto cursor-pointer text-shadow text-tab"
        :class="{ 'tab-item-active': currentActiveTab === $t('nav.oneOfficeOneScreen') }" :style="{
          backgroundImage: `url(${currentActiveTab === $t('nav.oneOfficeOneScreen') ? rightActive : rightDefalut})`,
          fontSize: currentLanguage === 'en' ? '17px' : '22px',
          lineHeight: currentLanguage === 'en' ? '14px' : 'normal'
        }" @click="activeTab($t('nav.oneOfficeOneScreen'))">
        {{ $t('nav.oneOfficeOneScreen') }}
      </div>
      <div
        class="tab-item w-[8%] text-center font-family-youshebiaotihei pointer-events-auto cursor-pointer text-shadow text-tab"
        :class="{ 'tab-item-active': currentActiveTab === $t('nav.onSiteVideo') }" :style="{
          backgroundImage: `url(${currentActiveTab === $t('nav.onSiteVideo') ? rightActive : rightDefalut})`,
          fontSize: currentLanguage === 'en' ? '17px' : '22px',
          lineHeight: currentLanguage === 'en' ? '14px' : 'normal'
        }" @click="activeTab($t('nav.onSiteVideo'))">
        {{ $t('nav.onSiteVideo') }}
      </div>
      <div
        class="tab-item w-[8%] text-center font-family-youshebiaotihei pointer-events-auto cursor-pointer text-shadow text-tab"
        :class="{ 'tab-item-active': currentActiveTab === $t('nav.oneProfitFiveRates') }" :style="{
          backgroundImage: `url(${currentActiveTab === $t('nav.oneProfitFiveRates') ? rightActive : rightDefalut})`,
          fontSize: currentLanguage === 'en' ? '17px' : '22px',
          lineHeight: currentLanguage === 'en' ? '14px' : 'normal'
        }" @click="activeTab($t('nav.oneProfitFiveRates'))">
        {{ $t('nav.oneProfitFiveRates') }}
      </div>
    </div>
    <div class="admin-info">
      <!-- 语言切换下拉菜单 -->
      <div class="admin-profile" @click="toggleLanguageDropdown">
        <img :src="currentLanguage === 'zh' ? chineseIcon : englishIcon" alt="语言切换" class="admin-icon" />
        <span class="admin-text">{{ $t('language.' + (currentLanguage === 'zh' ? 'chinese' : 'english')) }}</span>
        <img src="@/assets/images/svg/bottomSelected.svg" alt="下拉菜单" class="dropdown-icon"
          :class="{ 'rotate-180': showLanguageDropdown }" />
      </div>
      <transition name="fade">
        <div class="dropdown-menu" v-show="showLanguageDropdown">
          <div class="language-item" @click="changeLanguage('zh')" :class="{ active: currentLanguage === 'zh' }">
            <img :src="chineseIcon" alt="中文" class="language-icon" />
            <span>{{ $t('language.chinese') }}</span>
          </div>
          <div class="language-item" @click="changeLanguage('en')" :class="{ active: currentLanguage === 'en' }">
            <img :src="englishIcon" alt="English" class="language-icon" />
            <span>{{ $t('language.english') }}</span>
          </div>
        </div>
      </transition>

      <div class="admin-profile" @click="toggleMap" v-if="router.currentRoute.value.path === '/project/index' && !isInChinaMode">
        <span class="text-white text-[18px]">{{ mapMode === 'earth' ? '展开' : '收起' }}</span>
      </div>
      <div class="admin-profile" @click="handleLogout">
        <span class="text-white text-[18px]">退出</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from 'vue-i18n';
import { ElMessageBox } from 'element-plus';
import leftActive from '@/assets/images/header/leftActive.png'
import leftDefalut from '@/assets/images/header/leftDefalut.png'
import rightActive from '@/assets/images/header/rightActive.png'
import rightDefalut from '@/assets/images/header/rightDefalut.png'
import chineseIcon from '@/assets/images/header/chinese.png'
import englishIcon from '@/assets/images/header/english.png'
import { clearTokenRefresh } from '@/utils/request'

const router = useRouter();
const { t, locale } = useI18n();

import zonglan from "@/assets/images/layout/buju.png";
import jingying from "@/assets/images/layout/jingying.png";
import zichan from "@/assets/images/layout/zichan.png";
// import xiangmu from "@/assets/images/layout/xiangmu.png";

const showLanguageDropdown = ref(false);
const mapMode = ref('earth'); // 添加一个状态变量，默认为'plan'模式
const iframe = ref(document.getElementById("iframe")); // 添加iframe引用
const isInChinaMode = ref(false); // 添加中国模式状态

// 语言相关
const currentLanguage = ref(localStorage.getItem('language') || 'zh');


// 记录业务布局最后访问的路由
const lastProjectRoute = ref(sessionStorage.getItem("lastProjectRoute") || "/project/index");

// 定义当前激活的tab
const currentActiveTab = ref(t('nav.businessLayout')); // 移除localStorage的初始化，使用默认值

// 根据路由路径确定对应的tab
const getTabFromRoute = (path) => {
  if (path.startsWith('/project/')) {
    return t('nav.businessLayout');
  } else if (path.startsWith('/business/')) {
    return t('nav.groupManagement');
  } else if (path.startsWith('/assets/')) {
    return t('nav.groupAssets');
  } else if (path.startsWith('/yibuyiping/')) {
    return t('nav.oneOfficeOneScreen');
  } else if (path.startsWith('/xianchangshipin/')) {
    return t('nav.onSiteVideo');
  } else if (path.startsWith('/liwulv/')) {
    return t('nav.oneProfitFiveRates');
  } else {
    // 默认返回业务布局
    return t('nav.businessLayout');
  }
};

// 监听路由变化
watch(() => router.currentRoute.value.path, (newPath) => {
  // 如果当前路径属于业务布局相关页面，则记录该路径
  if (newPath.startsWith('/project/')) {
    sessionStorage.setItem("lastProjectRoute", newPath);
    lastProjectRoute.value = newPath;
  }

  // 根据新路由更新选中的tab
  const newTab = getTabFromRoute(newPath);
  if (newTab !== currentActiveTab.value) {
    currentActiveTab.value = newTab;
    localStorage.setItem("currentActiveTab", newTab);
  }
}, { immediate: true });

const toggleLanguageDropdown = () => {
  showLanguageDropdown.value = !showLanguageDropdown.value;
};

// 切换语言
const changeLanguage = (lang) => {
  currentLanguage.value = lang;
  locale.value = lang;
  localStorage.setItem('language', lang);
  showLanguageDropdown.value = false;

  // 向iframe发送语言切换消息
  if (!iframe.value) {
    iframe.value = document.getElementById("iframe");
  }
  if (iframe.value) {
    iframe.value.contentWindow.postMessage(
      { eve: "changeLanguage", data: lang === 'zh' ? 'cn' : 'en' },
      "*"
    );
  }
};

const toggleMap = () => {
  // 确保iframe存在
  if (!iframe.value) {
    iframe.value = document.getElementById("iframe");
    if (!iframe.value) return;
  }

  // 根据当前状态切换地图模式
  if (mapMode.value === 'plan') {
    mapMode.value = 'earth';
    iframe.value.contentWindow.postMessage(
      { eve: "changeModel", data: "earth" },
      "*"
    );
  } else {
    mapMode.value = 'plan';
    iframe.value.contentWindow.postMessage(
      { eve: "changeModel", data: "plan" },
      "*"
    );
  }
};

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
  const adminInfo = document.querySelector('.admin-info');
  if (adminInfo && !adminInfo.contains(event.target)) {
    if (showLanguageDropdown.value) {
      showLanguageDropdown.value = false;
    }
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  // 根据当前路由设置对应的tab，而不是从localStorage读取
  const currentPath = router.currentRoute.value.path;
  const tabFromRoute = getTabFromRoute(currentPath);
  currentActiveTab.value = tabFromRoute;
  localStorage.setItem("currentActiveTab", tabFromRoute);
  
  // 初始化中国模式状态
  isInChinaMode.value = localStorage.getItem("isChina") === "1";
  
  // 监听localStorage变化
  const handleStorageChange = () => {
    isInChinaMode.value = localStorage.getItem("isChina") === "1";
  };
  
  // 监听storage事件
  window.addEventListener('storage', handleStorageChange);
  
  // 使用MutationObserver监听localStorage的直接修改
  const storageObserver = new MutationObserver(() => {
    const newValue = localStorage.getItem("isChina") === "1";
    if (newValue !== isInChinaMode.value) {
      isInChinaMode.value = newValue;
    }
  });
  
  // 定期检查localStorage变化（备用方案）
  const checkInterval = setInterval(() => {
    const newValue = localStorage.getItem("isChina") === "1";
    if (newValue !== isInChinaMode.value) {
      isInChinaMode.value = newValue;
    }
  }, 500);
  
  // 清理函数
  onBeforeUnmount(() => {
    window.removeEventListener('storage', handleStorageChange);
    clearInterval(checkInterval);
  });
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});

const activeTabImg = computed(() => {
  if (currentActiveTab.value == t('nav.businessLayout')) {
    return zonglan;
  } else if (currentActiveTab.value == t('nav.groupManagement')) {
    return jingying;
  } else if (currentActiveTab.value == t('nav.groupAssets')) {
    return zichan;
  } else return zonglan;
  //  else if (currentActiveTab.value == "项目") {
  //   return xiangmu;
  // }
});
const activeTab = (tab) => {
  // 如果点击的是当前已激活的选项卡，不进行路由跳转（业务布局除外）
  if (tab === currentActiveTab.value && tab !== t('nav.businessLayout')) {
    return;
  }

  currentActiveTab.value = tab;
  localStorage.setItem("currentActiveTab", tab);

  if (tab === t('nav.businessLayout')) {
    console.log(tab);
    
    // 清除所有相关状态数据
    localStorage.removeItem("isChina");
    localStorage.removeItem("clickProject");
    sessionStorage.removeItem("countryLevel");
    sessionStorage.removeItem("countryTitle");
    sessionStorage.removeItem("clickCountry");
    sessionStorage.removeItem("lastProjectState");
    sessionStorage.removeItem("clickProject");
    
    // 导航到首页
    router.push("/project/index");
    
    // 使用延迟确保在页面加载后发送消息切换到地球模式
    setTimeout(() => {
      if (!iframe.value) {
        iframe.value = document.getElementById("iframe");
      }
      if (iframe.value && iframe.value.contentWindow) {
        // 发送消息切换到地球模式
        iframe.value.contentWindow.postMessage(
          { eve: "changeModel", data: "earth",model: "earth" },
          "*"
        );
      }
    }, 500);
  } else if (tab === t('nav.groupManagement')) {
    router.push("/business/index");
  } else if (tab === t('nav.groupAssets')) {
    router.push("/assets/index");
  } else if (tab === t('nav.oneOfficeOneScreen')) {
    router.push("/yibuyiping/index");
  } else if (tab === t('nav.onSiteVideo')) {
    router.push("/xianchangshipin/index");
  } else if (tab === t('nav.oneProfitFiveRates')) {
    router.push("/liwulv/index");
  }
};
// 退出登录方法
const handleLogout = () => {
  ElMessageBox.confirm(t('user.logoutConfirm'), t('user.warning'), {
    confirmButtonText: t('user.confirm'),
    cancelButtonText: t('user.cancel'),
    type: 'warning'
  }).then(() => {
    // 清除所有相关的存储
    clearTokenRefresh()
    localStorage.clear()  // 清除所有 localStorage 数据
    sessionStorage.clear()  // 清除所有 sessionStorage 数据

    // 使用 router 进行导航
    router.push('/login')
  }).catch(() => {
    // 用户取消操作，不做任何处理
  })
};


</script>

<style scoped lang="scss">
.header-container {
  width: 100%;
  height: 122px;
  // background-image: url('@/assets/images/header/headerBg.png');
  // background-size: 100% 100%;
  // background-repeat: no-repeat;
  // background-position: center;
  z-index: 9999;

  .biaoti {
    font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
    font-weight: 400;
    font-size: 34px;
    color: #EFF8FC;
    letter-spacing: 3px;
    text-shadow: inset 0px 0px 1px rgba(255, 255, 255, 0.8), 0px 0px 7px rgba(130, 165, 255, 0.54), 0px 2px 0px rgba(19, 80, 143, 0.66);
    text-align: center;
    font-style: normal;
    text-transform: none;
    // background: linear-gradient(90deg, #FFFFFF 0%, #FFFFFF 40%, #77BAFF 100%);
  }

  .title {
    text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.8);
  }

  .text-tab {
    color: rgba($color: #fff, $alpha: 0.7);
  }

  .text-tab-active {
    color: #fff;
    text-shadow: 3px 3px 5px rgba(0, 0, 0, 0.5);
  }

  .tab-item {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    color: #fff;
    transition: background-image 0.2s;
  }

  .tab-item-active {
    background-image: url('@/assets/images/header/selected.png');
    color: #fff;
  }

  .admin-info {
    position: absolute;
    top: 19px;
    right: -10px;
    z-index: 100;
    pointer-events: all;
    display: flex;
    align-items: center;

    .admin-profile {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 4px;
      pointer-events: all;
      span{
      font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
      font-weight: normal;
      font-size: 18px;
      text-shadow: 0px 0px 4px #0085FF;
      text-align: right;
      font-style: normal;
      text-transform: none; }
      .admin-icon {
        width: 30px;
        height: 30px;
        margin-right: 8px;
      }

      .admin-text {
        color: #fff;
        font-size: 14px;
        margin-right: 8px;
      }

      .dropdown-icon {
        width: 16px;
        height: 16px;
        transition: transform 0.3s;
      }
    }

    .dropdown-menu {
      position: absolute;
      top: 45px;
      right: 0;
      background: rgba(16, 36, 87, 0.7);
      border-radius: 4px;
      min-width: 120px;
      z-index: 999;

      .language-item {
        color: #fff;
        padding: 10px 15px;
        cursor: pointer;
        display: flex;
        align-items: center;
        white-space: nowrap;

        .language-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        &.active {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }

  .rotate-180 {
    transform: rotate(180deg);
  }

  // 添加过渡动画
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s, transform 0.3s;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }
}
</style>
