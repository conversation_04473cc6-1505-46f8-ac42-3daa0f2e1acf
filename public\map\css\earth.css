body {
  position: relative;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
}

#map {
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0);
  pointer-events: all;
  overflow: hidden;
  background: url("../textures/bg.png") no-repeat center center / 100% 100%;
}

.label-bg {
  /* background: url("../textures/biaoqian.png") no-repeat center center / 100% 100%; */
  /* min-width: 127px; */
  height: 62px;
  font-size: 14px;
  color: #fff;
  padding-left: 40px;
  pointer-events: auto;
  padding-right: 60px;
  line-height: 46px;
  cursor: pointer;
  margin-top: 50px;
}

.label-num {
  font-size: 16px;
  color: #fff;
  padding-left: 4px;
  font-weight: bold;
  padding-top: 2px;
}

#iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  border: none;
}

.hide {
  opacity: 0!important;
  pointer-events: none!important;
  transition: opacity 1s ease-in-out;
}

.show {
  opacity: 1!important;
  pointer-events: all!important;
  transition: opacity 1s ease-in-out;
}

.tools-container {
  position: absolute;
  top: 80px;
  right: 20px;
  color: #fff;
}
.tools-item {
  width: 169px;
  height: 107px;
  cursor: pointer;
  line-height: 107px;
  font-family: Microsoft YaHei;
  font-weight: 300;
  padding-left: 60px;
  box-sizing: border-box;
}
.zhedie {
  background: url("../img/zhedie.png") no-repeat center center / 100% 100%;
}
.zhankai {
  background: url("../img/zhankai.png") no-repeat center center / 100% 100%;
}
.none {
  display: none;
}