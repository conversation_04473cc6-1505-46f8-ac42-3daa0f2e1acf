<template>
  <!-- 全局背景层 -->
  <div class="global-bg">
    <!-- 中间主展示区 -->
    <div class="central-display">
      <!-- 中间核心背景 -->
      <img src="@/assets/images/zichan/assetsCenter.png" class="core-background">

      <!-- 四角底座 -->
      <div class="corner-base top-left">
        <div class="base-number">{{ getValueByCategory('固定资产净额', '固定资产净额') }}</div>
        <div class="base-title">{{ $t('assets.netFixedAssets') }}</div>
      </div>

      <div class="corner-base top-right">
        <div class="base-number">{{ getValueByCategory('本年折旧', '本年折旧总额') }}</div>
        <div class="base-title">{{ $t('assets.currentYearDepreciation') }}</div>
      </div>

      <div class="corner-base bottom-left">
        <div class="base-number">{{ getValueByCategory('投资性房地产', '年末金额') }}</div>
        <div class="base-title">{{ $t('assets.investmentRealEstate') }}</div>
      </div>

      <div class="corner-base bottom-right">
        <div class="base-number">{{ $t('assets.rankingTop5') }}</div>
        <div class="base-title">{{ $t('assets.assetRanking') }}</div>
      </div>
    </div>

    <!-- 四个象限图表 -->
    <!-- 左上图表 -->
    <div class="quad-chart lt">
      <div class="chart-header"> <span>{{ $t('assets.netFixedAssets') }}</span> </div>
      <div class="chart-body">
        <!-- 具体图表内容 -->
        <div class="chart-title">
          {{ $t('assets.netFixedAssets') }}:<span class="chart-title-num">{{ getValueByCategory('固定资产净额', '固定资产净额')
          }}</span><span class="unit" style="margin-top: 2px;">{{ $t('assets.unit') }}</span>
        </div>
        <div class="chart-content">
          <div class="circle-container">

            <!-- 五个图标项目 -->
            <div class="icon-item top">
              <div class="icon"><img src="@/assets/images/zichan/left1-img1.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.land') }}：</div>
                <div class="number">{{ getValueByCategory('固定资产净额', '土地') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>

            <div class="icon-item right">
              <div class="icon"><img src="@/assets/images/zichan/left1-img2.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.houseAndBuilding') }}：</div>
                <div class="number">{{ getValueByCategory('固定资产净额', '房屋及建筑物') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>

            <div class="icon-item bottom">
              <div class="icon"><img src="@/assets/images/zichan/left1-img3.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.transportationEquipment') }}：</div>
                <div class="number">{{ getValueByCategory('固定资产净额', '运输设备') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>

            <div class="icon-item left">
              <div class="icon"><img src="@/assets/images/zichan/left1-img4.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.machinery') }}：</div>
                <div class="number">{{ getValueByCategory('固定资产净额', '机器设备') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>

            <div class="icon-item center-top">
              <div class="icon"><img src="@/assets/images/zichan/left1-img5.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.officeAndOtherEquipment') }}：</div>
                <div class="number">{{ getValueByCategory('固定资产净额', '办公及其他设备') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右上图表 -->
    <div class="quad-chart rt">
      <div class="chart-header"> <span>{{ $t('assets.currentYearDepreciation') }}</span> </div>
      <div class="chart-body">
        <div class="chart-title-r">
          {{ $t('assets.totalCurrentYearDepreciation') }}: <span class="chart-title-num">{{ getValueByCategory('本年折旧',
            '本年折旧总额') }}</span>
          <span class="unit" style="margin: 2px 6px 0 0;">{{ $t('assets.unit') }}</span>
          {{ $t('assets.depreciationRate') }}: <span class="chart-title-num">{{ getValueByCategory('本年折旧', '折旧率')
          }}%</span>
        </div>
        <div class="chart-content">
          <div class="circle-container">

            <!-- 五个图标项目 -->
            <div class="icon-item top">
              <div class="icon"><img src="@/assets/images/zichan/left1-img1.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.land') }}：</div>
                <div class="number">{{ getValueByCategory('本年折旧', '土地') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>

            <div class="icon-item right">
              <div class="icon"><img src="@/assets/images/zichan/left1-img2.png"></div>

              <div class="info">
                <div class="title">{{ $t('assets.houseAndBuilding') }}：</div>
                <div class="number">{{ getValueByCategory('本年折旧', '房屋及建筑物') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>

            <div class="icon-item bottom">
              <div class="icon"><img src="@/assets/images/zichan/left1-img3.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.transportationEquipment') }}：</div>
                <div class="number">{{ getValueByCategory('本年折旧', '运输设备') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>

            <div class="icon-item left">
              <div class="icon"><img src="@/assets/images/zichan/left1-img4.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.machinery') }}：</div>
                <div class="number">{{ getValueByCategory('本年折旧', '机器设备') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>

            <div class="icon-item center-top">
              <div class="icon"><img src="@/assets/images/zichan/left1-img5.png"></div>
              <div class="info">
                <div class="title">{{ $t('assets.officeAndOtherEquipment') }}：</div>
                <div class="number">{{ getValueByCategory('本年折旧', '办公及其他设备') }}</div>
                <div class="unit">{{ $t('assets.unit') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 左下图表 -->
    <div class="quad-chart lb">
      <div class="chart-header"> <span>{{ $t('assets.investmentRealEstate') }}</span> </div>
      <div class="chart-body">
        <!-- 左侧大背景图 -->
        <div class="lb-container">
          <div class="lb-background">
            <img src="@/assets/images/zichan/box-left.png" alt="背景图">
          </div>
          <div class="lb-indicators">
            <div class="indicator-item">
              <div class="indicator-icon">
                <img src="@/assets/images/zichan/box-right1.png" alt="图标1">
              </div>
              <div class="indicator-data">
                <div class="indicator-title">{{ $t('assets.yearEndAmount') }}</div>
                <div class="indicator-value">{{ getValueByCategory('投资性房地产', '年末金额') }}<span class="unit">{{
                  $t('assets.unit') }}</span>
                </div>
              </div>
            </div>

            <div class="indicator-item">
              <div class="indicator-icon">
                <img src="@/assets/images/zichan/box-right2.png" alt="图标2">
              </div>
              <div class="indicator-data">
                <div class="indicator-title">{{ $t('assets.fairValueChange') }}：</div>
                <div class="indicator-value">{{ getValueByCategory('投资性房地产', '公允价值变动') }}<span class="unit">{{
                  $t('assets.unit') }}</span>
                </div>
              </div>
            </div>

            <div class="indicator-item">
              <div class="indicator-icon">
                <img src="@/assets/images/zichan/box-right3.png" alt="图标3">
              </div>
              <div class="indicator-data">
                <div class="indicator-title">{{ $t('assets.cost') }}：</div>
                <div class="indicator-value">{{ getValueByCategory('投资性房地产', '成本') }}<span class="unit">{{
                  $t('assets.unit') }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右下图表 -->
    <div class="quad-chart rb">
      <div class="chart-header"> <span>{{ $t('assets.rankingTop5') }}</span> </div>
      <div class="chart-body">
        <!-- 其他图表内容 -->
        <div class="rank-list">
          <!-- 排名列表每一行 -->
          <div v-for="(item, index) in getTop5Assets()" :key="index" class="rank-item">
            <div class="rank-icon">
              <img :src="getIconPath(index + 1)" alt="图标">
            </div>
            <div class="rank-name">{{ item.businessName }}</div>
            <div class="rank-value">{{ item.amout.toFixed(2) }} <span>{{ item.unit }}</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import request from '@/utils/request'

// 国际化
const { t } = useI18n()

// 数据响应式声明
const assetsData = ref([])

// 获取图标路径的方法
const getIconPath = (index) => {
  return new URL(`../../assets/images/zichan/icon${index}.png`, import.meta.url).href
}

// 获取数据的方法
const fetchData = async () => {
  try {
    const response = await request.get('/globalManage/group/T-group-assets/page', {
      params: {
        pageSize: 40
      }
    })
    console.log(response)
    if (response.code === 0) {
      assetsData.value = response.data.list;
      console.log(assetsData.value)
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 根据类别和业务名称获取值
const getValueByCategory = (category, businessName) => {
  console.log(assetsData.value)
  const item = assetsData.value.find(
    item => item.category === category && item.businessName === businessName
  )
  return item ? item.amout.toFixed(2) : '0.00'
}

// 获取资产排名TOP5
const getTop5Assets = () => {
  return assetsData.value
    .filter(item => item.category === '资产排名')
    .sort((a, b) => b.amout - a.amout)
    .slice(0, 5)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.global-bg {
  pointer-events: all;
  background: url('@/assets/images/zichan/bg.png') no-repeat center/cover;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 0;
}

// 中间展示区容器
.central-display {
  position: absolute;
  width: 100%; // 根据实际背景图尺寸设置
  height: 100%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);

  // 核心背景图
  .core-background {
    width: 93%;
    height: 100%;
    padding-top: 10%;
    padding-left: 7%;
  }

  // 四角底座样式
  .corner-base {
    position: absolute;
    width: 170px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .base-number {
      font-family: TCloudNumber, TCloudNumber;
      font-weight: bold;
      font-size: 36px;
      color: #FFFFFF;
      text-shadow: 0px 2px 0px #0085FF, 0px -1px 0px #455DEF;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    .base-title {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 28px;
      color: #FFFFFF;
      letter-spacing: 2px;
      text-shadow: 0px 2px 0px #0085FF, 0px -1px 0px #455DEF;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    &.top-left {
      top: 29%;
      left: 31%;
    }

    &.top-right {
      top: 29%;
      right: 31%;
    }

    &.bottom-left {
      bottom: 38%;
      left: 31%;
    }

    &.bottom-right {
      bottom: 38%;
      right: 30%;
    }
  }
}

// 象限图表通用样式
.quad-chart {
  position: absolute;
  width: 450px;
  height: 460px;

  // 定位设置
  &.lt {
    top: 10%;
    left: 2%;
  }

  &.rt {
    right: 2%;
    top: 10%;
  }

  &.lb {
    bottom: 2%;
    left: 2%;
  }

  &.rb {
    right: 2%;
    bottom: 2%;
  }

  .chart-header {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding-left: 50px;
    background: url('@/assets/images/zichan/box-title.png') no-repeat center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;

    span {
      font-family: "Alimama", sans-serif;
      font-weight: 700;
      font-size: 20px;
      background-image: linear-gradient(to bottom,
          #006FD0 0%,
          #FFFFFF 50%,
          #006FD0 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }
  }

  .chart-title {
    font-size: 14px;
    background: url('@/assets/images/zichan/oneLight.png') no-repeat center/cover;
    width: 100%;
    height: 43px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: left;
    padding-left: 50px;
  }

  .chart-title-r {
    font-size: 12px;
    background: url('@/assets/images/zichan/twoLight.png') no-repeat center/cover;
    width: 100%;
    height: 43px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: left;
    padding-left: 45px;
  }

  .chart-title-num {
    font-family: TCloudNumber;
    font-weight: 700;
    font-size: 18px;
    background-image: linear-gradient(to bottom,
        #FFE49A 0%,
        #FFFFFF 50%,
        #FFE49A 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .chart-content {
    width: 100%;
    height: 90%;
    background: url('@/assets/images/zichan/box-lt.png') no-repeat center/cover;
    background-size: cover;
    background-repeat: no-repeat;
  }

  .chart-body {
    width: 100%;
    height: calc(100% - 40px);
    padding: 15px;
    position: relative;
    background: url('@/assets/images/zichan/box-content.png') no-repeat center/cover;
    background-size: cover;
    background-repeat: no-repeat;
  }

  .circle-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .icon-item {
    position: absolute;
    display: flex;
    align-items: center;

    .icon {
      width: 40px;
      height: 40px;
      margin-right: 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .info {
      color: #fff;

      .title {
        font-size: 14px;
        margin-bottom: 2px;
      }

      .number {
        font-size: 18px;
        background-image: linear-gradient(to bottom,
            #9AC4FF 0%,
            #FFFFFF 50%,
            #9AC4FF 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
        font-weight: bold;
        font-family: TCloudNumber;
        margin-bottom: 2px;
      }
    }

    &.top {
      top: 5%;
      left: 50%;
      transform: translateX(-50%);
    }

    &.right {
      top: 50%;
      right: 5%;
      transform: translateY(-50%);
    }

    &.bottom {
      bottom: 9%;
      left: 23%;
      transform: translateX(-50%);
    }

    &.left {
      top: 50%;
      left: 5%;
      transform: translateY(-50%);
    }

    &.center-top {
      bottom: 10%;
      left: 59%;
    }
  }

  .unit {
    font-family: TCloudNumber;
    font-weight: 400;
    font-size: 12px;
    color: rgba(243, 247, 255, 0.7);
    line-height: 14px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  .rank-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .rank-item {
    display: flex;
    align-items: center;
    height: 60px;
    padding: 0 10px;
    margin-bottom: 10px;
    position: relative;

    .rank-icon {
      flex: 0 0 36px;
      height: 36px;
      margin-right: 12px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .rank-name {
      flex: 1;
      font-size: 14px;
      color: #ffffff;
      line-height: 1.4;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      padding-right: 10px;
    }

    .rank-value {
      font-family: "Alimama", sans-serif;
      flex: 0 0 auto;
      font-size: 16px;
      background-image: linear-gradient(to bottom,
          #9AC4FF 0%,
          #FFFFFF 50%,
          #9AC4FF 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
      font-weight: bold;
      white-space: nowrap;

      span {
        font-size: 12px;
        font-weight: normal;
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

// 左下方盒子特有样式
.lb-container {
  width: 100%;
  height: 100%;
  display: flex;
}

.lb-background {
  flex: 0 0 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
    max-height: 90%;
    object-fit: contain;
  }
}

.lb-indicators {
  flex: 0 0 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.indicator-item {
  display: flex;
  align-items: center;
  padding: 21px 0;
}

.indicator-icon {
  width: 90px;
  height: 90px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.indicator-data {
  display: flex;
  flex-direction: column;
}

.indicator-title {
  font-size: 14px;
  color: #FFFFFF;
  margin-bottom: 5px;
}

.indicator-value {
  white-space: nowrap;
  font-size: 18px;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: bold;

  .unit {
    font-size: 12px;
    font-weight: normal;
    color: rgba(243, 247, 255, 0.3);
    -webkit-text-fill-color: rgba(243, 247, 255, 0.3);
    background-image: none;
    margin-left: 5px;
  }
}
</style>