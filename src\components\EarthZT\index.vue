<template>
    <div class="earth-container" id="map" ref="container"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

const container = ref(null)

onMounted(() => {
    window.render = new ZTRender("map");
    // // 检查 ztmap3d 是否成功加载
    // if (!window.ztmap3d) {
    //     console.error('ztmap3d 库加载失败')
    //     return
    // }

    // try {
    //     const ztmap3d = window.ztmap3d
    //     var map = new ztmap3d.Map(container.value, {      
    //         scene:{             
    //             fxaa: true //抗锯齿
    //         },
    //     });
    //     console.log(map)
    // } catch (error) {
    //     console.error('初始化 ztmap3d 时发生错误:', error)
    // }
})

onBeforeUnmount(() => {
    // 清理代码
})
</script>

<style scoped>
.earth-container {
    width: 100%;
    height: 100%;
    position: relative;
    pointer-events: all;
}
</style>