$(function(){
    initRender();
    // httpAjaxPost("/admin-api/system/auth/login",{"username":"admin","password":"Admin258369"},function(res){
    //     console.log(res,"-------------")
    //     render.token = res.data.accessToken;
    //     httpAjax("/admin-api/globalManage/zjmanage/largescreen/getForeignInfoEarth",function(res){
    //         console.log(res,"---------");
    //     });
    // })
    window.addEventListener('message', function (e) {
        var data = e.data;
        console.log(data,"------------")
        if(data.eve=="cancle"){     //返回上一级 
            outCountry();
        }else if(data.eve=="changeModel"){     //切换模式
            changeModel(data.data);
        }else if(data.type=="token"){     //获取token
            render.token = data.data;
            // initEarth();
            getForeignInfoByName("earth","",function(){
                showCountryMeshs("earth");
            })
        }
    })
})
let render = null;
function initRender() {
    render = new ZTRender("map");
    render.mapHeight = 300;         //地图高度
    //物理灯光
    render.render.physicallyCorrectLights = true;
    // render.render.shadowMap.autoUpdate = false;         //启用场景中的阴影自动更新
    let hei = new THREE.HemisphereLight(0xffffff, 0x444444, 1);
    render.scene.add(hei);
    const light = new THREE.DirectionalLight(0xffffff, 0.5);
    // render.scene.add(light);
    light.position.set(2000, 2000, -1200); //default; light shining from top
    // light.castShadow = true; // default false
    // light.shadow.camera.near = 1;
    // light.shadow.camera.far = 3500;
    // light.shadow.camera.right = 1500;
    // light.shadow.camera.left = - 1500;
    // light.shadow.camera.top = 1500;
    // light.shadow.camera.bottom = - 1500;
    // light.shadow.mapSize.width = 1024;
    // light.shadow.mapSize.height = 1024;
    // light.shadowCameraNear = 1;
    // light.shadow.bias = -0.0000001;
    render.light = light;
    // render.control.object.position.set(0, 1000, 1000);
    render.control.maxPolarAngle = THREE.MathUtils.degToRad(85);
    // render.control.minPolarAngle = THREE.MathUtils.degToRad(-90);
    // render.control.panTarget.set(-94.46866670200413,2.000000000000003, -59.76570390048769);
    // render.control.screenSpacePanning = true;
    render.stats = new Stats();
    render.stats.domElement.style.zIndex = 100;
    // document.body.appendChild(render.stats.domElement);
    // render.setView({target:{x:-244.6791734231468,y:-39.017211561393395,z:-175.49559372613163},position:{x:-801.3918166941709,y:444.0666454205334,z:-1185.6819059330871}});
    render.setView({target:{x:370.687270800479,y:0,z:0},position:{x:23335.38031065261,y:5522.579038603469,z:8683.02007843369}});
    render.events = new ZTMapEvents(render.scene, render.camera, render.container);

    render.update = function () {
        render.stats.update();
        if(render.lightGroups)render.lightGroups.rotation.y = render.control.getAzimuthalAngle();
        if(render.earthInnerObject) render.earthInnerObject.rotation.y = render.control.getAzimuthalAngle() - THREE.MathUtils.degToRad(90);
    }
    render.scene.children[1].intensity = 1;
    render.animationsTool = new Animations();
    render.labelRenderer.enableAvoid = true;
    render.control.enableDamping = true;
    render.control.dampingFactor = 0.1;
    render.control.zoomSpeed = 3;
    render.control.mouseButtons = { LEFT: THREE.MOUSE.ROTATE, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.PAN };
    // render.control.minAzimuthAngle = THREE.MathUtils.degToRad(85);
    // render.control.maxAzimuthAngle = THREE.MathUtils.degToRad(-150);
    initCubeTexture(render,function(){
    })
    initEarth();
    window.parent.postMessage({eve:"loadOk"},"*");
    initLights();
}
function initLights(){
    render.lightGroups = new THREE.Group();
    render.scene.add(render.lightGroups);
    // let pointLight = new THREE.SpotLight({color:0xffffff});
    let pointLight = new THREE.PointLight({color:0xffffff});
    // const spotLightHelper = new THREE.SpotLightHelper( pointLight );
    // render.lightGroups.add( spotLightHelper );
    render.pointLight = pointLight;
    render.lightGroups.add(render.pointLight);
    render.pointLight.intensity = 70000;
    render.pointLight.decay = 1.05;
    render.pointLight.distance = 100000;
    render.pointLight.position.set(7698.521564563551, 2925.6269801880658,517.6721715458398);
    let pointLight1 = pointLight.clone();
    pointLight1.position.set(-7698.521564563551, 2925.6269801880658,200);
    render.lightGroups.add(pointLight1);

}
function initCubeTexture(render,callback){
    var path = "sky/";
    var format = '.jpg';
    var urls = [
        "px"+ format, "nx"+ format,
        "py"+ format, "ny"+ format,
        "pz"+ format, "nz"+ format
    ];
    const loader = new THREE.CubeTextureLoader();
    loader.setPath( path );
    render.envMap = loader.load( urls );
    // render.envMap.encoding = THREE.sRGBEncoding;
    if(callback) callback();
    // render.scene.background = render.envMap;
    render.scene.environment = render.envMap;
}
// 创建地球
function initEarth(){
    // render.earthSize = 6371000;
    render.earthSize = 6371;
    // let texture = new THREE.TextureLoader().load("textures/earth4.jpg");
    render.earthMaterial = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,opacity:1,metalness:0,roughness:0,depthWrite:false});  //
    render.planMapMaterial = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,opacity:1,metalness:0,roughness:0,depthWrite:false});  //
    let sphereGeometry = new THREE.SphereGeometry(render.earthSize, 64, 64);
    render.earthObject = new THREE.Mesh(sphereGeometry, render.earthMaterial);
    render.earthObject.renderOrder = 2;
    render.scene.add(render.earthObject);
    render.earthObject.currModel = "earth";
    render.control.enablePan = false;
    render.control.minDistance = 15000;
    render.control.maxDistance = 50000;
    let sphereGeometry1 = new THREE.SphereGeometry(render.earthSize + 1000, 12, 12);
    render.sphereLineMaterial = new THREE.MeshStandardMaterial({color:0x0b4288,transparent:true,opacity:0.1,wireframe:true,depthWrite:false,blending:2,roughness:0,metalness:0});  //
    render.sphereLineObject = new THREE.Mesh(sphereGeometry1, render.sphereLineMaterial);
    render.sphereLineObject.scale.y = 0.93;
    render.scene.add(render.sphereLineObject);
    render.sphereLineObject.renderOrder = 3;
    // test();
    initWorldJSON();
    initStars(sphereGeometry1);

    let sphereGeometry2 = new THREE.SphereGeometry(render.earthSize - 100, 64, 64);
    let earthInnerTexture = createEarthBg();
    earthInnerTexture.offset.y = -0.05;
    render.earthInnerMaterial = new THREE.MeshLambertMaterial({color:0xffffff,transparent:true,opacity:1,roughness:0,metalness:0.2,map:earthInnerTexture,envMapIntensity:0.8});  //
    render.earthInnerObject = new THREE.Mesh(sphereGeometry2, render.earthInnerMaterial);
    render.earthInnerObject.renderOrder = 1;
    render.scene.add(render.earthInnerObject);
    // let earthInnerObjectClone = render.earthInnerObject.clone();
    // earthInnerObjectClone.material = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,opacity:1,roughness:0,metalness:0.2,map:earthInnerTexture,envMapIntensity:0.8});  //
    // render.earthInnerObject.add(earthInnerObjectClone);
    
    render.control.autoRotate = true;
    render.events.on("hover",render.earthInnerObject,function(){
        render.control.autoRotate = false;
    },function(){
        if(render.earthObject.currModel=="earth")render.control.autoRotate = true;
    })

    render.currCountrysModel = new THREE.Group();
    render.currCountrysModel.scale.y = render.mapHeight;
    render.scene.add(render.currCountrysModel);
    render.labelRenderer.enableIntersect = true;
    render.currCountrysModel.level = "earth";

    // getForeignInfoByName("earth","",function(){
    //     showCountryMeshs("earth");
    // })
    window.parent.postMessage({eve:"clickTitle",title:"earth"},"*");
}
// 经纬度球面坐标
function lonlatToEarthVector3(longitude, latitude,radius) {
    var lg = THREE.MathUtils.degToRad(90 - longitude);// - THREE.MathUtils.degToRad(110);
    var lt = THREE.MathUtils.degToRad(latitude);
    var temp = radius * Math.cos(lt);
    var x = temp * Math.sin(lg);
    var y = radius * Math.sin(lt);
    var z = temp * Math.cos(lg);
    // console.log(new THREE.Spherical( radius, lg, lt ));
    return {
        x: x,
        y: y,
        z: -z
    }
}
// 经纬度平面坐标
function lonlatToPlanVector3(longitude, latitude,radius) {
    var theta = THREE.MathUtils.degToRad(longitude);
    var phi = THREE.MathUtils.degToRad(latitude);
    // var temp = render.earthSize * Math.cos(lt);
    // var x = temp * Math.sin(lg);
    // var y = render.earthSize * Math.sin(lt);
    // var z = temp * Math.cos(lg);

    // 将球面坐标投影到平面
    // const theta = Math.atan2(z, x);
    // const phi = Math.asin(y / radius);
    // 计算平面坐标
    const u = theta / (2 * Math.PI) + 0.5;
    const v = phi / Math.PI + 0.5;
    let w_ = 2 * Math.PI * radius;
    let h_ = w_ / 2;
    let x = u * w_ - h_;
    let z = v * h_ - (h_ / 2);
    return {
        x: -x,
        y: 0,
        z: z
    }
    // let h_ = w_ / 1.5;
    // let x = u * w_  - (h_ / 2);
    // let z = v * h_ - (h_ / 2);
    // return {
    //     x: -x,
    //     y: 0,
    //     z: z
    // }
}
/**
 * 切换地图模式
 * @param {*} model  plan:平面，earth:地球
 * @returns 
 */
function changeModel(model,callback,isNotHttp){
    if(["plan","earth"].indexOf(model)==-1) return;
    if(render.earthObject.currModel == model) return;
    render.earthObject.currModel = model;
    clearModels();
    if(!render.earthObject.planPositions){
        let sphereGeometry = render.earthObject.geometry;
        const spherePositions = sphereGeometry.getAttribute('position');
        const positions = [],earthPositions = [];
        const r = render.earthSize;
        for (let i = 0; i < spherePositions.count; i++) {
            const x = spherePositions.getX(i);
            const y = spherePositions.getY(i);
            const z = spherePositions.getZ(i);
            earthPositions.push(x,y,z);
            // 将球面坐标投影到平面
            const theta = Math.atan2(z, x);
            const phi = Math.asin(y / r);
            // 计算平面坐标
            const u = theta / (2 * Math.PI) + 0.5;
            const v = phi / Math.PI + 0.5;
            let w_ = 2 * Math.PI * r;
            let h_ = w_ / 2;
            positions.push(u * w_ - h_, 0, v * h_ - (h_ / 2));
        }
        render.earthObject.planPositions = positions;
        render.earthObject.earthPositions = earthPositions;
    }
    if(model=="plan"){
        render.setView({target:{x:-1505.9191539023175,y:-4.1175299284803975e-14,z:2124.0203267875154},position:{x:-1505.9193063514576,y:38231.20097475156,z:2123.9820958904916}});
        render.control.enablePan = true;
        render.control.minDistance = 8000;
        if(render.sphereLineObject)render.sphereLineObject.visible = false;
        if(render.earthInnerObject)render.earthInnerObject.visible = false;
        render.control.autoRotate = false;
        render.lightGroups.visible = false;
        render.earthObject.material = render.planMapMaterial;
        $(".cir2,.cir1").hide();
        render.control.mouseButtons = { LEFT: THREE.MOUSE.PAN, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.ROTATE };
        render.control.enableRotate = false;
        render.labelRenderer.enableIntersect = false;
        $("body").attr("code","plan");
    }else{
        render.setView({target:{x:370.687270800479,y:0,z:0},position:{x:23335.38031065261,y:5522.579038603469,z:8683.02007843369}});
        render.control.enablePan = false;
        render.control.minDistance = 15000;
        render.earthObject.visible = true;
        render.countryMeshs.visible = false;
        render.control.mouseButtons = { LEFT: THREE.MOUSE.ROTATE, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.PAN };
        render.control.enableRotate = true;
        render.labelRenderer.enableIntersect = true;
    }
    render.animationsTool.setPosition(render.earthObject, {
        start: { x: 0 }, position: { x: 1 }, delta: 1000, update: function (_obj, param) {
            if (param.object){
                const earthPositions = param.object.earthPositions;
                const positions = [];
                for (let i = 0; i < earthPositions.length; i+=3) {
                    const x = earthPositions[i];
                    const y = earthPositions[i+1];
                    const z = earthPositions[i+2];
                    const tx = param.object.planPositions[i];
                    const ty = param.object.planPositions[i+1];
                    const tz = param.object.planPositions[i+2];
                    let np = new THREE.Vector3(x,y,z);
                    if(model=="plan"){
                        np = np.lerp(new THREE.Vector3(tx,ty,tz),_obj.x);
                    }else{
                        np = new THREE.Vector3(tx,ty,tz).lerp(np,_obj.x);
                    }
                    positions.push(np.x,np.y,np.z);
                }
                param.object.geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
            }
        },callback:function(){
            render.earthObject.geometry.computeVertexNormals();
            render.earthObject.geometry.computeBoundingSphere();
            if(model=="earth"){
                if(render.sphereLineObject)render.sphereLineObject.visible = true;
                if(render.earthInnerObject)render.earthInnerObject.visible = true;
                render.control.autoRotate = true;
                render.lightGroups.visible = true;
                render.earthObject.material = render.earthMaterial;
                $(".cir2,.cir1").show();
                $("body").attr("code","earth");
                if(!isNotHttp){
                    getForeignInfoByName("earth","",function(){
                        showCountryMeshs("earth");
                    })
                }
            }else{
                // render.earthObject.visible = false;
                if(!isNotHttp){
                    getForeignInfoByName("earth","",function(){
                        showCountryMeshs("plan");
                    })
                }
                render.setView({target:{x:-1505.9191539023175,y:-4.1175299284803975e-14,z:2124.0203267875154},position:{x:-1505.9193063514576,y:38231.20097475156,z:2123.9820958904916}});
            }
            if(callback) callback();
        }
    })
}
function showCountryMeshs(model){
    if(model=="plan"){
        render.countryMeshs.visible = true;
        // render.labelRenderer.enableIntersect = false;
    }else{
        render.labelRenderer.intersectObjects = [render.earthInnerObject];
        // render.labelRenderer.enableIntersect = true;
    }
    let children = render.countryMeshs.children;
    for(let i=0;i<children.length;i++){
        if(model=="plan") children[i].visible = true;
        loadCountryName(children[i],children[i].name,model);
    }
}
function initWorldJSON(){
    $.ajax({ 
        type : "GET", //提交方式 
        url : "./data/world.json",
        async: false,
        success : function(response) {//返回数据根据结果进行相应的处理 
           if(typeof response == "string") response = JSON.parse(response);
            let features = response.features;
            var canvas = document.createElement('canvas');
            // $("body").append(canvas);
            let w = 6371 * 2 * Math.PI / 10;
            let h = w / 2;
            canvas.width = w;
            canvas.height = h;
            var context = canvas.getContext('2d');
            var centerX = w / 2;
            var centerY = h / 2;
            var average = w / 360;
            context.strokeStyle = "#ffffff";
            context.clearRect(0, 0, w, h);
            render.countryMeshs = new THREE.Group();
            render.countryMeshs.position.y = -0.1;
            render.scene.add(render.countryMeshs);
            render.countryMeshs.visible = false;
            render.countryMeshs.scale.y = render.mapHeight;
            for(let i=0;i<features.length;i++){
                let geometry = features[i].geometry;
                let coordinates = geometry.coordinates;
                let properties  = features[i].properties;
                let group = new THREE.Group();
                properties.name_en = properties.name;
                properties.name = properties.name_zh || properties.name;
                group.name = properties.name;
                render.countryMeshs.add(group);
                let polys = {properties:properties,coordinates:[]};
                let maxX = -Infinity,maxY = -Infinity,minX = Infinity,minY = Infinity;
                if(geometry.type=="MultiPolygon"){
                    for(let j=0;j<coordinates.length;j++){
                        draw(context,coordinates[j][0],centerX,centerY,average);
                        if(coordinates[j][0]){
                            let box = getPolyBox(coordinates[j][0]);
                            maxX = Math.max(maxX,box.maxX);
                            maxY = Math.max(maxY,box.maxY);
                            minX = Math.min(minX,box.minX);
                            minY = Math.min(minY,box.minY);
                            let mesh = getPoly(coordinates[j][0]);
                            mesh.name = properties.name;
                            mesh.userData = properties;
                            group.add(mesh);
                            polys.coordinates.push(coordinates[j][0]);
                        }
                    }
                }else{
                    draw(context,coordinates[0],centerX,centerY,average);
                    if(coordinates[0]){
                        let box = getPolyBox(coordinates[0]);
                        maxX = Math.max(maxX,box.maxX);
                        maxY = Math.max(maxY,box.maxY);
                        minX = Math.min(minX,box.minX);
                        minY = Math.min(minY,box.minY);
                        let mesh = getPoly(coordinates[0]);
                        mesh.name = properties.name;
                        mesh.userData = properties;
                        group.add(mesh);
                        polys.coordinates.push(coordinates[0]);
                    }
                }
                polys.properties.center = [minX+(maxX - minX)/2,minY+(maxY - minY)/2];
                group.polys = polys;
            }
            let texture = new THREE.CanvasTexture(canvas);
            render.earthMaterial.map = texture;
            render.earthMaterial.needsUpdate = true;
            var canvas1 = document.createElement('canvas');
            canvas1.width = canvas.width;
            canvas1.height = canvas.height;
            let ctx = canvas1.getContext("2d");
            var gradient = ctx.createLinearGradient(0, 0, canvas1.width, 0); // 从左到右
            gradient.addColorStop(0, 'rgba(18, 34, 77,0)');
            gradient.addColorStop(0.5, 'rgba(18, 34, 77,0.3)');  
            gradient.addColorStop(1, 'rgba(18, 34, 77,0)'); 
            // ctx.fillStyle = "rgba(18, 34, 77,0.3)";
            ctx.fillStyle = gradient;
            ctx.fillRect(0,50,w,h - 100);
            ctx.drawImage(canvas,0,0);
            let texture1 = new THREE.CanvasTexture(canvas1);
            render.planMapMaterial.map = texture1;
            render.planMapMaterial.needsUpdate = true;
        }
   })
}
function loadCountryName(object,name,model){
    if(!object) return;
    let center = {x:0,y:0,z:0};
    // console.log(datas.data[0][name],name,datas.data[0],"--------");
    if(render.foreignInfoEarth&&render.foreignInfoEarth[name]){
        let names = render.foreignInfoEarth[name].split(":");
        render.foreignInfoEarth[name] = names[0];
        let code = "";
        if(names[1]>0) code = "yd";
        if(model=="plan"){
            center = lonlatToPlanVector3(object.polys.properties.center[0],object.polys.properties.center[1],render.earthSize);
            render.setInfoWindow(`<div class="countryName" type="country" code="${code}" onClick="inCountry('${name}')" adcode="${name}">${name}（${render.foreignInfoEarth[name]}）</div>`,{x:center.x,y:0,z:center.z},{name:name});
        }else{
            center = lonlatToEarthVector3(object.polys.properties.center[0],object.polys.properties.center[1],render.earthSize);
            render.setInfoWindow(`<div class="countryName" type="country" code="${code}" onClick="inCountryByEarth('${name}')" adcode="${name}">${name}（${render.foreignInfoEarth[name]}）</div>`,{x:center.x,y:center.y,z:center.z},{name:name});
        }
        if(render.infoWindos[name]) render.infoWindos[name].isAvoid = true;
    }
}
function draw(ctx,datas,centerX,centerY,average){
    // ctx.closePath();
    if(!datas) return;
    let maxX = -Infinity,maxY = -Infinity,minX = Infinity,minY = Infinity;
    for(let i=0;i<datas.length;i++){
        maxX = Math.max(centerX + datas[i][0] * average,maxX);
        maxY = Math.max(centerY - datas[i][1] * average,maxY);
        minX = Math.min(centerX + datas[i][0] * average,minX);
        minY = Math.min(centerY - datas[i][1] * average,minY);
    }
    let w = Math.abs((maxX - minX));
    let h = Math.abs((maxY - minY));
    let r = Math.max(w,h);
    let cx = minX + w / 2;
    let cy = minY + h / 2;
    if(r<10) return;
    // console.log(w,h,"-------")
    ctx.beginPath();
    // 创建径向渐变，从中心向外扩展
    // var gradient = ctx.createRadialGradient(cx, cy, 0, cx, cy, Math.min(w,h));
    // gradient.addColorStop(0, 'rgba(106, 186, 255,0)'); // 开始颜色为白色
    // // gradient.addColorStop(0.1, 'rgba(106, 186, 255,0)'); // 开始颜色为白色
    // // gradient.addColorStop(0.2, 'rgba(106, 186, 255,0)'); // 开始颜色为白色
    // gradient.addColorStop(1, '#6abaff');  // 结束颜色为蓝色
    ctx.fillStyle = 'rgba(26, 73, 112,1)';
    // 设置阴影属性
    ctx.shadowOffsetX = 0;       // 水平方向阴影偏移量
    ctx.shadowOffsetY = 0;       // 垂直方向阴影偏移量
    ctx.shadowBlur = 3;        // 阴影模糊程度
    // ctx.shadowColor = 'rgba(106, 186, 255,1)'; // 阴影颜色
    ctx.shadowColor = '#fff'; // 阴影颜色
    for(let i=0;i<datas.length;i++){
        if(i==0){
            ctx.moveTo(centerX + datas[i][0] * average, centerY - datas[i][1] * average);
        }else{
            ctx.lineTo(centerX + datas[i][0] * average, centerY - datas[i][1] * average);
        }
    }
    // ctx.fill();//stroke
    ctx.stroke();
    ctx.closePath();
}
function getSprite(img){
    let material = null;
    if(!render.spriteMaterials) render.spriteMaterials = {};
    if(render.spriteMaterials[img]){
        material = render.spriteMaterials[img];
    }else{
        const map = new THREE.TextureLoader().load( img );
        material = new THREE.SpriteMaterial( { map: map,transparent:true,sizeAttenuation:true,blending:2 } );
        render.spriteMaterials[img] = material;
    }
    const sprite = new THREE.Sprite( material );
    sprite.renderOrder = 99;
    return sprite;
}
function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min)) + min;
}
function initStars(geometry){
    let positions = geometry.getAttribute("position").array;
    let sprite = getSprite("textures/star.png");
    let sprite_ = getSprite("textures/circle.png");
    let newPositions = [];
    for(let i=0;i<positions.length;i+=3){
        let x = positions[i] + getRandomInt(-200,200);
        let y = positions[i+1];
        let z = positions[i+2] + getRandomInt(-200,200);
        newPositions.push(x,y,z);
        let sprite1 = sprite.clone();
        let scale = getRandomInt(300,600);
        sprite1.position.set(x,y,z);
        sprite1.scale.set(scale,scale,scale);
        let sprite2 = sprite_.clone();
        sprite2.position.set(x,y,z);
        sprite2.scale.set(200,200,200);
        render.sphereLineObject.add(sprite1);
        render.sphereLineObject.add(sprite2);
    }
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(newPositions, 3));
    geometry.computeVertexNormals();
    geometry.computeBoundingSphere();
}
function createEarthBg(){
    var canvas = document.createElement('canvas');
    // $("body").append(canvas);
    canvas.width = 256;
    canvas.height = canvas.width / 2;
    var ctx = canvas.getContext('2d');
    var gradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, canvas.width / 4);
    gradient.addColorStop(0, 'rgba(0, 0, 0, 0)'); 
    // gradient.addColorStop(0.4, 'rgba(11, 55, 107,1)'); 
    // gradient.addColorStop(1, 'rgba(13, 78, 158,1)'); 
    gradient.addColorStop(0.4, 'rgb(13, 83, 167)'); 
    gradient.addColorStop(1, 'rgb(24, 125, 251)'); 
    ctx.fillStyle = gradient;
    ctx.fillRect(0,0,canvas.width,canvas.height);
    let texture = new THREE.CanvasTexture(canvas);
    return texture;
}
//绘制城市边界
function createCityBound(w,h,sw,sh,poins,title){
    var canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;
    var ctx = canvas.getContext('2d');
    ctx.clearRect(0,0,w,h);
    ctx.lineWidth = 5;
    ctx.strokeStyle = "#fff";
    let wR = w / 512,hR = h/512;
    for(let i=0;i<poins.length;i+=2){
        if(i==0){
            ctx.moveTo((poins[i].x - sw)/wR,(poins[i].z - sh)/hR);
        }else{
            ctx.lineTo((poins[i].x - sw)/wR,(poins[i].z - sh)/hR);
        }
    }
    ctx.stroke();
    if(title){
        ctx.fillText(title,w/2,h/2);
    }
    // let texture = new THREE.CanvasTexture(canvas);
    return canvas;
}
function getPolyBox(points){
    let maxX = -Infinity,minX = Infinity,maxY = -Infinity,minY = Infinity;
    for(let i=0;i<points.length;i++){
        maxX = Math.max(maxX,points[i][0]);
        maxY = Math.max(maxY,points[i][1]);
        minX = Math.min(minX,points[i][0]);
        minY = Math.min(minY,points[i][1]);
    }
    return {minX,minY,maxX,maxY}
}
//绘制面
function getPoly(points){
    let shape = new THREE.Shape();
    let maxX = -Infinity,minX = Infinity,maxY = -Infinity,minY = Infinity;
    let cityPoints = [];
    // console.log(points)
    for(let i=0;i<points.length;i++){
        let point = lonlatToPlanVector3(points[i][0],points[i][1],render.earthSize);
        maxX = Math.max(maxX,point.x);
        maxY = Math.max(maxY,point.z);
        minX = Math.min(minX,point.x);
        minY = Math.min(minY,point.z);
        if(i==0){
            shape.moveTo(point.x,point.z);
        }else{
            shape.lineTo(point.x,point.z);
        }
        cityPoints.push(point);
    }
    let xLen = maxX - minX;
    let yLen = maxY - minY;
    let depth = 1;
    // if(Math.min(xLen,yLen)<50) depth = 0.01;
    let geometry = new THREE.ExtrudeGeometry( shape,{ depth: depth, bevelEnabled: false, bevelSegments: 2, steps: 2, bevelSize: 1, bevelThickness: 1 } );
    let positions = geometry.attributes.position.array;
    let newUvs = [];
    for(let i=0;i<positions.length;i+=3){
        let u = (positions[i] - minX) / xLen;
        let v = 1 - (positions[i+1] - minY) / yLen;
        newUvs.push(u,v);
    }
    // let canvas = createCityBound(xLen,yLen,minX,minY,cityPoints,"test");

    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(newUvs, 2));
    if(!render.mapMaterial) {
        let texture = new THREE.TextureLoader().load("textures/planMap.png");
        // let texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
        render.mapMaterial = new THREE.MeshStandardMaterial({color:0xffffff,side:2,map:texture,transparent:true,metalness:0.3,roughness:0});
        // render.mapMaterial1 = new THREE.MeshStandardMaterial({color:0x888888,side:2});
    }
    let mesh = new THREE.Mesh( geometry, render.mapMaterial);
    // if(depth == 0.01){
    //     mesh.material = render.mapMaterial1;
    // }
    mesh.rotation.x = THREE.MathUtils.degToRad(90);
    // group.add( mesh );
    return mesh;
}
function getJsonFeatures(features){
    let _features = [];
    for(let i=0;i<features.length;i++){
        let geometry = features[i].geometry;
        let coordinates = geometry.coordinates;
        let _coordinates = [];
        if(geometry.type=="MultiPolygon"){
            for(let j=0;j<coordinates.length;j++){
                _coordinates.push(coordinates[j][0]);
            }
        }else{  
            _coordinates.push(coordinates[0]);
        }
        _features.push({properties:features[i].properties,coordinates:_coordinates});
    }
    return _features;
}
function inCountryByEarth(country){
    changeModel("plan",function(){
        // console.log(country)
        inCountry(country,"earth");
    },true)
}
//进入国家
function inCountry(country,type){
    if(!render.countrys) render.countrys = [];
    render.countrys.push(country);
    render.control.minDistance = 4000;
    render.earthObject.visible = false;
    render.countryMeshs.visible = false;
    render.control.enableRotate = true;
    if(type=="earth") render.inCountryType = "earth";
    else render.inCountryType = "plan";
    window.parent.postMessage({eve:"clickTitle",title:country,level:"country"},"*");
    if(country=="China" || country=="中国"){
        getForeignInfoByName("country",country,function(datas){
            initGeoJsonDataByCode(100000,function(result){
                drawCountry(result,datas);
            })
        })
        render.currCountrysModel.level = "province";
    }else{
        let country_ = render.countryMeshs.getObjectByName(country);
        render.currCountrysModel.level = "province";
        if(country_){
            drawCountry({outF:[country_.polys],inF:[]});
            initDemoInfoByName(country,true);
        }
    }
}
function clearModels(){
    let children = render.currCountrysModel.children;
    let length = children.length;
    let removeObjects = [];
    for(let i=0;i<length;i++){
        // console.log(children.length,"--------")
        if(children[i].geometry) children[i].geometry.dispose();
        if(children[i].material) children[i].material.dispose();
        removeObjects.push(children[i]);
    }
    for(let i=0;i<removeObjects.length;i++){
        render.currCountrysModel.remove(removeObjects[i]);   
    }
    render.clearInfoWindows();
    if(render.labelRenderer.showAvoids)render.labelRenderer.showAvoids.clear()
}
function drawCountry(datas,demoDatas){
    if(!demoDatas) demoDatas = {};
    clearModels();
    let outF = datas.outF;
    let maxLen = -Infinity,currPolys = [];
    for(let i=0;i<outF.length;i++){
        let coordinates = outF[i].coordinates;
        for(let j=0;j<coordinates.length;j++){
            let mesh = getPoly(coordinates[j]);
            let box = getObjectBox(mesh);
            let length = Math.max(maxLen,Math.max(box.max.x-box.min.x,box.max.z-box.min.z));
            if(length>maxLen){
                maxLen = length;
                currPolys = coordinates[j];
                render.currCountrysModel.currPolys = currPolys;
            }
            render.currCountrysModel.add(mesh);
        }
    }
    getCountryTexture(datas,demoDatas);
    let box = getObjectBox(render.currCountrysModel);
    let length = Math.max((box.max.x - box.min.x),(box.max.z - box.min.z));
    let center = box.getCenter(new THREE.Vector3());
    render.control.minDistance = length;
    render.currCountrysModel.scale.y = length / 40;
    // if(render.currCountrysModel.scale.y < 200) render.currCountrysModel.scale.y = 200;
    render.viewAnimate({target:{x:center.x,y:box.max.y+0.005,z:center.z},position:{x:center.x,y:length,z:center.z-length}})
}
function getObjectBox(object) {
    var box = new THREE.Box3();
    box.setFromObject(object);
    return box;
}
//获取城市贴图
function getCountryTexture(datas,demoDatas){
    let outF = datas.outF;
    let inF = datas.inF;
    let maxX = -Infinity,minX = Infinity,maxY = -Infinity,minY = Infinity;
    let positions = [];
    for(let i=0;i<outF.length;i++){
        let coordinates = outF[i].coordinates;
        let properties = outF[i].properties;
        render.currCountrysModel.acroutes = properties.acroutes;
        if(inF.length==0&&properties){
            let centroid = properties.centroid || properties.center;
            if(centroid || properties.countryCenter){
                let point = {x:0,y:0,z:0};
                if(properties.countryCenter) point = properties.countryCenter;
                else point = lonlatToPlanVector3(centroid[0],centroid[1],render.earthSize);
                // render.setInfoWindow(`<div class="countryName" type="${properties.level}" onClick="clickCountryName(${properties.adcode},false,'${properties.name}','${properties.level}')" adcode="${properties.adcode}">${properties.name}</div>`,{x:point.x,y:0,z:point.z},{name:properties.name});
                render.setInfoWindow(`<div style="pointer-events:none;" class="countryName" type="${properties.level}" adcode="${properties.adcode}">${properties.name}</div>`,{x:point.x,y:0,z:point.z},{name:properties.name});
                // if(render.infoWindos[properties.name]) render.infoWindos[properties.name].isAvoid = true;
            }
        }
        for(let j=0;j<coordinates.length;j++){
            positions[j] = [];
            for(let p=0;p<coordinates[j].length;p++){
                let point = lonlatToPlanVector3(coordinates[j][p][0],coordinates[j][p][1],render.earthSize);
                maxX = Math.max(maxX,point.x);
                maxY = Math.max(maxY,point.z);
                minX = Math.min(minX,point.x);
                minY = Math.min(minY,point.z);
                positions[j].push(point);
            }
        }
    }
    // console.log(maxX,maxY,minX,minY,positions,"----------");
    let tW = 2048;
    let w = Math.abs((maxX - minX));
    let h = Math.abs((maxY - minY));
    let wR = (w / tW) || 1;
    let hR = (h / tW) || 1;
    var canvas = document.createElement('canvas');
    canvas.width = canvas.height = tW;
    // $("body").append(canvas);
    let ctx = canvas.getContext("2d");
    ctx.clearRect(0,0,tW,tW);
    // var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0); // 从左到右
    var gradient = ctx.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, tW);
    gradient.addColorStop(0, 'rgba(136, 216, 255,1)');
    gradient.addColorStop(0.2, 'rgba(136, 216, 255,0.4)'); 
    gradient.addColorStop(0.5, 'rgba(136, 216, 255,0.1)');  
    gradient.addColorStop(0.9, 'rgba(136, 216, 255,0.4)'); 
    gradient.addColorStop(1, 'rgba(136, 216, 255,1)'); 
    ctx.strokeStyle = gradient;
    ctx.lineWidth = 2;
    ctx.shadowBlur = 3;        // 阴影模糊程度
    ctx.shadowColor = 'rgba(136, 216, 255,1)'; // 阴影颜色
    let planGeometry = new THREE.PlaneBufferGeometry(1,1);
    for(let i=0;i<inF.length;i++){
        let coordinates = inF[i].coordinates;
        let properties = inF[i].properties;
        for(let j=0;j<coordinates.length;j++){
            ctx.beginPath();
            for(let p=0;p<coordinates[j].length;p++){
                let point = lonlatToPlanVector3(coordinates[j][p][0],coordinates[j][p][1],render.earthSize);
                if(p==0) ctx.moveTo((point.x - minX) / wR,tW - (point.z - minY) / hR);
                else ctx.lineTo((point.x - minX) / wR,tW - (point.z - minY) / hR);
            }
            ctx.strokeStyle = gradient;
            if(demoDatas[properties.name]){
                ctx.fillStyle = "rgba(57, 131, 164,0.2)";
                ctx.fill();
                ctx.strokeStyle = "rgb(128, 217, 255)";
            }
            ctx.stroke();
            ctx.closePath();
        }
        // demoDatas
        if(properties){
            if(properties.level) render.currCountrysModel.level = properties.level;
            let centroid = properties.centroid || properties.center;
            if(centroid&&demoDatas[properties.name]){
            // if(centroid){
                let point = lonlatToPlanVector3(centroid[0],centroid[1],render.earthSize);
                render.setInfoWindow(`<div class="countryName" type="${properties.level}" onClick="clickCountryName(${properties.adcode},false,'${properties.name}','${properties.level}')" adcode="${properties.adcode}">${properties.name}（${demoDatas[properties.name] || 0}）</div>`,{x:point.x,y:0,z:point.z},{name:properties.name});
                if(render.infoWindos[properties.name]){
                    render.infoWindos[properties.name].isAvoid = true;
                    render.infoWindos[properties.name].userData = {outF:[inF[i]],inF:[]};
                }
            }
        }
    }
    ctx.lineWidth = 3;
    ctx.strokeStyle = "#fff";
    ctx.shadowBlur = 10;        // 阴影模糊程度
    ctx.shadowColor = '#fff'; // 阴影颜色
    for(let i=0;i<positions.length;i++){
        let points = positions[i];
        ctx.beginPath();
        for(let j=0;j<points.length;j++){
            if(j==0) ctx.moveTo((points[j].x - minX) / wR,tW - (points[j].z - minY) / hR);
            else ctx.lineTo((points[j].x - minX) / wR,tW - (points[j].z - minY) / hR);
        }
        ctx.stroke();
        ctx.closePath();
    }
    let ver = [
        minX,0,maxY,maxX,0,maxY,minX,0,minY,maxX,0,minY
    ]
    planGeometry.setAttribute('position', new THREE.Float32BufferAttribute(ver, 3));
    planGeometry.computeVertexNormals();
    planGeometry.computeBoundingSphere();
    let material = new THREE.MeshStandardMaterial({color:0xffffff,transparent:true,map:new THREE.CanvasTexture(canvas),side:2});
    let mesh = new THREE.Mesh(planGeometry,material);
    mesh.position.y = 0.005;
    render.currCountrysModel.add(mesh);
}
function clickCountryName(adcode,isOut,name,level){
    if(!isOut&&render.countrys[render.countrys.length-1] != name){
        render.countrys.push(name);
    }
    window.parent.postMessage({eve:"clickTitle",title:name,level:render.currCountrysModel.level},"*");
    if(!isOut&&level=="district"){
        console.log("最低级别");
        if(name&&render.infoWindos[name]){
            render.currCountrysModel.level = "county";
            drawCountry(render.infoWindos[name].userData);
            initDemoInfoByName(name);
        }
        return;
    }
    getForeignInfoByName(level,name,function(datas){
        initGeoJsonDataByCode(adcode,function(result){
            // console.log(result,"---------")
            drawCountry(result,datas);
        })
    })
}
function outCountry(){
    if(render.currCountrysModel.level=="earth" || render.isOutCountry) return;
    render.isOutCountry = true;
    setTimeout(() => {
        render.isOutCountry = false;
    }, 2000);
    clearModels();
    if(render.currCountrysModel.level=="province"){
        render.currCountrysModel.level = "earth";
        if(render.inCountryType=="earth"){
            changeModel(render.inCountryType);
        }else{
            render.earthObject.visible = true;
            render.control.minDistance = 8000;
            render.control.enableRotate = false;
            getForeignInfoByName("earth","",function(){
                showCountryMeshs("plan");
            })
            render.viewAnimate({target:{x:-1505.9191539023175,y:-4.1175299284803975e-14,z:2124.0203267875154},position:{x:-1505.9193063514576,y:38231.20097475156,z:2123.9820958904916}});
        }
    }else{
        let level = getPrevLevel();
        let name = render.countrys[render.countrys.length - 2];
        let currName = render.countrys[render.countrys.length - 1];
        if(["上海市","北京市","重庆市","天津市"].indexOf(currName)>-1){
            level = getTsCityLevel(currName,level);
            level = getPrevLevel(level);
        }
        window.parent.postMessage({eve:"clickTitle",title:name,level:level},"*");
        clickCountryName(render.currCountrysModel.acroutes[render.currCountrysModel.acroutes.length-1],true,name,level);
    }
    if(render.countrys.length>0)render.countrys.splice(render.countrys.length-1,1);
}
//获取直辖市的层级
function getTsCityLevel(name,level){
    if(["上海市","北京市","重庆市","天津市"].indexOf(name)>-1){
        return "city";
    }else{
        return level;
    }
}
function getPrevLevel(level){
    let result = "";
    if(!level) level = render.currCountrysModel.level;
    switch (level) {
        case "district":
            result = "province"
            break;
        case "city":
            result = "country"
            break;
        case "county":
            result = "city"
            break;
        default:
            break;
    }
    return result;
}
//根据code获取边界数据
function initGeoJsonDataByCode(code,callback){
    $.ajax({ 
        type : "GET", //提交方式 
        url : `https://geo.datav.aliyun.com/areas_v3/bound/${code}.json`,
        success : function(response) {//返回数据根据结果进行相应的处理 
            // if(callback) callback(response);
            let features = getJsonFeatures(response.features);
            let result = {outF:features};
            $.ajax({ 
                type : "GET", //提交方式 
                url : `https://geo.datav.aliyun.com/areas_v3/bound/${code}_full.json`,
                success : function(response) {//返回数据根据结果进行相应的处理 
                    result.inF = getJsonFeatures(response.features);
                    if(callback) callback(result);
                },
                error:function(){
                    result.inF = [];
                    if(callback) callback(result);
                }
           })
        }
   })
}

//获取全球项目数据统计
function getForeignInfoByName(type,name,callback){
    //district   区
    type = getTsCityLevel(name,type);
    let url = "";
    if(type=="earth"){      //全球
        // let datas = {"code": 0,"data": [{"尼泊尔": "1","赞比亚": "1","沙特阿拉伯": "5","博茨瓦纳": "4","毛里求斯": "11","南苏丹": "2","乌干达": "2","刚果共和国（刚果（布）": "3","安哥拉": "2","莫桑比克": "1","埃塞俄比亚": "4","帕劳": "1","亚美尼亚": "1","加纳": "1","中国": "514","津巴布韦": "2","蒙古": "1","纳米比亚": "3"}],"msg": ""};
        // render.foreignInfoEarth = datas.data[0];
        // if(callback) callback(datas.data[0]);
        url = "/admin-api/globalManage/zjmanage/largescreen/getForeignInfoEarth";
    }else if(type=="country"){      //国家
        // let datas = {"code": 0,"data": [{"Sofala": "3","Abo Dhabi": "4","贵州省": "1","East Shewa": "3","上海市": "12","广东省": "4","湖北省": "4","湖南省": "2","Central": "2","安徽省": "5","四川省": "3","Western": "4","North Western": "2","Black River": "1","Kerewan": "1","江苏省": "160","North": "3","Pool": "1","Bie": "2","Meru": "2","河北省": "1","广西壮族自治区": "2","Southern Highlands": "1","West": "2","江西省": "2","重庆市": "1","Port Louis": "1","北京市": "7","Midlands": "3","Grand Port": "7","山东省": "9","陕西省": "3","浙江省": "5","Greater Accra": "1","Lakes": "5","天津市": "1","Savanne": "1","Mashonaland East": "1","Al Anbar": "9","山西省": "1"}],"msg": ""};
        // if(callback) callback(datas.data[0]);
        url = "/admin-api/globalManage/zjmanage/largescreen/getInnerProviceInfo";
    }else if(type=="province"){     //省份
        // let datas = {"code": 0,"data": [{"苏州市": "32","徐州市": "24","宿迁市": "3","盐城市": "1","扬州市": "13","淮安市": "6","南京市": "60","泰州市": "4","常州市": "4","无锡市": "5","南通市": "2","江苏省": "160","镇江市": "2","连云港市": "4"}],"msg": ""};
        // if(callback) callback(datas.data[0]);
        url = "/admin-api/globalManage/zjmanage/largescreen/getInnerProviceCityInfo?province="+name;
    }else if(type=="city"){     //市
        // let datas = {"code": 0,"data": [{"溧水区": "1","鼓楼区": "3","六合区": "1","秦淮区": "3","玄武区": "35","浦口区": "2","江宁区": "7","栖霞区": "2","建邺区": "1","雨花台区": "5","南京市": "60"}],"msg": ""};
        // if(callback) callback(datas.data[0]);
        url = "/admin-api/globalManage/zjmanage/largescreen/getInnerProviceCityDistrictInfo?city="+name;
    }else{
        if(callback) callback();
    }
    httpAjax(url,function(res){
        // console.log(res,"---------");
        if(res){
            if(type=="earth") render.foreignInfoEarth = res.data[0];
            if(callback) callback(res.data[0]);
        }else{
            if(type=="earth") render.foreignInfoEarth = {};
            if(callback) callback({});
        }
    });
}
function initDemoInfoByName(name,isGw){
    render.control.minDistance = 5;
    getDemoInfos(name,isGw,function(datas){
        for(let i=0;i<datas.length;i++){
            let center = new THREE.Vector3(0,0,0);
            if(datas[i]["项目经度"]&&datas[i]["项目纬度"]){
                center = lonlatToPlanVector3(datas[i]["项目经度"],datas[i]["项目纬度"],render.earthSize);
            }else{
                if(render.currCountrysModel.currPolys){
                    let lnglat = getRandomPointInPoly(render.currCountrysModel.currPolys);
                    center = lonlatToPlanVector3(lnglat[0],lnglat[1],render.earthSize);
                }else{
                    let box = getObjectBox(render.currCountrysModel);
                    center = box.getCenter(center);
                }
            }
            let name = datas[i]["项目名称"] || "";
            let id = datas[i]["ID"] || datas[i]["项目ID"] || "";
            render.setInfoWindow(`<div class="demoBg" code="${id}" onclick="clickDemo(this)" type="project" isGw="${isGw}" titleName="${name}"><div class="demoBg_title">${name}</div><div class="demoBg_zz"></div></div>`,{x:center.x,y:0,z:center.z},{name:name});
            if(render.infoWindos[name]) render.infoWindos[name].isAvoid = true;
        }
    })
    getYdInfos(name,isGw,function(datas){
        // console.log(datas,"----------")
        for(let i=0;i<datas.length;i++){
            let center = new THREE.Vector3(0,0,0);
            if(datas[i]["营地经度"]&&datas[i]["营地纬度"]){
                center = lonlatToPlanVector3(datas[i]["营地经度"],datas[i]["营地纬度"],render.earthSize);
            }else{
                if(render.currCountrysModel.currPolys){
                    let lnglat = getRandomPointInPoly(render.currCountrysModel.currPolys);
                    center = lonlatToPlanVector3(lnglat[0],lnglat[1],render.earthSize);
                }else{
                    let box = getObjectBox(render.currCountrysModel);
                    center = box.getCenter(center);
                }
            }
            let name = datas[i]["营地名称"] || "";
            let id = datas[i]["营地id"] || "";
            render.setInfoWindow(`<div class="demoBg" code="${id}" onclick="clickDemo(this)" type="yd" isGw="${isGw}" titleName="${name}"><div class="demoBg_title">${name}</div><div class="demoBg_zz"></div></div>`,{x:center.x,y:0,z:center.z},{name:name});
            if(render.infoWindos[name]) render.infoWindos[name].isAvoid = true;
        }
    })
}
function getRandomPointInPoly(points){
    // 创建多边形
    const polygon = turf.polygon([points]);
    // 获取多边形的边界框
    const bbox = turf.bbox(polygon);
    const [minX, minY, maxX, maxY] = bbox;
    
    // 生成一个在多边形内的随机点
    function getRandomPointInPolygon(poly) {
        let pt;
        do {
            const x = Math.random() * (maxX - minX) + minX;
            const y = Math.random() * (maxY - minY) + minY;
            pt = turf.point([x, y]);
        } while (!turf.booleanPointInPolygon(pt, poly)); // 检查点是否在多边形内
        return pt;
    }
    
    const randomPoint = getRandomPointInPolygon(polygon)
    return randomPoint.geometry.coordinates;
}
//获取项目信息
function getDemoInfos(name,isGw,callback){
    let url = "/admin-api/globalManage/zjmanage/largescreen/getXmjbxx?district="+name;
    if(isGw) url = "/admin-api/globalManage/zjmanage/largescreen/getXmxxV2?gb="+name;
    httpAjax(url,function(res){
        // console.log(res,"---------");
        if(callback) callback(res.data);
    });
}
//获取营地信息
function getYdInfos(name,isGW,callback){
    let url = "/admin-api/globalManage/zjmanage/largescreen/getYdxxV2?gb="+name;
    if(isGW) url = "/admin-api/globalManage/zjmanage/largescreen/getYdxxV2?gb="+name;
    httpAjax(url,function(res){
        // console.log(res,"---------");
        if(callback) callback(res.data);
    });
}
function clickDemo(obj){
    let code = $(obj).attr("code");
    let type = $(obj).attr("type");
    let name = $(obj).attr("titleName");
    let isGw = $(obj).attr("isGw");
    // console.log(code,"---------",isGw)
    window.parent.postMessage({eve:"clickDemo",type:type,code:code,name:name,isGw:isGw},"*");
}

let httpUrl = "http://222.190.120.19:8088";
// httpUrl = "http://192.168.0.111:48086";
function httpAjax(url,callback){
    $.ajax({
        url: httpUrl+url,
        type: "get",
        headers: {
            "Content-Type": "application/json",
            Authorization: 'Bearer ' + render.token,
            "tenant-id": "1"
        },
        success: function (res) {
            if(callback)callback(res);
        },
        error:function(res){
            if(callback)callback();
        }   
    })
}
function httpAjaxPost(url,data,callback){
    $.ajax({
        url: httpUrl+url,
        type: "POST",
        data:JSON.stringify(data),
        headers: {
            "Content-Type": "application/json",
            // Authorization: 'Bearer ' + render.token,
            // "tenant-id":1
        },
        success: function (res) {
            if(callback)callback(res);
        },
        error:function(res){
            if(callback)callback();
        }   
    })
}

function saveJSON (data, filename){
    if(!data) {
        // alert('保存的数据为空');
        return;
    }
    if(!filename) 
        filename = 'json.json'
    if(typeof data === 'object'){
        data = JSON.stringify(data, undefined, 0)
    }
    var blob = new Blob([data], {type: 'text/json'}),
    e = document.createEvent('MouseEvents'),
    a = document.createElement('a')
    a.download = filename
    a.href = window.URL.createObjectURL(blob)
    a.dataset.downloadurl = ['text/json', a.download, a.href].join(':')
    e.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
    a.dispatchEvent(e)
}