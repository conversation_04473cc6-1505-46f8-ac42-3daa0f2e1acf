// 自动国际化插件
// 这个插件会自动识别常见的中文文本并提供翻译

const commonTexts = {
    // 常用按钮文本
    '确定': { en: 'Confirm', zh: '确定' },
    '取消': { en: 'Cancel', zh: '取消' },
    '保存': { en: 'Save', zh: '保存' },
    '提交': { en: 'Submit', zh: '提交' },
    '重置': { en: 'Reset', zh: '重置' },
    '删除': { en: 'Delete', zh: '删除' },
    '编辑': { en: 'Edit', zh: '编辑' },
    '查看': { en: 'View', zh: '查看' },
    '搜索': { en: 'Search', zh: '搜索' },
    '返回': { en: 'Back', zh: '返回' },
    '关闭': { en: 'Close', zh: '关闭' },

    // 状态文本
    '加载中...': { en: 'Loading...', zh: '加载中...' },
    '暂无数据': { en: 'No Data', zh: '暂无数据' },
    '请稍候': { en: 'Please Wait', zh: '请稍候' },
    '操作成功': { en: 'Success', zh: '操作成功' },
    '操作失败': { en: 'Failed', zh: '操作失败' },

    // 单位
    '个': { en: 'items', zh: '个' },
    '人': { en: 'people', zh: '人' },
    '万元': { en: '10K RMB', zh: '万元' },
    '亿元': { en: 'Billion RMB', zh: '亿元' },
    '%': { en: '%', zh: '%' },

    // 时间相关
    '年': { en: 'Year', zh: '年' },
    '月': { en: 'Month', zh: '月' },
    '日': { en: 'Day', zh: '日' },
    '今日': { en: 'Today', zh: '今日' },
    '本月': { en: 'This Month', zh: '本月' },
    '本年': { en: 'This Year', zh: '本年' },

    // 业务相关
    '项目': { en: 'Project', zh: '项目' },
    '合同': { en: 'Contract', zh: '合同' },
    '金额': { en: 'Amount', zh: '金额' },
    '数量': { en: 'Quantity', zh: '数量' },
    '总计': { en: 'Total', zh: '总计' },
    '详情': { en: 'Details', zh: '详情' },
    '更多': { en: 'More', zh: '更多' }
}

// 自动翻译函数
export function autoTranslate(text, locale = 'zh') {
    if (commonTexts[text]) {
        return commonTexts[text][locale] || text
    }
    return text
}

// Vue 插件
export default {
    install(app, options = {}) {
        // 添加全局属性
        app.config.globalProperties.$autoTranslate = autoTranslate

        // 添加全局方法
        app.provide('autoTranslate', autoTranslate)
    }
} 