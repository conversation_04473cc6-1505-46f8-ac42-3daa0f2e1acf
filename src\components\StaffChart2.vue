<template>
  <div class="chart-container">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { formatNumber } from '@/utils/method'
const chartRef = ref(null)
const chart = ref(null)

// props
const props = defineProps({
  title: {
    type: String,
    default: '总量'
  },
  total: {
    type: Number,
    default: 0
  },
  data: {
    type: Array,
    default: () => []
  }
})

// 创建带间隔的数据数组函数
const createDataWithGaps = (data, isTransparent = false) => {
  const result = []
  // 计算总和
  const total = data.reduce((sum, item) => sum + item.value, 0)
  // 设置固定的间隔比例为2%
  const gapSize = total * 0.02 // 使用总数的2%作为固定间隔大小

  data.forEach(item => {
    // 添加实际数据
    result.push({
      ...item,
      itemStyle: {
        color: isTransparent ?
          item.itemStyle.color.replace('1)', '0.2)') :
          item.itemStyle.color
      }
    })
    // 添加固定大小的间隔
    result.push({
      value: gapSize,
      itemStyle: {
        color: 'rgba(0, 0, 0, 0)',
      },
      label: { show: false },
      labelLine: { show: false }
    })
  })
  return result
}

const initChart = () => {
  if (!chartRef.value) return

  chart.value = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  const originalData = props.data
  
  const option = {
    title: [
      {
        text: "{val|" + formatNumber(props.total) + "}\n{name|" + props.title + "}",
        top: "center",
        left: "center",
        textStyle: {
          rich: {
            name: {
              fontSize: 14,
              fontWeight: "300",
              padding: [10, 0],
              color: '#fff',
            },
            val: {
              fontSize: 32,
              color: '#fff',
              fontFamily: 'Oswald Medium',
            },
          },
        },
      },
    ],
    backgroundColor: 'transparent',
    series: [
      // 外层深色部分
      {
        type: 'pie',
        radius: ['48%', '55%'],
        center: ['50%', '50%'],
        startAngle: -60,
        clockwise: true,
        avoidLabelOverlap: false,
        itemStyle: {
          borderWidth: 0
        },
        emphasis: {
          scale: true,
          scaleSize: 3,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        data: createDataWithGaps(originalData),
        label: {
          show: true,
          lineHeight: 10,
          color: "inherit",
          formatter: function (params) {
            if (params.name !== "") {
              let data = originalData;
              let total = 0;
              let tarValue;
              for (let i = 0; i < data.length; i++) {
                total += data[i].value;
                if (data[i].name == params.name) {
                  tarValue = data[i].value;
                }
              }
              let v = tarValue;
              return [
                `{a|${params.name}}\n` + `{dot|}\n` + `{d|${params.value}}`,
              ];
            } else {
              return "";
            }
          },
          rich: {
            dot: {
              backgroundColor: "auto",
              height: 10,
              width: 10,
              padding: [10, -10, -10, -10],
              borderRadius: 10,
            },
            a: {
              fontSize: 14,
              color: '#ffffff',
              fontWeight: 300,
              fontFamily: 'Regular',
              left: 50,
              padding: [-30, -70, -20, -80],
              width: 30,
            },
            d: {
              fontSize: 20,
              fontWeight: 500,
              color: '#fff',
              fontFamily: 'Oswald Medium',
              width: 30,
              padding: [-5, -60, -20, -80],
              color: '#a1fcd7',
            },
            f: {
              fontSize: 14,
              fontWeight: 300,
              color: '#fff',
              padding: [16, 0, 0, 0],
              // width: 10
            },
          },
        },
        labelLine: {
          show: true,
          length: 30,
          length2: 80,
          smooth: 0,

        },
        emphasis: {
          label: {
            show: true,
            fontSize: "14",
          },
        },
      },
      {
        type: 'pie',
        radius: ['40%', '55%'],
        center: ['50%', '50%'],
        startAngle: -60,
        clockwise: true,
        silent: true,
        itemStyle: {
          borderWidth: 0
        },
        data: createDataWithGaps(originalData, true),
        label: { show: false },
        labelLine: { show: false }
      },
    ]
  }

  chart.value?.setOption(option)
}

const handleResize = () => {
  if (chart.value) {
    chart.value.resize()
  }
}

// 监听 props.data 的变化
watch(() => props.data, (newData) => {
  if (newData && newData.length > 0) {
    updateChart()
  }
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chart.value) {
    chart.value.dispose()
  }
})
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 300px;
  background: rgba(0, 0, 0, 0);
  border-radius: 12px;
  background: url('@/assets/images/zl/top4-img.png') no-repeat center center;
}

.title {
  position: absolute;
  top: 20px;
  left: 20px;
  color: #fff;
  font-size: 24px;
  font-family: 'oswald-medium';
}

.chart {
  width: 100%;
  height: 100%;
}
</style>