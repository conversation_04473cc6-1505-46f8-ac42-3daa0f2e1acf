<template>
  <!-- <div class="w-full"> -->
  <div class="flex home-container justify-center relative z-10">
    <!-- 添加返回按钮 -->
    <img class="pointer-events-auto cursor-pointer absolute top-[10px] left-[60px]"
      src="@/assets/images/xiangmu/back.png" alt="" srcset="" @click="back" style="z-index: 3" />

    <div class="carousel-3d -mt-[50px]">
      <!-- 国际工程卡片 -->
      <div class="carousel-card" :class="{
        'active': activeCardIndex === 0,
        'right': activeCardIndex === 3,
        'left': activeCardIndex === 1 || activeCardIndex === 2,
        'left-far': activeCardIndex === 2,
        'right-far': activeCardIndex === 3
      }" @click="switchCard(0)">
        <div class="rotate-perspective mx-[10px]">
          <div class="mt-[10px] bg1 w-[462px] h-[640px] rotate-item2">
            <div class="w-[462px] h-[610px] py-[15px] box-border rotate-perspective">
              <div class="w-full text-center text-[22px] h-[26px] leading-[16px] item-title">
                {{ $t('business.businessData.internationalEngineering') }}
              </div>
              <div class="w-full flex flex-row flex-wrap flex-btn">
                <template v-for="(item, index) in data.国际工程">
                  <div class="w-1/2 flex flex-col items-center mt-[20px] cursor-pointer"
                    :class="[oneIndex == index ? 'selected-item' : '']" @click="changeOneIndex(index)">
                    <div class="w-full h-[100px] flex items-end ml-[30px]">
                      <RotatingCircleGreen v-if="oneIndex == index" class="w-[111px] h-[100px]"><svg-icon
                          name="assetsJingYing" color="#fff" style="width: 26px;height: 26px;" /></RotatingCircleGreen>
                      <RotatingCircle v-else class="w-[111px] h-[100px]"><svg-icon name="assetsJingYingGreen"
                          color="#fff" style="width: 26px;height: 26px;" /></RotatingCircle>
                      <div class="ml-[6px]">
                        <div class="w-[150px] font-light text-[14px] mt-[-30px] targetEngineer">
                          {{ item.name }}
                        </div>
                        <div :class="[
                          'text-[25px] leading-[40px] font-family-oswald-medium tracking-[1.3px]',
                          oneIndex == index ? 'bgfont' : 'light'
                        ]">
                          {{ formatNumber(item.value) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
              <div class="targetBox">
                <div class="flex justify-between items-center mx-[30px]">
                  <div class="mt-[60px]">
                    <div class="targetFont">{{ $t('business.businessData.target.thisYearTarget') }}<span>（{{
                      $t('business.businessData.units.tenThousandUSD') }}）</span></div>
                    <div class="bgFont">{{ formatNumber(data.国际工程[oneIndex].target) }}</div>
                  </div>
                  <img src="@/assets/images/jingying/completeTheGoal.png" class="mx-4 mt-[60px]"
                    style="margin-left: 0;" />
                  <div class="mt-[60px]">
                    <div class="targetFont">{{ $t('business.businessData.target.completion') }}<span>（{{
                      $t('business.businessData.units.percent') }}）</span></div>
                    <div class="bgFont">{{ data.国际工程[oneIndex].percent }}</div>
                  </div>
                </div>

                <div class="flex justify-between items-center mx-[20px] mt-[30px] h-[120px]">
                  <div class="circle-bg left-data text-center flex flex-col justify-center items-center">
                    <div class="text-[18px]" style="margin-top: 20px;">{{ data.国际工程[oneIndex].oldValue }}</div>
                    <div class="text-[14px] year">{{ $t('business.businessData.target.lastYear') }}({{
                      $t('business.businessData.units.tenThousandUSD') }})</div>
                  </div>

                  <div class="middle-base flex flex-col justify-center items-center">
                    <div class="text-center relative z-10 -top-[30px]">
                      <div class="text-[20px] font-family-oswald-medium"
                        :class="data.国际工程[oneIndex].percent2 < 0 ? 'text-[#d93b35]' : 'text-[#a3d1ea]'">{{
                          data.国际工程[oneIndex].percent2 }}</div>
                      <div class="text-[14px]">{{ $t('business.businessData.target.yearOnYearGrowth') }}({{
                        $t('business.businessData.units.percent') }})</div>
                    </div>
                  </div>

                  <div class="circle-bg-r right-data text-center flex flex-col justify-center items-center">
                    <div class="text-[18px]" style="margin-top: 20px;">{{ data.国际工程[oneIndex].value }}</div>
                    <div class="text-[14px] year">{{ $t('business.businessData.target.thisYear') }}({{
                      $t('business.businessData.units.tenThousandUSD') }})</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 国内工程卡片 -->
      <div class="carousel-card" :class="{
        'active': activeCardIndex === 1,
        'right': activeCardIndex === 0,
        'left': activeCardIndex === 2 || activeCardIndex === 3,
        'left-far': activeCardIndex === 3,
        'right-far': activeCardIndex === 0
      }" @click="switchCard(1)">
        <div class="rotate-perspective mx-[10px]">
          <div class="mt-[10px] bg2 w-[462px] h-[640px] rotate-item2">
            <div class="w-[462px] h-[610px] py-[15px] box-border rotate-perspective">
              <div class="w-full text-center text-[22px] h-[26px] leading-[16px] item-title">
                {{ $t('business.businessData.domesticEngineering') }}
              </div>
              <div class="w-full flex flex-row flex-wrap flex-btn">
                <template v-for="(item, index) in data.国内工程">
                  <div class="w-1/2 flex flex-col items-center mt-[20px] cursor-pointer"
                    :class="[twoIndex == index ? 'selected-item' : '']" @click="changeTwoIndex(index)">
                    <div class="w-full h-[100px] flex items-end ml-[30px]">
                      <RotatingCircleGreen v-if="twoIndex == index" class="w-[111px] h-[100px]"><svg-icon
                          name="assetsJingYing" color="#fff" style="width: 26px;height: 26px;" /></RotatingCircleGreen>
                      <RotatingCircle v-else class="w-[111px] h-[100px]"><svg-icon name="assetsJingYingGreen"
                          color="#fff" style="width: 26px;height: 26px;" /></RotatingCircle>
                      <div class="ml-[6px]">
                        <div class="w-[150px] font-light text-[14px] mt-[-30px] targetEngineer">
                          {{ item.name }}
                        </div>
                        <div :class="[
                          'text-[25px] leading-[40px] font-family-oswald-medium tracking-[1.3px]',
                          twoIndex == index ? 'bgfont' : 'light'
                        ]">
                          {{ formatNumber(item.value) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
              <div class="targetBox">
                <div class="flex justify-between items-center mx-[30px]">
                  <div class="mt-[60px]">
                    <div class="targetFont">{{ $t('business.businessData.target.thisYearTarget') }}<span>（{{
                      $t('business.businessData.units.tenThousandRMB') }}）</span></div>
                    <div class="bgFont">{{ formatNumber(data.国内工程[twoIndex].target) }}</div>
                  </div>
                  <img src="@/assets/images/jingying/completeTheGoal.png" class="mx-4 mt-[60px]"
                    style="margin-left: 0;" />
                  <div class="mt-[60px]">
                    <div class="targetFont">{{ $t('business.businessData.target.completion') }}<span>（{{
                      $t('business.businessData.units.percent') }}）</span></div>
                    <div class="bgFont">{{ data.国内工程[twoIndex].percent }}</div>
                  </div>
                </div>

                <div class="flex justify-between items-center mx-[20px] mt-[30px] h-[120px]">
                  <div class="circle-bg left-data text-center flex flex-col justify-center items-center">
                    <div class="text-[18px]" style="margin-top: 20px;">{{ data.国内工程[twoIndex].oldValue }}</div>
                    <div class="text-[14px] year">{{ $t('business.businessData.target.lastYear') }}({{
                      $t('business.businessData.units.tenThousandRMB') }})</div>
                  </div>

                  <div class="middle-base flex flex-col justify-center items-center">
                    <div class="text-center relative z-10 -top-[30px]">
                      <div class="text-[20px] font-family-oswald-medium text-[#d93b35]">-9.01</div>
                      <div class="text-[14px]">{{ $t('business.businessData.target.yearOnYearGrowth') }}({{
                        $t('business.businessData.units.percent') }})</div>
                    </div>
                  </div>

                  <div class="circle-bg-r right-data text-center flex flex-col justify-center items-center">
                    <div class="text-[18px]" style="margin-top: 20px;">{{ data.国内工程[twoIndex].value }}</div>
                    <div class="text-[14px] year">{{ $t('business.businessData.target.thisYear') }}({{
                      $t('business.businessData.units.tenThousandRMB') }})</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 国际贸易经营卡片 -->
      <div class="carousel-card" :class="{
        'active': activeCardIndex === 2,
        'right': activeCardIndex === 0 || activeCardIndex === 1,
        'right-far': activeCardIndex === 0,
        'left': activeCardIndex === 3,
        'left-far': activeCardIndex === 0
      }" @click="switchCard(2)">
        <div class="rotate-perspective mx-[10px]">
          <div class="mt-[10px] bg3 w-[462px] h-[640px] rotate-item2">
            <div class="w-[462px] h-[610px] py-[15px] box-border rotate-perspective">
              <div class="w-full text-center text-[22px] h-[26px] leading-[16px] item-title">
                {{ $t('business.businessData.internationalTrade') }}
              </div>
              <div class="w-full flex flex-row flex-wrap flex-btn">
                <template v-for="(item, index) in data.贸易经营">
                  <div class="w-1/2 flex flex-col items-center mt-[20px] cursor-pointer"
                    :class="[threeIndex == index ? 'selected-item' : '']" @click="changeThreeIndex(index)">
                    <div class="w-full h-[100px] flex items-end ml-[30px]">
                      <RotatingCircleGreen v-if="threeIndex == index" class="w-[111px] h-[100px]"><svg-icon
                          name="assetsJingYing" color="#fff" style="width: 26px;height: 26px;" /></RotatingCircleGreen>
                      <RotatingCircle v-else class="w-[111px] h-[100px]"><svg-icon name="assetsJingYingGreen"
                          color="#fff" style="width: 26px;height: 26px;" /></RotatingCircle>
                      <div class="ml-[6px]">
                        <div class="w-[150px] font-light text-[14px] mt-[-30px] targetEngineer">
                          {{ item.name }}
                        </div>
                        <div :class="[
                          'text-[25px] leading-[40px] font-family-oswald-medium tracking-[1.3px]',
                          threeIndex == index ? 'bgfont' : 'light'
                        ]">
                          {{ formatNumber(item.value) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
              <div class="targetBox">
                <div class="flex justify-between items-center mx-[30px]">
                  <div class="mt-[60px]">
                    <div class="targetFont">{{ $t('business.businessData.target.thisYearTarget') }}<span>（{{
                      $t('business.businessData.units.tenThousandRMB') }}）</span></div>
                    <div class="bgFont">{{ formatNumber(data.贸易经营[threeIndex].target) }}</div>
                  </div>
                  <img src="@/assets/images/jingying/completeTheGoal.png" class="mx-4 mt-[60px]"
                    style="margin-left: 0;" />
                  <div class="mt-[60px]">
                    <div class="targetFont">{{ $t('business.businessData.target.completion') }}<span>（{{
                      $t('business.businessData.units.percent') }}）</span></div>
                    <div class="bgFont">{{ data.贸易经营[threeIndex].percent }}</div>
                  </div>
                </div>

                <div class="flex justify-between items-center mx-[20px] mt-[30px] h-[120px]">
                  <div class="circle-bg left-data text-center flex flex-col justify-center items-center">
                    <div class="text-[18px]" style="margin-top: 20px;">{{ data.贸易经营[threeIndex].oldValue }}</div>
                    <div class="text-[14px] year">{{ $t('business.businessData.target.lastYear') }}({{
                      $t('business.businessData.units.tenThousandRMB') }})</div>
                  </div>

                  <div class="middle-base flex flex-col justify-center items-center">
                    <div class="text-center relative z-10 -top-[30px]">
                      <div class="text-[20px] font-family-oswald-medium"
                        :class="data.贸易经营[threeIndex].percent2 < 0 ? 'text-[#d93b35]' : 'text-[#a3d1ea]'">{{
                          data.贸易经营[threeIndex].percent2 }}</div>
                      <div class="text-[14px]">{{ $t('business.businessData.target.yearOnYearGrowth') }}({{
                        $t('business.businessData.units.percent') }})</div>
                    </div>
                  </div>

                  <div class="circle-bg-r right-data text-center flex flex-col justify-center items-center">
                    <div class="text-[18px]" style="margin-top: 20px;">{{ data.贸易经营[threeIndex].value }}</div>
                    <div class="text-[14px] year">{{ $t('business.businessData.target.thisYear') }}({{
                      $t('business.businessData.units.tenThousandRMB') }})</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 投资经营卡片 -->
      <div class="carousel-card" :class="{
        'active': activeCardIndex === 3,
        'right': activeCardIndex === 0 || activeCardIndex === 1 || activeCardIndex === 2,
        'right-far': activeCardIndex === 1,
        'right-furthest': activeCardIndex === 0,
        'left': activeCardIndex === 0
      }" @click="switchCard(3)">
        <div class="rotate-perspective mx-[10px]">
          <div class="mt-[10px] bg4 w-[462px] h-[640px] rotate-item2">
            <div class="w-[462px] h-[610px] py-[15px] box-border rotate-perspective">
              <div class="w-full text-center text-[22px] h-[26px] leading-[16px] item-title">
                {{ $t('business.businessData.investmentBusiness') }}
              </div>
              <div class="invBox">
                <div class="inv-container relative"
                  style="background: url('@/assets/images/jingying/invBg.png') no-repeat center / cover;">
                  <div class="inv-row-1 flex items-center">
                    <div class="inv-base relative w-[20%] flex justify-center items-center">
                      <img src="@/assets/images/jingying/invBase.png" class="w-full" alt="底座" />
                      <svg-icon name="assetsJingYing" color="#fff" class="absolute"
                        style="width: 30px; height: 30px; z-index: 1;margin-left: 3px;" />
                    </div>
                    <div class="inv-right relative flex-1 flex items-center">
                      <img src="@/assets/images/jingying/invRight.png" class="w-full h-full" alt="右侧背景" />
                      <div class="absolute left-[20px] text-white flex items-center">
                        <div class="text-[14px] text-[#B2C7DE]">{{ $t('business.businessData.metrics.operatingRevenue')
                        }}：
                        </div>
                        <div class="text-[22px] font-family-oswald-medium absolute -right-[190px] fontYellow">
                          54,321<span class="text-[14px] text-[#B2C7DE] ml-[5px]">({{
                            $t('business.businessData.units.tenThousandRMB') }})</span></div>
                      </div>
                    </div>
                  </div>
                  <div class="inv-row-2 flex justify-between mt-[15px]">
                    <div class="inv-percent w-[48%] relative">
                      <div class="absolute -left-[80px] inset-0 flex flex-col justify-center items-center">
                        <div class="text-[14px] text-[#B2C7DE]">{{ $t('business.businessData.target.completion') }}（{{
                          $t('business.businessData.units.percent') }}）</div>
                        <div class="text-[22px] font-family-oswald-medium bgfont">91.01</div>
                      </div>
                    </div>
                    <div class="inv-growth -right-[50px] w-[48%] relative">
                      <div class="absolute inset-0 flex flex-col justify-center items-center">
                        <div class="text-[14px] text-[#B2C7DE]">{{ $t('business.businessData.target.yearOnYearGrowth')
                        }}（{{
                            $t('business.businessData.units.percent') }}）</div>
                        <div class="text-[22px] font-family-oswald-medium fontRed">-33.33</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bgBottom"></div>
                <div class="inv-container relative"
                  style="background: url('@/assets/images/jingying/invBg.png') no-repeat center / cover;margin-top: 10px;">
                  <div class="inv-row-1 flex items-center">
                    <div class="inv-base relative w-[20%] flex justify-center items-center">
                      <img src="@/assets/images/jingying/invBase.png" class="w-full" alt="底座" />
                      <svg-icon name="dollar" color="#fff" class="absolute"
                        style="width: 30px; height: 30px; z-index: 1;margin-left: 3px;" />
                    </div>
                    <div class="inv-right relative flex-1 flex items-center">
                      <img src="@/assets/images/jingying/invRight.png" class="w-full h-full" alt="右侧背景" />
                      <div class="absolute left-[20px] text-white flex items-center">
                        <div class="text-[14px] text-[#B2C7DE]">{{ $t('business.businessData.metrics.totalProfit') }}：
                        </div>
                        <div class="text-[22px] font-family-oswald-medium absolute -right-[190px] fontYellow">
                          54,321<span class="text-[14px] text-[#B2C7DE] ml-[5px]">({{
                            $t('business.businessData.units.tenThousandRMB') }})</span></div>
                      </div>
                    </div>
                  </div>
                  <div class="inv-row-2 flex justify-between mt-[15px]">
                    <div class="inv-percent w-[48%] relative">
                      <div class="absolute -left-[80px] inset-0 flex flex-col justify-center items-center">
                        <div class="text-[14px] text-[#B2C7DE]">{{ $t('business.businessData.target.completion') }}（{{
                          $t('business.businessData.units.percent') }}）</div>
                        <div class="text-[22px] font-family-oswald-medium bgfont">91.01</div>
                      </div>
                    </div>
                    <div class="inv-growth -right-[50px] w-[48%] relative">
                      <div class="absolute inset-0 flex flex-col justify-center items-center">
                        <div class="text-[14px] text-[#B2C7DE]">{{ $t('business.businessData.target.yearOnYearGrowth')
                        }}（{{
                            $t('business.businessData.units.percent') }}）</div>
                        <div class="text-[22px] font-family-oswald-medium fontRed">-33.33</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bgBottom"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="carousel-controls" style="display: none;">
        <div class="carousel-dot" :class="{ 'active': activeCardIndex === 0 }" @click="switchCard(0)"></div>
        <div class="carousel-dot" :class="{ 'active': activeCardIndex === 1 }" @click="switchCard(1)"></div>
        <div class="carousel-dot" :class="{ 'active': activeCardIndex === 2 }" @click="switchCard(2)"></div>
        <div class="carousel-dot" :class="{ 'active': activeCardIndex === 3 }" @click="switchCard(3)"></div>
      </div>

      <div class="base-navigation">
        <img src="@/assets/images/jingying/jingyingBase.png" @click="handleBaseClick" alt="底部导航" />
      </div>
    </div>
  </div>
  <!-- </div> -->

  <el-dialog v-model="dialogTableVisible" style="pointer-events: all" :modal="false"
    :title="$t('business.businessData.dialog.title')" width="800" @close="dialogTableVisible = false">
    <el-table :data="gridData">
      <el-table-column property="name" :label="$t('business.businessData.dialog.companyHeader')" width="150" />
      <el-table-column property="project" :label="$t('business.businessData.dialog.projectHeader')" />
      <el-table-column property="hetong" :label="$t('business.businessData.dialog.contractHeader')" width="200" />
    </el-table>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import { formatNumber } from "@/utils/method";
import Circular from "@/components/Circular.vue";
import axios from "axios";
import RotatingCircle from '@/components/RotatingCircle.vue';
import RotatingCircleGreen from '@/components/RotatingCircleGreen.vue';
import img1 from "@/assets/images/jingying/img1.png";
import img2 from "@/assets/images/jingying/img2.png";
import img3 from "@/assets/images/jingying/img3.png";
import img4 from "@/assets/images/jingying/img4.png";
import img9 from "@/assets/images/jingying/img9.png";
import img10 from "@/assets/images/jingying/img10.png";
import img11 from "@/assets/images/jingying/img11.png";
import request from '@/utils/request'
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

const dialogTableVisible = ref(false);
const activeCardIndex = ref(1);
let carouselTimer = null;

const router = useRouter();
const { t, locale } = useI18n();

// 定期切换卡片的函数
function startCarouselTimer() {
  carouselTimer = setInterval(() => {
    activeCardIndex.value = (activeCardIndex.value + 1) % 4; // 现在有4张卡片
  }, 10000); // 每10秒切换一次
}

// 手动切换卡片
function switchCard(index) {
  if (activeCardIndex.value !== index) {
    activeCardIndex.value = index;

    // 重置自动切换计时器
    clearInterval(carouselTimer);
    //   startCarouselTimer();
  }
}

// 在组件卸载时清除定时器
onUnmounted(() => {
  clearInterval(carouselTimer);
});

const gridData = ref([
  {
    name: "海三",
    project: "刚果-喀麦隆国际大学一期",
    hetong: 5256,
  },
  {
    name: "海二",
    project: "博茨瓦纳14个社区警察局及配套住房项目",
    hetong: 5086,
  },
  {
    name: "海二",
    project: "沙特利雅得塞德拉三四五期、利雅得东项目预制厂建设工程项目",
    hetong: 4400,
  },
  {
    name: "海三",
    project: "埃塞Chaka商业购物中心",
    hetong: 4289,
  },
  {
    name: "海三",
    project: "埃塞曼森阿拉达豪华购物中心",
    hetong: 3224,
  },
  {
    name: "海二",
    project: "赞比亚MANSA机场跑道等升级设计施工项目",
    hetong: 2950,
  },
  {
    name: "海一",
    project: "南苏丹为GPOC油田提供注水和水处理设备及安装服务",
    hetong: 2409,
  },
  {
    name: "海一",
    project: "援亚美尼亚国家公共电视台演播大厅项目",
    hetong: 2346,
  },
  {
    name: "毛里求斯",
    project: "毛里求斯电子城交易广场办公楼二期项目",
    hetong: 1603,
  },
  {
    name: "建设集团",
    project: "新加坡地铁CR116(增加)",
    hetong: 1068,
  },
  {
    name: "巴新",
    project: "汤加亚行防洪项目",
    hetong: 624,
  },
]);

const yearData = ref({
  营收: 0,
  利润: 0
});

function getGroupOperat() {
  request.get('/globalManage/group/T-group-business/page', {
    params: {
      pageSize: 50
    }
  })
    .then((res) => {
      if (res.code === 0 && res.data.list.length > 0) {
        try {
          const apiData = res.data.list;
          const formattedData = {
            国际工程: [],
            国内工程: [],
            贸易经营: [],
            投资经营: [],
          };

          // 获取2024年的数据
          const year2024Data = apiData.filter(item => item.year === "2024");

          year2024Data.forEach(item => {
            // 处理年度营收和利润数据
            if (!item.businessType) {
              if (item.businessName === '年度营收') {
                yearData.value.营收 = item.thisyearAmount;
              }
              if (item.businessName === '年度利润') {
                yearData.value.利润 = item.thisyearAmount;
              }
              return;
            }

            if (item.businessType) {
              const dataItem = {
                name: `${item.businessName}（${item.unit}）`,
                img: getImageByBusinessName(item.businessName),
                oldValue: Number(item.lastyearAmount) || 0,
                value: item.thisyearAmount,
                target: item.target || 0,
                percent: item.completionRate || 0,
                percent2: item.grow || 0,
              };

              formattedData[item.businessType].push(dataItem);
            }
          });

          data.value = formattedData;
          console.log('处理后的数据:', data.value);

        } catch (err) {
          console.error('数据处理错误:', err);
        }
      }
    })
    .catch((err) => {
      console.error("请求失败:", err);
    });
}

// 根据业务名称获取对应的图片
function getImageByBusinessName(name) {
  switch (name) {
    case '新签合同额':
      return img1;
    case '国际产值':
    case '建筑业产值':
      return img2;
    case '营业收入':
      return img3;
    case '利润总额':
      return img4;
    case '进口额':
    case '出口额':
      return img9;
    case '营业收入':
      return img10;
    case '利润总额':
      return img11;
    default:
      return img1;
  }
}

const rawData = ref({
  国际工程: [
    {
      type: 'newContractAmount',
      img: img1,
      oldValue: 46528,
      value: 65558,
      target: 60896,
      percent: 107.66,
      percent2: 40.9,
      unit: 'tenThousandUSD'
    },
    {
      type: 'internationalOutput',
      img: img2,
      oldValue: 49329,
      value: 46399,
      target: 52800,
      percent: 87.88,
      percent2: -5.94,
      unit: 'tenThousandUSD'
    },
    {
      type: 'operatingRevenue',
      img: img3,
      oldValue: 334354,
      value: 314496,
      target: 324116,
      percent: 97.03,
      percent2: -5.94,
      unit: 'tenThousandRMB'
    },
    {
      type: 'totalProfit',
      img: img4,
      oldValue: 21410,
      value: 15088,
      target: 15680,
      percent: 96.22,
      percent2: -29.53,
      unit: 'tenThousandRMB'
    },
  ],
  国内工程: [
    {
      type: 'newContractAmount',
      img: img1,
      oldValue: 1142709,
      value: 1045590,
      target: 1265730,
      percent: 82.61,
      percent2: -8.5,
      unit: 'tenThousandRMB'
    },
    {
      type: 'constructionOutput',
      img: img2,
      oldValue: 1053314,
      value: 1096009,
      target: 1158009,
      percent: 94.65,
      percent2: 4.05,
      unit: 'tenThousandRMB'
    },
    {
      type: 'operatingRevenue',
      img: img3,
      oldValue: 1035141,
      value: 998729,
      target: 1138349,
      percent: 87.73,
      percent2: -3.52,
      unit: 'tenThousandRMB'
    },
    {
      type: 'totalProfit',
      img: img4,
      oldValue: 7949,
      value: 7232,
      target: 11226,
      percent: 64.42,
      percent2: -9.01,
      unit: 'tenThousandRMB'
    },
  ],
  贸易经营: [
    {
      type: 'operatingRevenue',
      img: img10,
      oldValue: 81905,
      value: 53412,
      target: 64434,
      percent: 82.89,
      percent2: -34.79,
      unit: 'tenThousandRMB'
    },
    {
      type: 'totalProfit',
      img: img11,
      oldValue: 8090,
      value: 7383,
      target: 8112,
      percent: 91.01,
      percent2: -8.72,
      unit: 'tenThousandRMB'
    },
  ],
  投资经营: [
    {
      type: 'operatingRevenue',
      img: img10,
      oldValue: 81905,
      value: 53412,
      target: 64434,
      percent: 82.89,
      percent2: -34.79,
      unit: 'tenThousandRMB'
    },
    {
      type: 'totalProfit',
      img: img11,
      oldValue: 8090,
      value: 7383,
      target: 8112,
      percent: 91.01,
      percent2: -8.72,
      unit: 'tenThousandRMB'
    },
  ],
});

// 计算属性，实现动态翻译
const data = computed(() => {
  const result = {};

  Object.keys(rawData.value).forEach(category => {
    result[category] = rawData.value[category].map(item => ({
      ...item,
      name: `${t(`business.businessData.metrics.${item.type}`)}（${t(`business.businessData.units.${item.unit}`)}）`
    }));
  });

  return result;
});

const list1 = computed(() => {
  return [
    {
      name: t('business.businessData.target.lastYear'),
      value: data.value.国际工程[oneIndex.value].oldValue,
    },
    {
      name: t('business.businessData.target.thisYear'),
      value: data.value.国际工程[oneIndex.value].value,
    },
  ];
});

const list2 = computed(() => {
  return [
    {
      name: t('business.businessData.target.lastYear'),
      value: data.value.国内工程[twoIndex.value].oldValue,
    },
    {
      name: t('business.businessData.target.thisYear'),
      value: data.value.国内工程[twoIndex.value].value,
    },
  ];
});

const handleClick = (type, index) => {
  console.log(type, index);

  if (type == 1 && index == 0) {
    dialogTableVisible.value = true;
  }
};

function createTimerManager(indexRef, getList) {
  let timer = null;
  let debounceTimer = null;
  function startTimer() {
    timer = setInterval(() => {
      indexRef.value++;
      if (indexRef.value >= getList().length) {
        indexRef.value = 0;
      }
    }, 10000);
  }
  startTimer();
  return {
    changeIndex(newIndex) {
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        indexRef.value = newIndex;
        clearInterval(timer);
        startTimer();
      }, 300);
    },
    destroy() {
      clearInterval(timer);
      clearTimeout(debounceTimer);
    },
  };
}

const oneIndex = ref(0);
const twoIndex = ref(0);
const internationalManager = createTimerManager(
  oneIndex,
  () => data.value.国际工程
);
const domesticManager = createTimerManager(
  twoIndex,
  () => data.value.国内工程
);

function changeOneIndex(index) {
  internationalManager.changeIndex(index);
}

function changeTwoIndex(index) {
  domesticManager.changeIndex(index);
}

// 新增索引变量
const threeIndex = ref(0);
const fourIndex = ref(0);

// 新增贸易经营和投资经营管理器
const tradeManager = createTimerManager(
  threeIndex,
  () => data.value.贸易经营
);
const investmentManager = createTimerManager(
  fourIndex,
  () => data.value.投资经营
);

function changeThreeIndex(index) {
  tradeManager.changeIndex(index);
}

function changeFourIndex(index) {
  investmentManager.changeIndex(index);
}

// 新增计算属性
const list3 = computed(() => {
  return [
    {
      name: t('business.businessData.target.lastYear'),
      value: data.value.贸易经营[threeIndex.value].oldValue,
    },
    {
      name: t('business.businessData.target.thisYear'),
      value: data.value.贸易经营[threeIndex.value].value,
    },
  ];
});

const list4 = computed(() => {
  return [
    {
      name: t('business.businessData.target.lastYear'),
      value: data.value.投资经营[fourIndex.value].oldValue || 0,
    },
    {
      name: t('business.businessData.target.thisYear'),
      value: data.value.投资经营[fourIndex.value].value,
    },
  ];
});

// 在组件卸载时清除所有定时器
onUnmounted(() => {
  internationalManager.destroy();
  domesticManager.destroy();
  tradeManager.destroy();
  investmentManager.destroy();
});

onMounted(() => {
  getGroupOperat();
});

// 处理底部导航图片点击
function handleBaseClick() {
  // 切换到下一张卡片
  activeCardIndex.value = (activeCardIndex.value + 1) % 4;

  // 重置自动切换计时器
  clearInterval(carouselTimer);
  // startCarouselTimer(); // 如果需要重新启动自动切换，取消注释
}

// 添加返回函数
const back = () => {
  router.replace("/business/index");
};

// 添加语言切换函数
function toggleLanguage() {
  const currentLang = locale.value;
  const newLang = currentLang === 'zh' ? 'en' : 'zh';
  locale.value = newLang;
  localStorage.setItem('language', newLang);
}
</script>

<style lang="scss" scoped>
.home-container {
  pointer-events: all;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/images/jingying/jingyingBg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 20px;

  .bg1 {
    background: url("@/assets/images/jingying/assetsBg.png") no-repeat center center / 100% 100%;
  }

  .bg2 {
    background: url("@/assets/images/jingying/assetsBg.png") no-repeat center center / 100% 100%;
  }

  .carousel-3d {
    position: relative;
    width: 1000px;
    height: 700px;
    perspective: 2000px;
    display: flex;
    justify-content: center;
    align-items: center;

    .carousel-card {
      position: absolute;
      transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
      cursor: pointer;
      transform-style: preserve-3d;

      &.active {
        z-index: 10;
        transform: translateZ(100px) scale(1.05);
        filter: brightness(1.1);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
      }

      &.left {
        transform: translateX(-500px) translateZ(0) rotateY(-20deg);
        filter: brightness(0.8) blur(1px);
        opacity: 0.9;
        z-index: 5;
      }

      &.right {
        transform: translateX(500px) translateZ(0) rotateY(20deg);
        filter: brightness(0.8) blur(1px);
        opacity: 0.9;
        z-index: 5;
      }

      &.right-far {
        transform: translateX(650px) translateZ(-100px) rotateY(30deg);
        filter: brightness(0.7) blur(2px);
        opacity: 0.7;
        z-index: 4;
      }

      &.right-furthest {
        transform: translateX(800px) translateZ(-200px) rotateY(40deg);
        filter: brightness(0.6) blur(3px);
        opacity: 0.5;
        z-index: 3;
      }

      &.left-far {
        transform: translateX(-650px) translateZ(-100px) rotateY(-30deg);
        filter: brightness(0.7) blur(2px);
        opacity: 0.7;
        z-index: 4;
      }

      &.left-furthest {
        transform: translateX(-800px) translateZ(-200px) rotateY(-40deg);
        filter: brightness(0.6) blur(3px);
        opacity: 0.5;
        z-index: 3;
      }
    }

    .carousel-controls {
      position: absolute;
      bottom: -40px;
      display: flex;
      justify-content: center;
      gap: 15px;
      z-index: 20;

      .carousel-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background-color: #fff;
          transform: scale(1.2);
        }
      }
    }
  }

  .icons {
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: -120px;
    left: -50px;
    width: 480px;
    height: 100px;

    .icon-item {
      display: flex;
      width: 237px;
      height: 100px;

      .icon {
        width: 113px;
        height: 100px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      >div:last-child {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        padding-left: 10px;

        span {
          color: #a3d2eb;
          font-family: Oswald Medium;
          font-size: 22px;
          margin-right: 5px;
        }

        .text {
          line-height: 30px;
        }
      }
    }
  }

  .rotate-perspective {
    perspective: 800px;

    .item-title {
      width: 440px;
      height: 40px;
      margin-top: 20px;
      line-height: 40px;
      font-family: Alibaba PuHuiTi 2.0;
      font-weight: 600;
    }

    .line-title {
      width: 400px;
      height: 30px;
      box-sizing: border-box;
      margin: 20px auto;
      padding: 0 20px;
      background: url("@/assets/images/jingying/200.png") no-repeat left center / 220px 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-family: Oswald Medium;
        font-size: 25px;
        color: #b3e5ff;
      }
    }

    .bottom {
      width: 400px;
      margin: 20px auto;
      display: flex;

      .bottom-left {
        width: 140px;
        height: 100px;

        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .bottom-left-title {
          width: 140px;
          height: 30px;
          background: url("@/assets/images/jingying/207.png") no-repeat center center / 100% 100%;
          box-sizing: border-box;
          padding-left: 20px;
          line-height: 30px;
        }

        .bottom-left-num {
          text-align: center;
          font-family: Oswald Medium;
          font-size: 25px;
          color: #b3e5ff;
        }
      }

      .bottom-right {
        width: 260px;
        height: 200px;
      }
    }

    .rotate-item1 {
      .item-title {
        transform: rotate(2deg);
      }
    }

    .rotate-item1,
    .rotate-item2 {
      .flex-btn {
        .active {
          // 这里可以清空或删除原来的样式
        }
      }
    }
  }

  .sub-font {
    color: #d93b35;
  }

  .sub-font-bg {
    background: linear-gradient(to right, #ee8035, #d93b35);
  }

  .plus-font {
    color: #a3d1ea;
  }

  .plus-font-bg {
    background: linear-gradient(to right, #0082ff, #00b6ff);
  }

  .jindu-container-red {
    background: linear-gradient(to right, #ee8035, #d93b35);
  }

  .jindu-container-green {
    background: linear-gradient(to right, #0082ff, #00b6ff);
  }

  .linear {
    background-image: linear-gradient(to bottom, rgba(154, 196, 255, 0) 0%, #FFFFFF 62%, #9AC4FF 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .targetBox {
    width: 100%;
    height: 55%;
    background: url("@/assets/images/jingying/assetsSmall.png") no-repeat center;

    .targetFont {
      font-family: PingFang SC, PingFang SC;
      font-size: 16px;
      color: #F3F7FF;
      line-height: 19px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      padding-bottom: 10px;

      span {
        font-family: PingFang SC, PingFang SC;
        font-size: 14px;
        color: #B2C7DE;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .bgFont {
      font-size: 28px;
      font-weight: bold;
      background-image: linear-gradient(to bottom,
          #9AC4FF 0%,
          #FFFFFF 62%,
          #9AC4FF 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .circle-bg {
      width: 50%;
      height: 140px;
      background: url("@/assets/images/jingying/circle.png") no-repeat center;
      padding-bottom: 20px;
      font-family: TCloudNumber;
      font-weight: 300;
      font-size: 20px;
      color: #DBE9FF;
      line-height: 23px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    .circle-bg-r {
      width: 50%;
      height: 140px;
      background: url("@/assets/images/jingying/circleR.png") no-repeat center;
      padding-bottom: 20px;
      font-family: TCloudNumber;
      font-weight: 300;
      font-size: 20px;
      color: #DBE9FF;
      line-height: 23px;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    .middle-base {
      width: 380px;
      height: 120px;
      background: url("@/assets/images/jingying/base.png") no-repeat center center / 200px auto;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1;
    }

    .year {
      font-family: PingFang SC, PingFang SC;
      font-size: 14px;
      color: #F3F7FF;
      line-height: 19px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      font-weight: 100;
    }
  }
}

.bgfont {
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 50%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

// 添加选中项的样式
.selected-item {
  position: relative;

  &::after {
    content: "";
    width: 183px;
    height: 85px;
    position: absolute;
    bottom: -5px;
    right: -10px;
    background: url("@/assets/images/jingying/selectedBg.png") no-repeat center center / 100% 100%;
    z-index: -1;
  }
}

// 移除原来的active样式
.rotate-item1,
.rotate-item2 {
  .flex-btn {
    .active {
      // 这里可以清空或删除原来的样式
    }
  }
}

.bg3 {
  background: url("@/assets/images/jingying/assetsBg.png") no-repeat center center / 100% 100%;
}

.bg4 {
  background: url("@/assets/images/jingying/assetsBg.png") no-repeat center center / 100% 100%;
}

/* 投资经营盒子样式 */
.invBox {
  width: 100%;
  margin-top: 25px;
  padding: 0 20px;
  margin-bottom: 15px;

  .inv-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    padding: 20px 20px 0 20px;
  }

  .bgBottom {
    height: 80px;
    width: 100%;
    background: url("@/assets/images/jingying/invBg.png")no-repeat center bottom / 100% 100%;
  }

  .inv-row-2 {
    height: 60px;
    margin-top: 10px;
    background: url("@/assets/images/jingying/invBottom.png")no-repeat center center / 100% 100%;
    width: 100%;
  }

  .fontYellow {
    background-image: linear-gradient(to bottom,
        #FFE49A 0%,
        #FFFFFF 50%,
        #FFE49A 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .fontRed {
    background-image: linear-gradient(to bottom,
        #FE6263 0%,
        #FFFFFF 50%,
        #FE6263 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }
}

.base-navigation {
  position: absolute;
  bottom: -185px;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 20;

  img {
    cursor: pointer;
    width: 60%;
    max-width: 800px;
  }
}
</style>
