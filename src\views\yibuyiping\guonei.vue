<template>
    <div class="guonei-container" :style="{ backgroundImage: `url(${currentImage})` }">
        <!-- 返回按钮 -->
        <!-- <img class="cursor-pointer absolute -top-[6px] left-[60px]" src="@/assets/images/xiangmu/back.png" alt=""
            @click="back" style="z-index: 3" /> -->
            <div class="back-button" @click="back">
            <img src="@/assets/images/header/back.png" alt="" />
            <span>返回</span>
        </div>
        <!-- 锚点1：上面中间部分 - 只在主页面显示 -->
        <div v-if="currentView === 'main'" class="anchor anchor-1" @click="handleAnchor1Click"></div>

        <!-- 锚点2：上面右边部分 - 只在主页面显示 -->
        <div v-if="currentView === 'main'" class="anchor anchor-2" @click="handleAnchor2Click"></div>
        <!-- 锚点3：只在国内工程主页面显示 -->
        <div v-if="currentView === 'main' && currentMode === 'gongcheng'" class="anchor anchor-3"
            @click="handleAnchor3Click"></div>

        <!-- 投资服务页面的专用锚点 -->
        <div v-if="currentMode === 'touzi' && currentView === 'main'" class="anchor anchor-touzi-special"
            @click="handleTouziSpecialClick"></div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

// 引入图片资源
import gongchengMain from '@/assets/images/yibuyiping/一部一屏-国内事业部-国内工程.png';
import gongchengMore from '@/assets/images/yibuyiping/一部一屏-国内事业部-国内工程-查看更多.png';
import touziMain from '@/assets/images/yibuyiping/一部一屏-国内事业部-投资服务.png';
import touziMore from '@/assets/images/yibuyiping/一部一屏-国内事业部-投资服务-查看更多.png';
import yiliwulv from '@/assets/images/yibuyiping/一利五率.png';

const router = useRouter();

// 当前显示的模式：'gongcheng' | 'touzi'
const currentMode = ref('gongcheng');

// 当前显示的视图：'main' | 'more' | 'yiliwulv'
const currentView = ref('main');

// 计算当前应该显示的图片
const currentImage = computed(() => {
    if (currentView.value === 'yiliwulv') {
        return yiliwulv;
    }
    if (currentMode.value === 'gongcheng') {
        return currentView.value === 'main' ? gongchengMain : gongchengMore;
    } else {
        return currentView.value === 'main' ? touziMain : touziMore;
    }
});

// 锚点1点击处理：切换模式
const handleAnchor1Click = () => {
    console.log('锚点1点击');
    if (currentMode.value === 'gongcheng') {
        // 从国内工程切换到投资服务
        currentMode.value = 'touzi';
    } else {
        // 从投资服务切换回国内工程
        currentMode.value = 'gongcheng';
    }
    currentView.value = 'main'; // 重置到主视图
};

// 锚点2点击处理：查看更多
const handleAnchor2Click = () => {
    console.log('锚点2点击');
    currentView.value = 'more';
};

// 锚点3点击处理：显示一利五率
const handleAnchor3Click = () => {
    console.log('锚点3点击 - 显示一利五率');
    currentView.value = 'yiliwulv';
};

// 投资服务页面的专用锚点点击处理
const handleTouziSpecialClick = () => {
    console.log('投资服务页面的专用锚点点击');
    currentView.value = 'more';
};

// 返回按钮
const back = () => {
    if (currentView.value === 'more' || currentView.value === 'yiliwulv') {
        // 如果当前是详情页面或一利五率页面，返回到主页面（保持当前模式）
        currentView.value = 'main';
    } else {
        // 如果当前是主页面，返回到主页面列表
        router.push({ name: 'yibuyipingIndex' });
    }
};
</script>

<style scoped lang="scss">
.back-button {
    position: absolute;
    top: -6px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
}
.guonei-container {
    pointer-events: all;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    position: absolute;
    top: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.anchor {
    position: absolute;
    cursor: pointer;
    z-index: 10;
}

.anchor-1 {
    // 锚点1：上面中间部分
    width: 300px;
    height: 50px;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
}

.anchor-2 {
    // 锚点2：上面右边部分
    width: 130px;
    height: 60px;
    top: 10%;
    right: 8%;
}

.anchor-3 {
    // 锚点2：上面右边部分
    width: 130px;
    height: 60px;
    top: 10%;
    right: 15%;
}

.anchor-touzi-special {
    // 投资服务页面的专用锚点
    width: 150px;
    height: 60px;
    top: 10%;
    right: 2%;
}
</style>