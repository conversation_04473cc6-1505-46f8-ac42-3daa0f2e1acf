import * as echarts from 'echarts'

/**
 * 图表实例管理器
 * 用于统一管理ECharts实例，避免内存泄漏和runtime错误
 */
export class ChartManager {
    constructor() {
        this.charts = new Map()
        this.resizeHandlers = new Map()
    }

    /**
     * 创建图表实例
     * @param {string} id - 图表唯一标识
     * @param {HTMLElement} container - 容器元素
     * @param {Object} option - 图表配置
     * @returns {Object} ECharts实例
     */
    createChart(id, container, option) {
        try {
            // 如果已存在同ID的图表，先销毁
            if (this.charts.has(id)) {
                this.disposeChart(id)
            }

            const chart = echarts.init(container)
            chart.setOption(option)

            // 创建resize处理器
            const resizeHandler = () => {
                if (chart && !chart.isDisposed()) {
                    chart.resize()
                }
            }

            // 添加resize事件监听
            window.addEventListener('resize', resizeHandler)

            // 存储实例和处理器
            this.charts.set(id, chart)
            this.resizeHandlers.set(id, resizeHandler)

            return chart
        } catch (error) {
            console.error(`创建图表${id}失败:`, error)
            return null
        }
    }

    /**
     * 获取图表实例
     * @param {string} id - 图表ID
     * @returns {Object|null} ECharts实例
     */
    getChart(id) {
        return this.charts.get(id) || null
    }

    /**
     * 更新图表配置
     * @param {string} id - 图表ID
     * @param {Object} option - 新的配置
     */
    updateChart(id, option) {
        const chart = this.charts.get(id)
        if (chart && !chart.isDisposed()) {
            try {
                chart.setOption(option)
            } catch (error) {
                console.error(`更新图表${id}失败:`, error)
            }
        }
    }

    /**
     * 销毁指定图表
     * @param {string} id - 图表ID
     */
    disposeChart(id) {
        try {
            const chart = this.charts.get(id)
            const resizeHandler = this.resizeHandlers.get(id)

            if (resizeHandler) {
                window.removeEventListener('resize', resizeHandler)
                this.resizeHandlers.delete(id)
            }

            if (chart && !chart.isDisposed()) {
                chart.dispose()
            }

            this.charts.delete(id)
        } catch (error) {
            console.warn(`销毁图表${id}时出错:`, error)
        }
    }

    /**
     * 销毁所有图表
     */
    disposeAll() {
        const chartIds = Array.from(this.charts.keys())
        chartIds.forEach(id => {
            this.disposeChart(id)
        })
    }

    /**
     * 检查图表是否存在且可用
     * @param {string} id - 图表ID
     * @returns {boolean}
     */
    isChartValid(id) {
        const chart = this.charts.get(id)
        return chart && !chart.isDisposed()
    }
}

/**
 * 创建一个用于Vue组件的图表管理Hook
 * @returns {Object} 包含管理器实例和清理函数的对象
 */
export function useChartManager() {
    const manager = new ChartManager()

    const cleanup = () => {
        manager.disposeAll()
    }

    return {
        manager,
        cleanup
    }
}

/**
 * 安全地初始化ECharts实例
 * @param {HTMLElement} container - 容器元素
 * @param {Object} option - 图表配置
 * @param {Function} fallback - 失败时的回调
 * @returns {Object|null} ECharts实例或null
 */
export function safeInitChart(container, option, fallback = null) {
    try {
        if (!container) {
            console.warn('图表容器不存在')
            return null
        }

        const chart = echarts.init(container)
        chart.setOption(option)
        return chart
    } catch (error) {
        console.error('图表初始化失败:', error)
        if (fallback && typeof fallback === 'function') {
            fallback(error)
        }
        return null
    }
}

/**
 * 安全地销毁ECharts实例
 * @param {Object} chart - ECharts实例
 */
export function safeDisposeChart(chart) {
    if (!chart) return

    try {
        if (!chart.isDisposed()) {
            chart.dispose()
        }
    } catch (error) {
        console.warn('销毁图表时出错:', error)
    }
}

/**
 * 防抖的resize处理器
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间，默认300ms
 * @returns {Function} 防抖后的函数
 */
export function debounceResize(callback, delay = 300) {
    let timeoutId = null

    return function (...args) {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => {
            callback.apply(this, args)
        }, delay)
    }
} 