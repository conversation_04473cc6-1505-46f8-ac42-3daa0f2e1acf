export default {
    // Navigation menu
    nav: {
        businessLayout: 'Business Distribution',
        groupManagement: 'Group Operations',
        groupAssets: 'Group Assets',
        oneOfficeOneScreen: 'Department Output Value',
        onSiteVideo: 'Live Video',
        oneProfitFiveRates: 'Interest Rate Statistics'
    },

    // User interface
    user: {
        admin: 'Admin',
        logout: 'Log Out',
        logoutConfirm: 'Confirm to log out?',
        confirm: 'Confirm',
        cancel: 'Cancel',
        warning: 'Prompt'
    },

    // Language switching
    language: {
        chinese: '中文',
        english: 'English'
    },

    // Login page
    login: {
        title: '中江国际全球经营管理数字化平台',
        subtitle: '- CJI Business Management Digital Platform -',
        username: 'Please enter your username.',
        password: 'Please enter your password.',
        captcha: 'Please enter the verification code.',
        rememberPassword: 'Remember password',
        forgotPassword: 'Forget password?',
        loginButton: 'Log In',
        loginSuccess: 'Success',
        loginFailed: 'Failed',
        usernameRequired: 'Please enter your username.',
        passwordRequired: 'Please enter your password.',
        captcha<PERSON>equired: 'Please enter the verification code.',
        captchaError: 'The verification code is incorrect.',
        usernameLengthError: 'The username should be between 3 and 20 characters.',
        passwordLengthError: 'The password length should be between 3 and 20 characters.',
        loginError: 'Login failed. Please check your account and password.',
        agree: 'I have read and agreed.',
        serviceAgreement: 'Service Agreement',
        privacyAgreement: 'Privacy Agreement',
        agreeRequired: 'Please agree to the agreements.'
    },

    // Project page
    project: {
        totalProjects: 'Total Number of Projects Under Construction',
        totalAmount: 'Total Contract Amount of Projects Under Construction',
        annualProjectRevenue: 'Annual Total Project Receivables',
        annualProjectOutput: 'Annual Project Output Value',
        annualContractValue: 'Annual Project Output Value',
        revenue: 'Project Payment Revenue',
        budgetThisYear: 'Current Year Budget',
        completion: 'Completion',
        yearOnYearGrowth: 'Year-on-year Growth',
        totalStaff: 'Total Number of Personnel',
        safetyQualityInspection: 'Safety and Quality Inspection of Projects Under Construction',
        inspectionRecords: 'Investigation Record',
        hazardRecords: 'Potential Hazard Record',
        qualityProblemsTotal: 'Total Number of Quality Issues',
        rectificationRate: 'Rectification Rate',
        timelyRectificationRate: 'Timeliness Rate of Rectification',
        qualityProblemsTrend: 'Annual Trend of Quality Issues and Rectification',
        chinese: 'Chinese',
        foreign: 'Foreign',
        formalEmployees: 'Regular Employee',
        otherEmployment: 'Other Forms of Employment',
        people: '',
        overview: 'Overview of Projects Under Construction',
        // Staff types
        overseasLocalStaff: 'Overseas Local Employees',
        laborDispatch: 'Dispatched Employees',
        otherEmploymentForm: 'Other Employment Forms',
        partnerStaff: 'Personnel from Partner Units',
        // Quality status
        pendingRectification: 'To Be Rectified',
        qualified: 'Qualified',
        pendingVerification: 'To Be Verified',
        rectified: 'Rectified',
        problemCount: 'Number of Issues',
        rectificationCount: 'Number of Rectifications',
        // Months
        march: 'March',
        april: 'April',
        may: 'May',
        june: 'June',
        july: 'July',
        august: 'August',
        september: 'September',
        october: 'October',
        november: 'November',
        december: 'December',
        january: 'January',
        february: 'February',
        // Regions
        domestic: 'Domestic',
        international: 'International',
        // Data fields translation
        overseasBusinessCountries: 'Countries with Overseas Business Layout',
        overseasProjects: 'Overseas Projects Under Construction',
        contractAmount: 'Total Contract Amount',
        cumulativeOutput: 'Cumulative Output Value',
        cumulativeEngineering: 'Accumulated Project Payment Received',
        overseasPersonnel: 'Number of Overseas Personnel',
        domesticBusinessProvinces: 'Provinces with Domestic Business',
        domesticProjects: 'Domestic Projects Under Construction (Including Design)',
        units: {
            project: '',
            tenThousandYuan: 'Thousand Yuan',
            tenThousandUSD: 'Thousand USD',
            hundredMillionYuan: 'Hundred Million Yuan',
            hundredMillionUSD: 'Hundred Million USD',
            person: '',
            record: ''
        },
        // Data fields translation
        dataFields: {
            overseasBusinessCountries: 'Countries with Overseas Business Layout',
            overseasProjects: 'Overseas Projects Under Construction',
            contractAmount: 'Total Contract Amount',
            cumulativeOutput: 'Cumulative Output Value',
            cumulativeEngineering: 'Accumulated Project Payment Received',
            overseasPersonnel: 'Number of Overseas Personnel',
            domesticBusinessProvinces: 'Provinces with Domestic Business',
            domesticProjectsWithDesign: 'Domestic Projects Under Construction (Including Design) ',
            projectTotalPersonnel: 'Total Number of People in Project',
            formalEmployees: 'Regular Employee',
            otherEmploymentForms: 'Other Forms of Employment',
            currentYearCollectionRate: 'Other Forms of Employment'
        },
        // Staff types
        staffTypes: {
            formalEmployees: 'Regular Employee',
            overseasLocalStaff: 'Overseas Local Employees',
            laborDispatch: 'Dispatched Employees',
            otherEmploymentForms: 'Other Forms of Employment',
            partnerStaff: 'Personnel from Partner Units'
        },
        // Quality status
        qualityStatus: {
            pendingRectification: 'To Be Rectified',
            qualified: 'Qualified',
            pendingVerification: 'To Be Verified',
            rectified: 'Rectified'
        },
        // Chart items
        chartItems: {
            problemCount: 'Number of Issues',
            rectificationCount: 'Number of Rectifications',
            domestic: 'Domestic',
            international: 'International'
        },
        // Months
        months: {
            march: 'March',
            april: 'April',
            may: 'May',
            june: 'June',
            july: 'July',
            august: 'August',
            september: 'September',
            october: 'October',
            november: 'November',
            december: 'December',
            january: 'January',
            february: 'February'
        }
    },

    // Business management
    business: {
        overview: 'Business Overview',
        performance: 'Operating Performance',
        analysis: 'Business Analysis',
        revenue2025: '2025 Annual Revenue',
        revenue2024: '2024 Annual Revenue',
        revenue2023: '2023 Annual Revenue',
        annualRevenue: 'Annual Revenue',
        billionYuan: 'billion yuan',

        // Business Data Page
        businessData: {
            title: 'Business operation data',
            internationalEngineering: 'Operational Data of International Engineering Projects',
            domesticEngineering: 'Operational Data of Domestic Engineering Projects',
            internationalTrade: 'Operational Data of International Trade',
            investmentBusiness: 'Operational Data of Investment',

            // Data Metrics
            metrics: {
                newContractAmount: 'New Contract Amount',
                internationalOutput: 'International Output Value',
                constructionOutput: 'Construction Output Value',
                operatingRevenue: 'Operating Revenue',
                totalProfit: 'Total Profit',
                importAmount: 'Import Volume',
                exportAmount: 'Export Volume'
            },

            // Target and Completion
            target: {
                thisYearTarget: 'This Year\'s Target',
                completion: 'Completion',
                yearOnYearGrowth: 'Year-on-Year Growth',
                lastYear: 'Last Year',
                thisYear: 'This Year'
            },

            // Units
            units: {
                tenThousandUSD: 'Ten Thousand USD',
                tenThousandRMB: 'Ten Thousand RMB',
                percent: '%'
            },

            // Dialog
            dialog: {
                title: 'Key New Signed Projects in 2024',
                companyHeader: 'Company',
                projectHeader: 'Project Name',
                contractHeader: 'Contract Amount (Ten Thousand USD)'
            }
        }
    },

    // Asset management
    assets: {
        overview: 'Asset Overview',
        management: 'Asset Management',
        analysis: 'Asset Analysis',
        netFixedAssets: 'Net Fixed Assets',
        currentYearDepreciation: 'Current Year Depreciation',
        investmentRealEstate: 'Investment Real Estate',
        assetRanking: 'Asset Ranking',
        land: 'Land',
        houseAndBuilding: 'Houses and Buildings',
        transportationEquipment: 'Transportation Equipment',
        machinery: 'Machinery and Equipment',
        officeAndOtherEquipment: 'Office and Other Equipment',
        totalCurrentYearDepreciation: 'Total Current Year Depreciation',
        depreciationRate: 'Depreciation Rate',
        yearEndAmount: 'Year-end Amount',
        fairValueChange: 'Fair Value Change',
        cost: 'Cost',
        unit: '（Ten Thousand Yuan）',
        rankingTop5: 'TOP5'
    },

    // One office one screen
    oneOfficeOneScreen: {
        domestic: 'Domestic Business',
        international: 'International Business',
        overview: 'Business Overview',
        // Group headquarters
        group: {
            headquarters: 'CJI'
        },
        // Party committee system
        partyCommittee: {
            partyCommittee: 'CPP Committee',
            disciplinaryCommittee: 'Discipline Inspection Committee(DIC)',
            disciplinaryOffice: 'DIC Office',
            supervisionInspectionDept: 'Supervision and Inspection Department',
            investigationDept: 'Inspection and Investigation Department',
            organizationDept: 'Organization Department',
            partyMassWorkDept: 'Party Mass Work Department'
        },
        // Governance structure
        governance: {
            boardOfDirectors: 'Board of Directors',
            strategyInvestmentBudgetCommittee: 'Strategy, Investment and Budget Committee',
            compensationAssessmentCommittee: 'Compensation and Assessment Committee',
            auditCommittee: 'Audit Committee',
            riskComplianceCommittee: 'Risk and Compliance Management Committee',
            managementTeam: 'Management Team'
        },
        // Functional departments
        functionalDepts: {
            groupOffice: 'Group Office',
            financeDept: 'Financial Department',
            domesticBusinessDept: 'Domestic Business Department',
            internationalBusinessDept: 'Overseas Business Department',
            enterprisePlanningDept: 'Enterprise Planning and Management Department',
            auditDept: 'Audit Department',
            legalComplianceDept: 'Law Department',
            qualitySafetyDept: 'Quality and Safety Department'
        },
        // International engineering
        internationalEngineering: {
            title: 'International Engineering',
            overseasEngineeringCo1: 'Overseas Engineering Branch 1',
            overseasEngineeringCo2: 'Overseas Engineering Branch 2',
            overseasEngineeringCo3: 'Overseas Engineering Branch 3',
            jianshaConstructionCo: 'Jiangsu Jianda Construction Co., Ltd.',
            mauritiusCo: 'China Jiangsu International Mauritius Company',
            papuaNewGuineaCo: 'China Jiangsu International Papua New Guinea Company',
            humanResourcesBranch: 'Human Resources Branch',
            overseasCooperationInvestmentCo: 'Jiangsu Overseas Cooperation and Investment Company'
        },
        // Domestic engineering
        domesticEngineering: {
            title: 'Domestic Engineering',
            jiangsuConstructionGroup: 'China Jiangsu Construction Group Co., Ltd.',
            thirdConstructionBranch: 'Third Construction Branch',
            fifthConstructionBranch: 'The Fifth Construction Branch of Jiangsu Zhongjiang International Construction Group Co., Ltd.',
            decorationCurtainWallCo: 'Jiangsu Province Decoration Curtain Wall Engineering Co., Ltd.',
            architecturalDesignInstitute: 'China Jiangsu International Economic and Technological Cooperation Group Co., Ltd. Architectural Design Institute',
            publicEngineeringConstructionCenter: 'Jiangsu Public Works Co.Ltd.'
        },
        // International trade
        internationalTrade: {
            title: 'International Trade',
            overseasImportExportCo: 'Jiangsu Zhongjiang Overseas Import and Export Co., Ltd',
            hengtaiImportExportCo: 'Jiangsu Zhongjiang Hengtai Import and Export Co., Ltd ',
            internationalChemicalCo: 'Jiangsu Zhongjiang International Chemical Co., Ltd',
            internationalImportExportCo: 'Jiangsu Zhongjiang International Import and Export Co., Ltd',
            newMaterialsTechCo: 'Jiangsu New Material Sci-tech Development Co., Ltd'
        },
        // Investment services
        investmentServices: {
            title: 'Investment Services',
            equipmentCompleteSetCo: 'Jiangsu Complete Equipment Co., Ltd.',
            realEstateCo: 'Jiangsu Zhongjiang International Real Estate Co.,Ltd.',
            investmentCo: 'Jiangsu Zhongjiang International Investment Co., Ltd',
            urbanRuralConstructionInvestmentCo: 'Jiangsu Urban and Rural Construction Investment Co., Ltd',
            digitalConstructionTechCo: 'Jiangsu Zhongjiang Digital Construction Technology Co., Ltd',
            techInnovationIndustryDevelopmentCo: 'China Jiangsu Sci-tech Innovation Industry Development Co., Ltd.'
        }
    },

    // On-site video
    onSiteVideo: {
        liveVideo: 'Live Video',
        videoManagement: 'Video Management',
        monitoring: 'Monitoring Center'
    },

    // One profit five rates
    oneProfitFiveRates: {
        profit: 'Profit',
        profitMargin: 'Profit Margin',
        costRate: 'Cost Rate',
        expenseRate: 'Expense Rate',
        taxRate: 'Tax Rate',
        analysis: 'Financial Analysis',
        profitTotal: 'Total Profit',
        operatingCashRatio: 'Operating Cash Ratio',
        netAssetIncome: 'Return on Net Assets (ROE)',
        assetLiabilityRatio: 'Asset-Liability Ratio',
        allStaffLabor: 'Labor Productivity of All Staff',
        rdFunding: 'R&D Investment Intensity',
        unit: 'Ten Thousand Yuan',
        percent: '%',
        indicators: {
            operatingCashRatio: 'Operating Cash Ratio',
            netAssetIncome: 'Return on Net Assets (ROE)',
            assetLiabilityRatio: 'Asset-Liability Ratio',
            allStaffLabor: 'Labor Productivity of All Staff',
            rdFunding: 'R&D Investment Intensity'
        }
    },

    // Camp page
    camp: {
        overview: 'Camp Overview',
        name: 'xxxx Camp Name',
        location: 'xxxx Camp Location',
        video: 'Camp Video',
        photos: 'Camp Photos',
        noName: 'No Name Available'
    },

    // Country detail page
    country: {
        projectsUnderConstruction: 'Total Number of Projects Under Construction',
        engineeringRevenue: 'Engineering Revenue',
        contractAmount: 'Total Contract Amount of Projects Under Construction',
        cumulativeOutput: 'Cumulative Output Value',
        overseasPersonnel: 'Number of Overseas Personnel',
        chinese: 'Chinese',
        foreign: 'Foreign',
        formalEmployees: 'Formal Employees',
        otherEmploymentForms: 'Other Employment Forms',
        laborDispatch: 'Labor Dispatch',
        otherEmployment: 'Other Employment',
        partnerUnits: 'Partner Units',
        units: {
            projects: 'Projects',
            people: 'Persons',
            tenThousandYuan: 'Ten Thousand Yuan',
            tenThousandUSD: 'Ten Thousand USD'
        }
    },

    // Project Detail Page
    projectDetail: {
        // Top tabs
        tabs: {
            overview: 'Project Overview',
            qualitySafety: 'Quality & Safety',
            equipmentFacilities: 'Equipment & Facilities'
        },
        // Project data fields
        fields: {
            projectName: 'Project Name',
            contractAmount: 'Contract Amount',
            projectOutput: 'Output Value',
            engineeringPayment: 'Total Engineering Payment Received',
            projectProgress: 'Project Progress',
            constructionProgress: 'Construction Progress',
            startDate: 'Commencement Date',
            totalDuration: 'Total Construction Period',
            projectPhotos: 'Project Photos',
            projectVideo: 'Project Video',
            totalPersonnel: 'Total Project Personnel',
            chinese: 'Chinese',
            foreign: 'Foreign',
            country: 'Country',
            province: 'Province',
            city: 'City',
            district: 'District',
            longitude: 'Project Longitude',
            latitude: 'Project Latitude',
            formalEmployees: 'Formal Employees',
            laborDispatch: 'Labor Dispatch',
            otherEmploymentForms: 'Other Employment Forms',
            partnerPersonnel: 'Personnel from Partner Units'
        },
        // Action buttons
        actions: {
            back: 'Back',
            thumbnailAlt: 'Thumbnail'
        },
        // Units
        units: {
            tenThousandYuan: 'Ten Thousand Yuan',
            tenThousandUSD: 'Ten Thousand US Dollars',
            people: 'Persons',
            percent: '%',
            projects: 'Projects',
            days: 'Days'
        },
        // Messages
        messages: {
            noData: 'No Data Available',
            loading: 'Loading...',
            defaultImageUsed: 'Default Image Used',
            imageLoadFailed: 'Image Loading Failed',
            dataLoadFailed: 'Data Loading Failed'
        },
        // Project Overview Page (item1)
        overview: {
            projectTitle: 'Project Name',
            projectManagement: 'Project Management',
            projectPersonnel: 'Project Personnel',
            videoMonitoring: 'Video Monitoring',
            basicInfo: {
                contractAmount: 'Contract Amount',
                projectOutput: 'Output Value',
                totalDuration: 'Total Construction Period',
                startDate: 'Commencement Date',
                constructionProgress: 'Construction Progress',
                engineeringPayment: 'Total Engineering Payment Received',
                totalPersonnel: 'Total Project Personnel'
            }
        },
        // Quality Safety Page (item2)
        qualitySafety: {
            safetyHazards: 'Safety Hazards',
            qualityIssues: 'Quality Issues',
            inspectionRecords: 'Inspection Records',
            hazardRecords: 'Hazard Records',
            rectificationRate: 'Rectification Rate',
            timelyRectificationRate: 'Timely Rectification Rate',
            status: {
                resolved: 'Resolved',
                pendingReview: 'Pending Review',
                pendingRectification: 'Pending Rectification',
                overdue: 'Overdue',
                qualified: 'Qualified',
                pendingVerification: 'Pending Verification'
            },
            units: {
                types: 'Types',
                percent: '%'
            }
        },
        // Equipment Facilities Page (item3)
        equipmentFacilities: {
            videoMonitoring: 'Video Monitoring',
            dustMonitoring: {
                title: 'Dust Monitoring',
                all: 'All',
                data: {
                    pm25: 'PM2.5',
                    pm10: 'PM10',
                    temperature: 'Temperature',
                    humidity: 'Humidity',
                    windSpeed: 'Wind Speed',
                    windDirection: 'Wind Direction',
                    noise: 'Noise',
                    pressure: 'Atmospheric Pressure'
                },
                units: {
                    microgram: 'μg/m³',
                    celsius: '℃',
                    percent: '%',
                    meterPerSecond: 'm/s',
                    decibel: 'dB',
                    kilopascal: 'kPa'
                },
                values: {
                    northeast: 'Northeast'
                }
            },
            tabs: {
                generalDetection: 'General Detection',
                towerCraneMonitoring: 'Tower Crane Monitoring',
                elevatorMonitoring: 'Elevator Monitoring',
                unloadingPlatform: 'Unloading Platform'
            },
            towerCrane: {
                totalCount: 'Total Tower Cranes',
                running: 'In Operation',
                stopped: 'Stopped',
                height: 'Tower Height',
                jibLength: 'Jib Length',
                counterJibLength: 'Counter Jib Length'
            },
            elevator: {
                totalCount: 'Total Elevators',
                onlineCount: 'Online Count',
                todayWarnings: 'Today\'s Warnings',
                todayAlarms: 'Today\'s Alarms',
                offlineCount: 'Offline Count',
                weight: 'Load',
                height: 'Height',
                xTilt: 'X Tilt',
                yTilt: 'Y Tilt'
            },
            unloading: {
                realTimeWeight: 'Real-time Load',
                warningValue: 'Load Warning Threshold',
                alarmValue: 'Load Alarm Threshold'
            },
            units: {
                count: 'Unit',
                meter: 'm',
                kg: 'kg',
                degree: '°'
            }
        }
    },

    // One Office One Screen
    oneOfficeOneScreen: {
        detail: {
            title: 'Screen Detials',

            // Left Panel
            leftPanel: {
                personnelManagement: 'Personnel Management',
                projectInspection: 'Project Inspection',
                safetyEducation: 'Safety Education',
                hazardOverview: 'Hazard Overview'
            },

            // Personnel Management
            personnel: {
                totalPersonnel: 'Total Project Personnel',
                constructionWorkers: 'Construction Workers',
                safetyManagers: 'Safety Managers',
                managementStaff: 'Management Staff'
            },

            // Project Inspection
            inspection: {
                totalProjects: 'Total Projects',
                group: 'Group',
                subsidiary: 'Subsidiary',
                project: 'Project',
                selfInspection: 'Self-inspection',
                times: 'Times'
            },

            // Safety Education
            safetyEducation: {
                monthlyExamPassRate: 'Monthly Exam Pass Rate',
                safetyTrainingCoverage: 'Safety Training Coverage Rate'
            },

            // Hazard Overview
            hazard: {
                totalHazards: 'Total Hazards',
                rectified: 'Rectified',
                unrectified: 'Unrectified'
            },

            // Center Map Area Statistics
            centerStats: {
                greenConstructionAlerts: 'Green Construction Alerts',
                aiWarnings: 'AI Warnings',
                videoMonitoringDevices: 'Video Monitoring Devices',
                majorHazardousProjects: 'Major Hazardous Projects'
            },

            // Quality Management
            qualityManagement: {
                title: 'Quality Management',
                totalInspections: 'Total Inspections',
                firstTimeAcceptanceRate: 'First-time Acceptance Rate',
                repairRectificationRate: 'Repair and Rectification Rate'
            },

            // Quality Trend
            qualityTrend: {
                title: 'Quality Issue Trend',
                overdue: 'Overdue',
                notOverdue: 'Not Overdue',
                veryUrgent: 'Very Urgent',
                generalSituation: 'General Situation'
            },

            // Awards
            awards: {
                title: 'Excellence Awards',
                nationalQualityAward: 'National High-quality Project Award',
                provincialSafetyAward: 'Provincial Safe and Civilized Construction Award',
                municipalGreenBuildingAward: 'Municipal Green Building Demonstration Award',
                industryQualityAward: 'Industry Quality Management Award',
                technicalInnovationAward: 'Outstanding Contribution Award for Technical Innovation',
                environmentalProtectionAward: 'Advanced Unit Award for Environmental Protection Construction',
                smartConstructionAward: 'Excellent Smart Construction Project Award',
                lubanAward: 'Luban Award for Construction Engineering'
            },

            // Legal Regulations
            legalRegulations: {
                title: 'Laws and Regulations',
                regulations: 'Regulations',
                standards: 'Standards',
                notices: 'Notices'
            },

            // Bottom Buttons
            bottomButtons: {
                safety: 'Safety',
                quality: 'Quality',
                environment: 'Environment'
            },

            // Common Units
            units: {
                people: 'Persons',
                items: 'Items',
                times: 'Times',
                percent: '%'
            }
        },

        // Safety Page
        safety: {
            title: 'Safety Management',

            // Inspection Type Analysis
            inspectionTypeAnalysis: {
                title: 'Inspection Type Analysis',
                dailyInspection: 'Daily Inspection',
                specialInspection: 'Special Inspection',
                surpriseInspection: 'Surprise Inspection',
                generalRecord: 'General Record',
                hazardRectification: 'Hazard Rectification',
                shutdownRectification: 'Shutdown for Rectification'
            },

            // Enterprise Inspection
            enterpriseInspection: {
                title: 'Enterprise Inspection',
                totalEnterprises: 'Total Number of Enterprises',
                hazardCount: 'Number of Hazards'
            },

            // Project Inspection Analysis
            projectInspectionAnalysis: {
                title: 'Project Inspection Analysis',
                inspectionPoint: 'Inspection Point',
                table: {
                    inspectionTime: 'Inspection Time',
                    inspectionPoint: 'Inspection Point',
                    inspector: 'Inspector',
                    inspectionSituation: 'Inspection Situation',
                    qrCode: 'QR Code',
                    operation: 'Operation',
                    search: 'Search'
                }
            },

            // Hazard Type Analysis
            hazardTypeAnalysis: {
                title: 'Hazard Type Analysis',
                hazardType: 'Hazard Type',
                siteGovernance: 'Site Governance',
                hazard2: 'Hazard 2',
                hazard3: 'Hazard 3',
                hazard4: 'Hazard 4'
            },

            // Hazard Top 5
            hazardTop5: {
                title: 'Top 5 Hazard Issues',
                hazardTypeLabel: 'Hazard Type：'
            },

            // Common Units
            units: {
                items: 'piece(s)',
                times: 'time(s)',
                percent: '%'
            }
        }
    },

    // Common
    common: {
        loading: 'Loading',
        noData: 'No Data Available',
        error: 'Error',
        success: 'Success',
        save: 'Save',
        edit: 'Edit',
        delete: 'Delete',
        search: 'Search',
        back: 'Back',
        next: 'Next',
        previous: 'Previous',
        submit: 'Submit',
        reset: 'Reset',
        close: 'Close',
        total: 'Total',
        detail: 'Details',
        more: 'More',
        expand: 'Expand',
        collapse: 'Collapse',
        languageSwitch: 'Language Switch',
        dropdown: 'Dropdown Menu',
        header: {
            title: '中江国际集团全球经营管理数字化平台'
        }
    }
} 