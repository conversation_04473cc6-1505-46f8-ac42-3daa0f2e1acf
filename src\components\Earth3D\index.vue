<template>
  <div class="earth-container" ref="container">
    <!-- <div class="color-picker">
      <div>
        <label>地球颜色：</label>
        <input type="color" v-model="earthColor" @input="updateEarth('emissive')">
      </div>
      <div>
        <label>地球透明度：</label>
        <input type="range" min="0" max="1" step="0.01" v-model="earthOpacity" @input="updateEarth('opacity')">
      </div>
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'
import earthTexture from '@/assets/images/textures/earth4.png'
import earthTexture2 from '@/assets/images/textures/earth_point.png'

// 声明响应式引用
const container = ref(null)
const animationFrameId = ref(null)
// 地球颜色
const earthColor = ref('#4f8bf1')
// 地球透明度
const earthOpacity = ref(1)


// 声明Three.js相关变量
let _scene, _camera, _renderer, _earth, _earthcopy, _earthPoint, _earthInside, _controls, _labelRenderer
const autoRotationDelay = 2000 // 2秒后恢复自转

// 初始化Three.js场景
const initThree = () => {

  // 创建场景
  _scene = new THREE.Scene()


  // 创建相机
  _camera = new THREE.PerspectiveCamera(
    45,
    container.value.clientWidth / container.value.clientHeight,
    0.1,
    1000
  )
  window.camera = _camera

  // 创建渲染器
  _renderer = new THREE.WebGLRenderer({ 
    antialias: true, // 开启抗锯齿
    alpha: true  // 启用透明背景
  })
  _renderer.setSize(container.value.clientWidth, container.value.clientHeight)
  _renderer.setPixelRatio(window.devicePixelRatio)
  container.value.appendChild(_renderer.domElement)

  // 添加控制器
  _controls = new OrbitControls(_camera, _renderer.domElement)

  _controls.enableDamping = true
  _controls.dampingFactor = 0.03

  // 限制只能水平旋转
  _controls.minPolarAngle = Math.PI / 2
  _controls.maxPolarAngle = Math.PI / 2

  // 禁止缩放
  // _controls.enableZoom = false

  // 禁用平移
  _controls.enablePan = false

  // 启用自动旋转
  _controls.autoRotate = true
  _controls.autoRotateSpeed = 0.4 // 增加自动旋转速度

  // 添加控制器事件监听
  _controls.addEventListener('start', () => {
    _controls.autoRotate = false
  })

  _controls.addEventListener('end', () => {
    setTimeout(() => {
      _controls.autoRotate = true
    }, autoRotationDelay)
  })

  window.THREE = THREE
  window.scene = _scene
  window.render = _renderer
  window.controls = _controls

  initCubeTexture1(_renderer,()=>{
    console.log('initCubeTexture1')
  })

  // 创建地球
  createEarth()

  // 添加光源
  addLights()

  // 初始化标签渲染器
  initLabelRenderer()

  // 添加示例标签
  createLabel(new THREE.Vector3(0, 0, 1), '标签')
}

// 创建地球
const createEarth = () => {

  // 加载地球纹理
  const earthTextureMap = new THREE.TextureLoader().load(earthTexture)
  const earthTexturePointMap = new THREE.TextureLoader().load(earthTexturePoint)

  // 创建球体几何体
  const geometry = new THREE.SphereGeometry(1, 64, 64)

  // 创建地球材质
  _renderer.earthMaterial = new THREE.MeshStandardMaterial({
    map: earthTextureMap,
    transparent: true,
    opacity: 0.84,
    depthWrite: true,
    depthTest: true,
    side: 0,
    emissive: new THREE.Color('#001E52'),
    blending: 2,
    alphaTest: 0.2,
    roughness: 0.3,
    metalness: 1,
  })

  // 创建地球外部点材质
  _renderer.earthMaterialPoint = new THREE.MeshStandardMaterial({
    map: earthTexturePointMap,
    transparent: true,
    opacity: 1,
    depthWrite: false,
    emissive: new THREE.Color(earthColor.value),
    side: 1,
  })

  window.material3 = new THREE.MeshStandardMaterial({
    transparent: true,
    opacity: 0.1,
    depthWrite: false,
    side: 2,
    color: new THREE.Color(earthColor.value),
  })

  // 创建地球网格
  _earth = new THREE.Mesh(geometry, window.material)
  _earth2 = _earth.clone()
  _earth3 = _earth.clone()
  _earth3.name = 'earth3'
  _earth2.material.blending = 2
  _earth3.scale.set(1.35, 1.35, 1.35)
  _earth3.material = window.material2
  // 创建一个组来包含地球
  _renderer.earthGroup = new THREE.Group()
  _renderer.earthGroup.add(_earth)
  // _renderer.earthGroup.add(_earth2)
  _renderer.earthGroup.add(_earth3)

  // 创建地球组
  _renderer.earthGroup = new THREE.Group()
  _scene.add(_renderer.earthGroup)
  _renderer.earthGroup.position.set(0, -0.2, 0)

  // 添加地球
  _renderer.earthGroup.add(_earth)
  _renderer.earthGroup.add(_earthcopy)

  // 创建地球外部点并添加
  _earthPoint = _earth.clone()
  _earthPoint.material = _renderer.earthMaterialPoint
  _earthPoint.scale.set(1.01, 1.01, 1.01)
  _renderer.earthGroup.add(_earthPoint)

  // 创建地球内部模糊并添加
  _earthInside = _earth.clone()
  _earthInside.material = _renderer.earthMaterialInside
  _earthInside.scale.set(0.99, 0.99, 0.99)
  _renderer.earthGroup.add(_earthInside)
  
}

// 添加光源
const addLights = () => {
  // 添加环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 2)
  _scene.add(ambientLight)
}

// 创建星空背景
const createStars = () => {
  const starsGeometry = new THREE.BufferGeometry()
  const starsMaterial = new THREE.PointsMaterial({
    color: 0xffffff,
    size: 0.02,
    sizeAttenuation: true
  })

  const starsVertices = []
  for (let i = 0; i < 10000; i++) {
    const x = THREE.MathUtils.randFloatSpread(2000)
    const y = THREE.MathUtils.randFloatSpread(2000)
    const z = THREE.MathUtils.randFloatSpread(2000)
    starsVertices.push(x, y, z)
  }

  starsGeometry.setAttribute(
    'position',
    new THREE.Float32BufferAttribute(starsVertices, 3)
  )
  const stars = new THREE.Points(starsGeometry, starsMaterial)
  _scene.add(stars)
}

// 创建环线
const createRings = () => {
  // 定义环线数量和间距
  const numRings = 10 // 环线数量
  const startY = 0.95 // 最上面的环的位置
  const endY = -0.95 // 最下面的环的位置
  const step = (endY - startY) / (numRings - 1) // 环线之间的间距
  const radiusOffset = 0.15 // 环线相对地球的额外半径

  // 创建渐变纹理
  const createGradientTexture = () => {
    const canvas = document.createElement('canvas')
    canvas.width = 256
    canvas.height = 1
    const context = canvas.getContext('2d')
    
    // 创建渐变
    const gradient = context.createLinearGradient(0, 0, canvas.width, 0)
    gradient.addColorStop(0, 'rgba(0, 208, 255, 0)')
    gradient.addColorStop(0.4, 'rgba(0, 208, 255, 1)')
    gradient.addColorStop(0.5, 'rgba(0, 208, 255, 1)')
    gradient.addColorStop(0.6, 'rgba(0, 208, 255, 1)')
    gradient.addColorStop(1, 'rgba(0, 208, 255, 0)')
    
    context.fillStyle = gradient
    context.fillRect(0, 0, canvas.width, canvas.height)
    
    return new THREE.CanvasTexture(canvas)
  }

  const glowTexture = createGradientTexture()
  glowTexture.wrapS = THREE.RepeatWrapping
  glowTexture.wrapT = THREE.RepeatWrapping

  for (let i = 0; i < numRings; i++) {
    const y = startY + step * i
    // 根据y位置计算环的半径 (使用毕达哥拉斯定理)，并添加额外半径
    const baseRadius = Math.sqrt(1 - y * y)
    const radius = baseRadius * (1 + radiusOffset)
    
    // 创建环线几何体
    const ringGeometry = new THREE.RingGeometry(radius - 0.01, radius, 64)
    
    // 创建基础环线材质
    const ringMaterial = new THREE.MeshBasicMaterial({
      color: 0x0092ff,
      side: THREE.DoubleSide,
      transparent: true,
      opacity: 0.3
    })

    // 创建光晕环线材质
    const glowMaterial = new THREE.MeshBasicMaterial({
      map: glowTexture.clone(), // 每个环使用独立的纹理实例
      side: THREE.DoubleSide,
      transparent: true,
      opacity: 0.4,
      blending: THREE.AdditiveBlending
    })
    
    // 创建基础环线和光晕环线
    const ring = new THREE.Mesh(ringGeometry, ringMaterial)
    const glowRing = new THREE.Mesh(
      new THREE.RingGeometry(radius - 0.01, radius, 64),
      glowMaterial
    )
    
    // 创建一个组来包含环
    _renderer.ringGroup = new THREE.Group()
    _renderer.ringGroup.add(ring)
    _renderer.ringGroup.add(glowRing)
    
    // 设置环的位置和旋转
    _renderer.ringGroup.position.y = y * (1 + radiusOffset)
    ring.rotation.x = -Math.PI / 2
    glowRing.rotation.x = -Math.PI / 2
    
    // 存储初始偏移量，使每个环的光晕位置不同
    glowMaterial.map.offset.x = i / numRings
    
    // 将材质添加到一个数组中，以便在动画循环中更新
    if (!window.glowMaterials) window.glowMaterials = []
    window.glowMaterials.push(glowMaterial)


    _renderer.earthGroup.add(_renderer.ringGroup)
    
    // // 添加到场景
    // _scene.add(_renderer.ringGroup)
  }
}

// 初始化标签渲染器
const initLabelRenderer = () => {
  _labelRenderer = new CSS2DRenderer()
  _labelRenderer.setSize(container.value.clientWidth, container.value.clientHeight)
  _labelRenderer.domElement.style.position = 'absolute'
  _labelRenderer.domElement.style.top = '0'
  _labelRenderer.domElement.style.pointerEvents = 'none'
  container.value.appendChild(_labelRenderer.domElement)
}

// 创建标签
const createLabel = (position, title) => {
  // 创建HTML元素
  const labelDiv = document.createElement('div')
  labelDiv.className = 'label-bg'
  labelDiv.innerHTML = `${title}<span class="label-num">10</span>`

  // 创建CSS2D对象
  const label = new CSS2DObject(labelDiv)
  label.position.copy(position)
  label.userData = { basePosition: position.clone() }
  _renderer.earthGroup.add(label)
}

// 检查标签是否在地球背面
const checkLabelVisibility = () => {
  const labels = _renderer.earthGroup.children.filter(child => child instanceof CSS2DObject)
  
  labels.forEach(label => {
    const worldPosition = label.userData.basePosition.clone()
    worldPosition.applyMatrix4(_renderer.earthGroup.matrixWorld)
    
    const directionToCamera = new THREE.Vector3().subVectors(_camera.position, worldPosition)
    const normalVector = worldPosition.clone().normalize()
    const dotProduct = normalVector.dot(directionToCamera)
    
    label.element.style.opacity = dotProduct > 0 ? '1' : '0'
    label.element.style.pointerEvents = dotProduct > 0 ? 'auto' : 'none'
  })
}

// 修改动画循环函数
const animate = () => {
  animationFrameId.value = requestAnimationFrame(animate)

  if (_controls) {
    _controls.update()
  }

  // 检查标签可见性
  checkLabelVisibility()

  if (window.glowMaterials) {
    window.glowMaterials.forEach(material => {
      material.map.offset.x -= 0.008
    })
  }

  _renderer.render(_scene, _camera)
  _labelRenderer.render(_scene, _camera)
}

// 修改窗口大小改变处理函数
const onWindowResize = () => {
  _camera.aspect = container.value.clientWidth / container.value.clientHeight
  _camera.updateProjectionMatrix()
  _renderer.setSize(container.value.clientWidth, container.value.clientHeight)
  _labelRenderer.setSize(container.value.clientWidth, container.value.clientHeight)
}

const updateEarth = (attr) => {
  if (_renderer.earthMaterial) {
    _renderer.earthMaterial[attr] = attr === 'emissive' ? new THREE.Color(earthColor.value) : earthOpacity.value
  }
}

const initCubeTexture1 = (render,callback)=>{
var path = 'sky2/';
var format = '.jpg';
var urls = [
 // "posx"+ format, "negx"+ format,
 // "posy"+ format, "negy"+ format,
 // "posz"+ format, "negz"+ format
"px"+ format, "nx"+ format,
"py"+ format, "ny"+ format,
"pz"+ format, "nz"+ format
// "left"+ format, "right"+ format,
 // "up"+ format, "down"+ format,
// "front"+ format, "back"+ format
 ];
const loader = new THREE.CubeTextureLoader();
loader.setPath( path );
loader.load( urls ,function(texture){
// texture.rotation = THREE.MathUtils.degToRad(90);
 render.sceneBacTexture = texture;
 _scene.environment = render.sceneBacTexture;
// render.scene.environment = render.sceneBacTexture;
 if(callback) callback();
 });
}

// 生命周期钩子
onMounted(() => {
  initThree()
  animate()
  window.addEventListener('resize', onWindowResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', onWindowResize)
  
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
  }
  
  if (_earth) {
    _earth.geometry.dispose()
    _earth.material.dispose()
  }

  if (_renderer) {
    _renderer.dispose()
  }

})
</script>

<style>
/* 全局样式 */
.label-bg {
  background: url('@/assets/images/textures/biaoqian.png') no-repeat center center / 100% 100%;
  /* text-align: center; */
  min-width: 127px;
  height: 62px;
  box-sizing: border-box;
  font-size: 16px;
  color: #fff;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease;
  padding-top: 12px;
  padding-left: 30px;
}
.label-num{
  font-size: 18px;
  color: #fff;
  padding-left: 4px;
  font-weight: bold;
}
</style>

<style scoped>
.earth-container {
  width: 100%;
  height: 100%;
  position: relative;
  pointer-events: all;
}

.color-picker {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.color-picker label {
  color: white;
  font-size: 14px;
}

.color-picker input[type="color"] {
  width: 50px;
  height: 30px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

canvas {
  position: relative;
  pointer-events: auto !important;
}
</style> 