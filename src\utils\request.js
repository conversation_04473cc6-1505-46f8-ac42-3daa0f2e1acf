import axios from "axios"
import { ElMessage, ElMessageBox } from "element-plus"

const request = axios.create({
  // 修改为实际的后端地址
  baseURL: window.baseEnv.baseURL,
  timeout: 1000 * 60 * 5, // request timeout
  withCredentials: true, // 启用 Cookie
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json, text/plain, */*',
    'tenant-id': '1',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  }
})

// 是否显示重新登录
export const isRelogin = { show: false }
// 请求队列
let requestList = []
// 是否正在刷新中
let isRefreshToken = false
// 请求白名单，无须token的接口
const whiteList = ['/login', '/refresh-token']

// 设置定时器，定期检查token是否即将过期
let tokenCheckTimer = null

// 启动token检查定时器
const startTokenCheck = () => {
  // 清除可能存在的旧定时器
  if (tokenCheckTimer) {
    clearInterval(tokenCheckTimer)
  }

  // 每5分钟检查一次token
  tokenCheckTimer = setInterval(async () => {
    const token = sessionStorage.getItem('token')
    const refreshToken = sessionStorage.getItem('refreshToken')

    if (token && refreshToken) {
      try {
        // 尝试刷新token
        await refreshTokenFn()
      } catch (error) {
        console.error('Token refresh failed:', error)
      }
    }
  }, 5 * 60 * 1000) // 5分钟
}

// 登录成功后调用此函数
export const setupTokenRefresh = (token, refreshToken) => {
  // 保存token和refreshToken
  sessionStorage.setItem('token', token)
  sessionStorage.setItem('refreshToken', refreshToken)

  // 启动token检查
  startTokenCheck()
}

// 登出时调用此函数
export const clearTokenRefresh = () => {
  // 清除token和refreshToken
  sessionStorage.removeItem('token')
  sessionStorage.removeItem('refreshToken')

  // 清除定时器
  if (tokenCheckTimer) {
    clearInterval(tokenCheckTimer)
    tokenCheckTimer = null
  }

  // 清除所有请求队列
  requestList = []
  isRefreshToken = false
}

// request interceptor
request.interceptors.request.use(
  (config) => {
    // 从 sessionStorage 获取 token
    const token = sessionStorage.getItem('token')
    const refreshToken = sessionStorage.getItem('refreshToken')

    // 是否需要设置 token
    let isToken = (config.headers || {}).isToken === false
    whiteList.some((v) => {
      if (config.url) {
        config.url.indexOf(v) > -1
        return (isToken = false)
      }
    })

    if (token && !isToken) {
      config.headers["Authorization"] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// response interceptor
request.interceptors.response.use(
  async (response) => {
    const res = response.data
    const config = response.config
    console.log(res)
    // 如果返回的状态码是401，直接弹出登录提示，不进行token刷新
    if (res.code === 401) {
      return handleAuthorized()
    } else if (res.code !== 0) {
      ElMessage.error(res.msg || '请求失败')
      return Promise.reject(new Error(res.msg || '请求失败'))
    }

    return res
  },
  (error) => {
    console.error('响应错误:', error)
    // 处理401错误（需要从error.response中获取)
    if (error.response && error.response.status === 401) {
      return handleAuthorized()
    }
    ElMessage.error(error.message || '请求失败')
    return Promise.reject(error)
  }
)

// 刷新token的函数
const refreshTokenFn = async () => {
  try {
    return await axios.post('/admin-api/system/auth/refresh-token', {
      refreshToken: sessionStorage.getItem('refreshToken')
    }, {
      headers: {
        'Content-Type': 'application/json',
        'tenant-id': '1'
      }
    })
  } catch (error) {
    handleAuthorized() // 刷新失败时调用未授权处理
    return Promise.reject(error)
  }
}
// 处理未授权的情况
const handleAuthorized = () => {
  if (!isRelogin.show) {
    // 判断当前路由是否是登录页面
    const currentPath = window.location.hash.split('?')[0]
    if (currentPath === '#/login') {
      return
    }
    isRelogin.show = true
    ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
      showCancelButton: false,
      closeOnClickModal: false,
      showClose: false,
      confirmButtonText: '重新登录',
      type: 'warning'
    }).then(() => {
      // 清理token
      clearTokenRefresh()
      isRelogin.show = false
      // 跳转到登录页
      window.location.href = '/login?redirect=' + encodeURIComponent(window.location.href)
    })
  }
  return Promise.reject('登录状态已过期')
}

export default request