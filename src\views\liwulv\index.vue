<template>
    <div class="liwulv-container">
        <!-- <div class="lines-container">
            <div class="curve-container line1">
                <img src="@/assets/images/lilv/curve-dark.png" class="base-curve" />
                <img src="@/assets/images/lilv/curve-bright.png" class="highlight-curve" />
            </div>
            <div class="curve-container line2">
                <img src="@/assets/images/lilv/curve-dark.png" class="base-curve" />
                <img src="@/assets/images/lilv/curve-bright.png" class="highlight-curve" />
            </div>
            <div class="curve-container line3">
                <img src="@/assets/images/lilv/curve-dark.png" class="base-curve" />
                <img src="@/assets/images/lilv/curve-bright.png" class="highlight-curve" />
            </div>
            <div class="curve-container line4">
                <img src="@/assets/images/lilv/curve-dark.png" class="base-curve" />
                <img src="@/assets/images/lilv/curve-bright.png" class="highlight-curve" />
            </div>
        </div> -->

        <div class="profit-container">
            <div class="profit-amount">53.45<span class="unit">{{ $t('oneProfitFiveRates.unit') }}</span></div>
            <div class="profit-title">{{ $t('oneProfitFiveRates.profitTotal') }}</div>
        </div>

        <div class="indicators-container">
            <!-- 营业现金比率 -->
            <div class="indicator top-center">
                <div class="indicator-value">33.37<span class="percent">{{ $t('oneProfitFiveRates.percent') }}</span>
                </div>
                <div class="indicator-title">{{ $t('oneProfitFiveRates.indicators.operatingCashRatio') }}</div>
                <img src="@/assets/images/lilv/operatingCash.png"
                    :alt="$t('oneProfitFiveRates.indicators.operatingCashRatio')" />
            </div>

            <!-- 净资产收益率 -->
            <div class="indicator right-top">
                <div class="indicator-value">66.76<span class="percent">{{ $t('oneProfitFiveRates.percent') }}</span>
                </div>
                <div class="indicator-title">{{ $t('oneProfitFiveRates.indicators.netAssetIncome') }}</div>
                <img src="@/assets/images/lilv/netAssetIncome.png"
                    :alt="$t('oneProfitFiveRates.indicators.netAssetIncome')" />
            </div>

            <!-- 资产负债率 -->
            <div class="indicator left-bottom">
                <div class="indicator-value">66.76<span class="percent">{{ $t('oneProfitFiveRates.percent') }}</span>
                </div>
                <div class="indicator-title">{{ $t('oneProfitFiveRates.indicators.assetLiabilityRatio') }}</div>
                <img src="@/assets/images/lilv/assetLiabilityRatio.png"
                    :alt="$t('oneProfitFiveRates.indicators.assetLiabilityRatio')" />
            </div>

            <!-- 全员劳动生产率 -->
            <div class="indicator right-bottom">
                <div class="indicator-value">8.91<span class="percent">{{ $t('oneProfitFiveRates.percent') }}</span>
                </div>
                <div class="indicator-title">{{ $t('oneProfitFiveRates.indicators.allStaffLabor') }}</div>
                <img src="@/assets/images/lilv/allStaffLabor.png"
                    :alt="$t('oneProfitFiveRates.indicators.allStaffLabor')" />
            </div>

            <!-- 研发经费投入强度 -->
            <div class="indicator bottom-center">
                <div class="indicator-value">4.48<span class="percent">{{ $t('oneProfitFiveRates.percent') }}</span>
                </div>
                <div class="indicator-title">{{ $t('oneProfitFiveRates.indicators.rdFunding') }}</div>
                <img src="@/assets/images/lilv/r&dFunding.png" :alt="$t('oneProfitFiveRates.indicators.rdFunding')" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>

<style scoped>
.liwulv-container {
    pointer-events: all;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-image: url('@/assets/images/lilv/一利五率.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    position: absolute;
    top: 0;
}

.connection-lines {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.line-container {
    position: absolute;
    top: 50%;
    left: 50%;
    height: auto;
    width: 20%;
    transform-origin: left center;
}

.connection-line {
    width: 100%;
    height: auto;
    animation: wave-move 2s infinite linear;
}

.line-top {
    transform: rotate(0deg) translateY(-50%);
}

.line-right-top {
    transform: rotate(45deg) translateY(-50%);
}

.line-right-bottom {
    transform: rotate(125deg) translateY(-50%);
}

.line-left-bottom {
    transform: rotate(-125deg) translateY(-50%);
}

.line-bottom {
    transform: rotate(180deg) translateY(-50%);
}

.profit-container {
    position: absolute;
    top: 25.5%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10;
}

.profit-amount {
    font-family: TCloudNumber, TCloudNumber;
    font-weight: bold;
    font-size: 56px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    background: linear-gradient(180deg,
            #FFE49A 0%,
            rgba(255, 255, 255, 0.8) 50%,
            #FFE49A 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    margin-bottom: 10px;
}

.unit {
    font-size: 1.5rem;
    margin-left: 5px;
    color: #F3F7FF;
    font-size: 16px;
}

.profit-title {
    font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
    font-weight: bold;
    font-size: 34px;
    letter-spacing: 1px;
    text-shadow: 0px 4px 16px #0085FF, 0px 3px 8px rgba(0, 195, 255, 0.5);
    text-align: center;
    font-style: normal;
    text-transform: none;
}

.indicators-container {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 5;
}

.indicator {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.indicator-value {
    font-family: TCloudNumber, TCloudNumber;
    font-weight: bold;
    font-size: 36px;
    color: #FFFFFF;
    text-shadow: 0px 2px 0px #0085FF, 0px -1px 0px #455DEF;
    text-align: center;
    font-style: normal;
    text-transform: none;
}

.indicator-title {
    font-family: Alimama ShuHeiTi, Alimama ShuHeiTi;
    font-weight: bold;
    font-size: 24px;
    text-shadow: 0px 4px 12px #0085FF, 0px 2px 2px #072540;
    text-align: center;
    font-style: normal;
    text-transform: none;
}

.indicator img {
    width: 230px;
    height: 200px;
    position: relative;
    z-index: 2;
}

.percent {
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    color: #F3F7FF;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

.top-center {
    top: 33.5%;
    left: 24%;
    transform: translateX(-50%);
}

.left-top {
    top: 35%;
    left: 21%;
}

.right-top {
    top: 34%;
    right: 19%;
}

.left-bottom {
    bottom: 19%;
    left: 28%;
}

.right-bottom {
    bottom: 19%;
    right: 28%;
}

.bottom-center {
    bottom: 15%;
    left: 50%;
    transform: translateX(-50%);
}

@keyframes wave-move {
    0% {
        transform: translateX(0);
    }

    50% {
        transform: translateX(20px);
    }

    100% {
        transform: translateX(0);
    }
}

.curve-container {
    position: relative;
    width: 374px;
    height: 59px;
}

.base-curve,
.highlight-curve {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}

.highlight-curve {
    -webkit-mask-image: linear-gradient(to left,
            transparent 0%,
            black 20%,
            black 80%,
            transparent 100%);
    -webkit-mask-size: 200% 100%;
    -webkit-mask-position: 200% 0;
    -webkit-animation: flow 2s linear infinite;

    mask-image: linear-gradient(to left,
            transparent 0%,
            black 20%,
            black 80%,
            transparent 100%);
    mask-size: 200% 100%;
    mask-position: 200% 0;
    animation: flow 2s linear infinite;
}

@keyframes flow {
    0% {
        mask-position: 200% 0;
        -webkit-mask-position: 200% 0;
    }

    100% {
        mask-position: 0% 0;
        -webkit-mask-position: 0% 0;
    }
}

.lines-container {
    .line1 {
        width: 434px;
        height: 99px;
        position: absolute;
        right: 27%;
        top: 48%;
    }

    .line2 {
        width: 434px;
        height: 99px;
        position: absolute;
        right: 31%;
        top: 61%;
        transform: RotateY(55deg) rotateZ(372deg);
    }

    .line3 {
        width: 334px;
        height: 45px;
        position: absolute;
        right: 52%;
        top: 62%;
        transform: RotateY(155deg) rotateZ(382deg);
    }

    .line4 {
        width: 425px;
        height: 129px;
        position: absolute;
        right: 54%;
        top: 47%;
        transform: RotateY(180deg) rotateZ(357deg) rotateX(0deg) translateY(13px) translateZ(160px);
    }
}
</style>