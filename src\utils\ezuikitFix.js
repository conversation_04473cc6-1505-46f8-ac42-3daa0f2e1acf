// EZUIKit 错误修复工具

/**
 * 修复 EZUIKit 页面卸载时的 JS_HideWnd 错误
 */
export function fixEZUIKitUnloadError() {
    // 保存原始的页面卸载处理函数
    const originalOnPageHide = window.onpagehide;
    const originalOnUnload = window.onunload;

    // 重新定义页面隐藏事件处理器
    window.onpagehide = function (event) {
        try {
            if (originalOnPageHide && typeof originalOnPageHide === 'function') {
                originalOnPageHide.call(this, event);
            }
        } catch (error) {
            console.warn('EZUIKit页面隐藏事件处理出错，已忽略:', error);
        }
    };

    // 重新定义页面卸载事件处理器
    window.onunload = function (event) {
        try {
            if (originalOnUnload && typeof originalOnUnload === 'function') {
                originalOnUnload.call(this, event);
            }
        } catch (error) {
            console.warn('EZUIKit页面卸载事件处理出错，已忽略:', error);
        }
    };
}

/**
 * 安全销毁 EZUIKit 播放器
 */
export function safeDestroyEZUIKit(player) {
    if (!player) return;

    try {
        // 尝试停止播放
        if (typeof player.stop === 'function') {
            player.stop();
        }

        // 尝试销毁播放器
        if (typeof player.destroy === 'function') {
            player.destroy();
        }

        console.log('EZUIKit播放器已安全销毁');
    } catch (error) {
        console.warn('销毁EZUIKit播放器时出错，已忽略:', error);
    }
}

/**
 * 安全初始化 EZUIKit 播放器
 */
export async function safeInitEZUIKit(config) {
    try {
        // 检查 EZUIKit 是否可用
        if (typeof window.EZUIKit === 'undefined') {
            throw new Error('EZUIKit库未加载');
        }

        // 检查必要的配置参数
        if (!config.id) {
            throw new Error('缺少播放器容器ID');
        }

        const container = document.getElementById(config.id);
        if (!container) {
            throw new Error(`播放器容器未找到: ${config.id}`);
        }

        // 创建播放器
        const player = new window.EZUIKit.EZUIKitPlayer(config);

        // 添加错误处理
        if (player && typeof player.on === 'function') {
            player.on('error', (error) => {
                console.warn('EZUIKit播放器运行时错误:', error);
            });
        }

        return player;
    } catch (error) {
        console.error('初始化EZUIKit播放器失败:', error);
        throw error;
    }
}

/**
 * 清理所有 EZUIKit 相关资源
 */
export function cleanupEZUIKitResources() {
    try {
        // 清理全局变量
        if (window.janus) {
            if (typeof window.janus.destroy === 'function') {
                window.janus.destroy();
            }
            window.janus = null;
        }

        if (window.tts) {
            if (typeof window.tts.destroy === 'function') {
                window.tts.destroy();
            }
            window.tts = null;
        }

        // 清理其他可能的全局对象
        if (window.EZUIKit && window.EZUIKit.instances) {
            Object.keys(window.EZUIKit.instances).forEach(key => {
                const instance = window.EZUIKit.instances[key];
                if (instance && typeof instance.destroy === 'function') {
                    try {
                        instance.destroy();
                    } catch (e) {
                        console.warn(`清理EZUIKit实例${key}时出错:`, e);
                    }
                }
            });
        }

        console.log('EZUIKit资源清理完成');
    } catch (error) {
        console.warn('清理EZUIKit资源时出错:', error);
    }
}

/**
 * 监听页面可见性变化，在页面隐藏时主动清理资源
 */
export function setupEZUIKitVisibilityHandler(player) {
    let isPlayerPaused = false;

    const handleVisibilityChange = () => {
        if (!player) return;

        try {
            if (document.hidden) {
                // 页面隐藏时暂停播放
                if (typeof player.pause === 'function') {
                    player.pause();
                    isPlayerPaused = true;
                }
            } else {
                // 页面显示时恢复播放
                if (isPlayerPaused && typeof player.play === 'function') {
                    player.play();
                    isPlayerPaused = false;
                }
            }
        } catch (error) {
            console.warn('处理页面可见性变化时出错:', error);
        }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 返回清理函数
    return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
}

/**
 * 防抖函数 - 用于防止频繁的播放器操作
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
} 