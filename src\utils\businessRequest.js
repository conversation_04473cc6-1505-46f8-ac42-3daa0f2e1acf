import axios from 'axios';

// 创建业务请求实例
const businessRequest = axios.create({
    baseURL: 'http://222.190.120.19:8088', // 修改为实际的接口地址
    timeout: 60000,
    timeout: 1000 * 60 * 5, // request timeout
    withCredentials: true, // 启用 Cookie
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'tenant-id': '1',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }
});

// 添加请求拦截器
businessRequest.interceptors.request.use(
    (config) => {
        // 从 sessionStorage 获取 token，和全局 request.js 保持一致
        const token = sessionStorage.getItem('token');

        if (token) {
            config.headers["Authorization"] = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

export default businessRequest; 