<template>
  <div class="relative w-full h-full origin-[0_0] overflow-hidden bg-[#0a0f1a] bg-[right_bottom] z-[2]">
    <div class="content relative">
      <div
        class="absolute w-1/2 text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title z-10 top-0 left-2/4 transform -translate-x-1/2">
        {{ title }}
      </div>
      <div
        class="absolute w-1/2 text-center text-[28px] font-family-youshebiaotihei tracking-[8px] title z-10 top-[120px] left-2/4 transform -translate-x-1/2">
        {{ projectData.项目名称 }}</div>
      <div class="bg"></div>
      <!-- <div class="topBg"></div> -->
      <div class="leftBg"></div>
      <div class="bottomBg"></div>
      <div class="btns">
        <div v-for="(item, index) in filteredBtns" :class="btnsActive == index && 'active'" :key="index"
          @click="btnsChange(index)">
          <span> {{ item.name }}</span>
        </div>
      </div>
      <!-- 只有当不是使用默认图片时才显示缩略图容器 -->
      <div v-if="!isUsingDefaultImage" class="thumbnails-container">
        <div v-for="(image, index) in images" :key="index" class="thumbnail"
          :class="{ 'active': currentImageIndex === index }">
          <img :src="image" :alt="t('projectDetail.actions.thumbnailAlt')"
            @click="() => handleThumbnailClick(index, image)" />
        </div>
      </div>
      <div class="map" :style="backgroundStyle"></div>
      <item1 :projectData="projectData" :isInternationalProject="isInternationalProject" v-if="btnsActive == 0" />
      <item2 :projectData="projectData" v-if="btnsActive == 1" />
      <item3 :projectData="projectData" v-if="btnsActive == 2" />
      <div class="backHome-button" @click="backHome">
        <img src="@/assets/images/header/back.png" alt="" />
        <span>返回首页</span>
      </div>
      <div class="back-button" @click="back">
        <span>返回上一级</span>
      </div>
      <!-- <img class="pointer-events-auto cursor-pointer absolute top-[20px] left-[100px]"
        src="@/assets/images/xiangmu/back.png" alt="" srcset="" style="z-index: 3" @click="back" /> -->
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useI18n } from 'vue-i18n';
import item1 from "./components/item1.vue";
import item2 from "./components/item2.vue";
import item3 from "./components/item3.vue";
import request from "@/utils/request";
import defaultMapImg from '@/assets/images/project/mapImg.png';

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

const title = ref("");
const projectName = localStorage.getItem("clickProject");
const projectData = ref({});

const isChina = ref(localStorage.getItem("isChina") ? true : false);

// 判断是否为国际项目
const isInternationalProject = ref(false);

const btns = ref([
  {
    id: 1,
    name: t('projectDetail.tabs.overview'),
  },
  {
    id: 2,
    name: t('projectDetail.tabs.qualitySafety'),
  },
  {
    id: 3,
    name: t('projectDetail.tabs.equipmentFacilities'),
  },
]);

// 计算过滤后的按钮列表
const filteredBtns = computed(() => {
  // if (isInternationalProject.value) {
  //   // 国外项目只显示第一个按钮
  //   return btns.value.slice(0, 1);
  // }
  return btns.value.map(btn => ({
    ...btn,
    name: btn.id === 1 ? t('projectDetail.tabs.overview') :
      btn.id === 2 ? t('projectDetail.tabs.qualitySafety') :
        btn.id === 3 ? t('projectDetail.tabs.equipmentFacilities') :
          btn.name
  }));
});

const btnsActive = ref(0);
const btnsChange = (val) => {
  btnsActive.value = val;
  localStorage.setItem("btnsActive", val);
};
const images = ref([]);
const currentImageIndex = ref(0);
// 添加标识是否使用默认图片
const isUsingDefaultImage = ref(true);

const selectImage = (index) => {
  if (currentImageIndex.value === index) {
    // 触发强制刷新
    currentImageIndex.value = -1;
    setTimeout(() => {
      currentImageIndex.value = index;
    }, 0);
  } else {
    currentImageIndex.value = index;
  }
};

function generateImageUrls(pathStr, options = {}) {
  // 参数校验
  if (typeof pathStr !== "string") {
    throw new TypeError("路径参数必须是字符串类型");
  }

  // 合并配置选项
  const {
    separator = ";",
    prefix = "https://vr.ztmapinfo.com/yydpdatamedia.php?path=",
    allowedExtensions = [".jpg", ".jpeg", ".png", ".webp"],
  } = options;

  // 处理路径
  return pathStr
    .split(separator)
    .map((path) => path.trim())
    .filter((path) => {
      // 空路径过滤
      if (!path) return false;

      // 扩展名校验
      const extension = path.slice(path.lastIndexOf(".")).toLowerCase();
      return allowedExtensions.includes(extension);
    })
    .map((path) => {
      // 编码处理
      const encodedPath = encodeURIComponent(path);
      return `${prefix}${encodedPath}`;
    });
}

// 照片处理函数
async function getProjectPhotoUrls(photoIds) {
  if (!photoIds) {
    return [defaultMapImg];
  }

  try {
    // 先获取token
    const tokenRes = await request.get("/globalManage/zjmanage/largescreen/getToken");

    if (tokenRes.code !== 0 || !tokenRes.data) {
      console.error("获取token失败:", tokenRes);
      return [defaultMapImg];
    }

    const token = tokenRes.data;
    const userId = "941981453197164545"; // 固定的userId

    // 处理照片ID列表
    const photoIdArray = photoIds.split(',').filter(id => id.trim());

    if (photoIdArray.length === 0) {
      return [defaultMapImg];
    }

    const photoUrls = [];

    // 限制处理最多5张图片
    const maxPhotos = Math.min(photoIdArray.length, 5);

    // 验证每个图片URL是否可访问
    for (let i = 0; i < maxPhotos; i++) {
      const photoId = photoIdArray[i].trim();
      if (photoId) {
        try {
          // 构建照片URL
          const photoUrl = `http://192.168.15.61:20600/papi/openapi/api/file/v2/common/download/${photoId}?access_token=${token}&userid=${userId}`;

          // 验证图片是否可访问
          const isValid = await validateImageUrl(photoUrl);
          if (isValid) {
            photoUrls.push(photoUrl);
          }
        } catch (err) {
          console.warn(`图片${photoId}处理失败:`, err);
          continue;
        }
      }
    }

    return photoUrls.length > 0 ? photoUrls : [defaultMapImg];
  } catch (err) {
    console.error("处理照片失败:", err);
    return [defaultMapImg];
  }
}

// 验证图片URL是否可访问
function validateImageUrl(url) {
  return new Promise((resolve) => {
    const img = new Image();
    const timeout = setTimeout(() => {
      resolve(false);
    }, 5000); // 5秒超时

    img.onload = () => {
      clearTimeout(timeout);
      resolve(true);
    };

    img.onerror = () => {
      clearTimeout(timeout);
      resolve(false);
    };

    img.src = url;
  });
}

// 获取项目数据
async function fetchProjectData() {
  try {
    let projectDatas = {};
    let queryData = {}
    try {
      // 从sessionStorage获取项目数据而不是URL参数
      queryData = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    } catch (e) {
      console.error("解析项目数据失败", e);
    }
    const projectId = queryData.code || "";
    const isGwParam = queryData.isGw || "false";

    // 转换isGw参数，确保是字符串类型
    const isGw = String(isGwParam).toLowerCase();

    // 设置国际项目标识
    isInternationalProject.value = isGw === "true";

    let res;
    if (isGw === "true") {
      // 国外项目接口
      res = await request.get("/globalManage/zjmanage/largescreen/getXmxxV2", {
        params: { id: projectId }
      });
    } else {
      // 国内项目接口
      res = await request.get("/globalManage/zjmanage/largescreen/getXmjbxx", {
        params: { id: projectId }
      });
    }

    if (res.code === 0 && res.data && res.data.length > 0) {
      const data = res.data[0];

      if (isGw === "true") {
        // 设置标题为国家名
        title.value = data.国家 || "";
        projectData.value = data;
      } else {
        // 国内项目数据映射
        projectDatas = {
          项目名称: data.项目名称 || "",
          合同金额: data.合同金额 || "0",
          产值: data.产值 || "0",
          工程回款总额
            : data.工程回款总额 || "0",
          项目进度: data.施工进度 ? data.施工进度 + '%' : '0%',
          开工日期: data.开工日期 || "",
          总工期
            : data.总工期 || "",
          照片: data.项目照片 || "",
          视频: data.项目视频 || "",
          项目总人数: data.项目总人数 || "0",
          正式员工: data.正式员工 || "0",
          劳务派遣: data.劳务派遣 || "0",
          其他形式用工: data.其他形式用工 || "0",
          合作单位: data.合作单位人员 || "0",
          类型: "项目",
          省: data.省 || "",
          市: data.市 || "",
          区: data.区 || "",
          项目经度: data.项目经度 || "",
          项目纬度: data.项目纬度 || ""
        };

        // 设置标题为省份名
        title.value = data.省 || "";
        projectData.value = data;
      }

      // 处理项目图片 - 使用新的接口和照片处理逻辑
      if (data.项目照片) {
        try {
          // 使用新的照片处理函数
          const photoUrls = await getProjectPhotoUrls(data.项目照片);

          // 检查是否获取到有效的图片URL
          if (photoUrls.length > 0 && photoUrls[0] !== defaultMapImg) {
            images.value = photoUrls;
            isUsingDefaultImage.value = false;
          } else {
            // 如果有照片字段但处理失败，不设置默认图片，让背景为空
            images.value = [];
            isUsingDefaultImage.value = true;
          }
        } catch (err) {
          console.error("处理项目图片失败:", err);
          // 如果有照片字段但处理异常，不设置默认图片
          images.value = [];
          isUsingDefaultImage.value = true;
        }
      } else {
        // 只有在完全没有项目照片字段时才设置默认图片
        images.value = [defaultMapImg];
        isUsingDefaultImage.value = true;
      }

      // 保存视频信息
      if (data.项目视频) {
        // 可以在这里处理视频信息，如果需要的话
        // videoUrl.value = data.项目视频;
      }
    } else {
      // 接口返回错误，设置默认图片
      images.value = [defaultMapImg];
      isUsingDefaultImage.value = true;
    }
  } catch (err) {
    console.error("获取项目数据失败:", err);
    // 接口异常，设置默认图片
    images.value = [defaultMapImg];
    isUsingDefaultImage.value = true;
  }
}

const backgroundStyle = computed(() => {
  // 强制computed重新计算的关键一步
  const imageIndex = currentImageIndex.value;

  // 确保images数组有效并且当前索引有效
  let currentImg;

  try {
    if (images.value && images.value.length > 0) {
      // 确保索引在有效范围内
      const validIndex = imageIndex >= 0 && imageIndex < images.value.length
        ? imageIndex
        : 0;
      currentImg = images.value[validIndex];

      // 额外检查URL格式是否有效
      if (typeof currentImg !== 'string' || (!currentImg.startsWith('http') && !currentImg.startsWith('@/'))) {
        currentImg = defaultMapImg;
      }
    } else {
      // 如果images数组为空，不设置背景图片
      currentImg = null;
    }
  } catch (err) {
    console.error('处理背景图片URL失败:', err);
    currentImg = null;
  }
  console.log(currentImg);

  // 如果没有图片，返回空的背景样式
  if (!currentImg) {
    return {
      backgroundColor: "transparent",
    };
  }

  return {
    backgroundImage: `url(${currentImg})`,
    backgroundRepeat: "no-repeat",
    backgroundPosition: "center",
    backgroundSize: "100% 100%",
  };
});

onMounted(() => {
  // let btnsActive = localStorage.getItem("btnsActive");
  // if (btnsActive) {
  //   btnsActive = [0, 1, 2].includes(Number(btnsActive))
  //     ? Number(btnsActive)
  //     : 0;
  //   btnsChange(btnsActive);
  // }

  // 使用新的统一接口获取数据
  fetchProjectData();
});

const back = () => {
  localStorage.removeItem("clickProject");

  // 构建要传递的数据
  const transferData = {
    level: '',
    title: ''
  };

  // 判断是国内还是国外项目
  const clickProject = sessionStorage.getItem("clickProject");
  if (clickProject) {
    try {
      const queryData = JSON.parse(clickProject || "{}");
      const isGw = queryData.isGw === "true";

      if (isGw) {
        // 国际项目：传递level和国家
        transferData.level = 'country';
        transferData.title = projectData.value.国家 || title.value;
      } else {
        // 中国项目：传递level和区域
        transferData.level = 'district';
        transferData.title = projectData.value.区 || '';
      }

      // 如果没有区级数据，尝试使用市级数据
      if (!transferData.title && !isGw) {
        transferData.level = 'city';
        transferData.title = projectData.value.市 || '';
      }

      // 如果没有市级数据，使用省级数据
      if (!transferData.title && !isGw) {
        transferData.level = 'province';
        transferData.title = projectData.value.省 || '';
      }
    } catch (e) {
      console.error("解析项目数据失败", e);
    }
  }

  // 使用sessionStorage存储数据而不是通过URL参数
  sessionStorage.setItem("countryLevel", transferData.level);
  sessionStorage.setItem("countryTitle", transferData.title);

  router.replace({
    path: "/project/country"
  });
};

const backHome = () => {
  // 清除所有相关状态数据
  localStorage.removeItem("isChina");
  localStorage.removeItem("clickProject");
  sessionStorage.removeItem("countryLevel");
  sessionStorage.removeItem("countryTitle");
  sessionStorage.removeItem("clickCountry");
  sessionStorage.removeItem("lastProjectState");
  sessionStorage.removeItem("clickProject");
  
  // 导航到首页
  router.push("/project/index");
  // 使用延迟确保在页面加载后发送消息切换到地球模式
  setTimeout(() => {
      if (!iframe.value) {
        iframe.value = document.getElementById("iframe");
      }
      if (iframe.value && iframe.value.contentWindow) {
        // 发送消息切换到地球模式
        iframe.value.contentWindow.postMessage(
          { eve: "changeModel", data: "earth",model: "earth" },
          "*"
        );
      }
    }, 500);
};

const handleThumbnailClick = (index, image) => {
  const img = new window.Image();
  img.onload = () => {
    currentImageIndex.value = index;
  };
  img.src = image;
  if (img.complete) {
    currentImageIndex.value = index;
  }
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  pointer-events: all;
  position: relative;
  z-index: 1;

  .bg {
    width: 100%;
    height: 150px;
    position: absolute;
    top: -28px;
    left: 0;
    background: url("@/assets/images/header/headerBgFont.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 3;
  }

  .topBg {
    width: 100%;
    height: 240px;
    position: absolute;
    top: 0;
    right: 0;
    background: url("@/assets/images/yingdi/top.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 2;
  }

  .bottomBg {
    width: 100%;
    height: 160px;
    position: absolute;
    left: 0;
    bottom: 0;
    // background: url("@/assets/images/yingdi/bottom.png") no-repeat;
    background-size: 100% 100%;
    pointer-events: none;
    z-index: 2;
  }

  .btns {
    width: 36px;
    height: 500px;
    position: absolute;
    top: 115px;
    left: 5px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 6;

    >div {
      width: 36px;
      height: 160px;
      background: url("@/assets/images/project/btn.png") no-repeat;
      background-size: 100% 100%;
      color: #97acb4;
      box-sizing: border-box;
      padding: 0 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      cursor: pointer;

      &.active {
        background: url("@/assets/images/project/btnActive.png") no-repeat;
        background-size: 100% 100%;
        color: #fff;
      }
    }
  }

  .map {
    width: 100%;
    height: 100%;
    // background: url("@/assets/images/project/mapImg.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }
}
.back-button {
    position: absolute;
    top: 25px;
    left: 170px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
}
.backHome-button {
    position: absolute;
    top: 25px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 8px;
    pointer-events: auto;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
    font-weight: normal;
    font-size: 16px;
    text-shadow: 0px 0px 4px #0085FF;
    text-align: right;
    font-style: normal;
    text-transform: none;
    img{
      width: 18px;
      height: 18px;
    }
}

// 缩略图容器样式
.thumbnails-container {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 12px;
  z-index: 8;
  pointer-events: auto;
  max-height: 80vh;
  overflow-y: auto;
  
  // 隐藏滚动条但保留滚动功能
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.thumbnail {
  width: 100px;
  height: 70px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  
  &:hover {
    border-color: rgba(0, 89, 255, 0.6);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 89, 255, 0.3);
  }
  
  &.active {
    border-color: #0080ff;
    box-shadow: 0 0 15px rgba(0, 47, 255, 0.6);
    transform: scale(1.08);
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
    
    &:hover {
      opacity: 0.9;
    }
  }
}
</style>
