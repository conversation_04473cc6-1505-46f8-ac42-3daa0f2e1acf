class CSS2DObject extends THREE.Object3D {

	constructor( element = document.createElement( 'div' ) ) {

		super();

		this.isCSS2DObject = true;

		this.isAvoid = false;		//鏄惁寮€鍚伩璁�
		this.avoidHideCallback = null;	//閬胯闅愯棌瀵硅薄鍥炶皟
		this.avoidShowCallback = null;	//閬胯鏄剧ず瀵硅薄鍥炶皟

		this.element = element;

		this.element.style.position = 'absolute';
		this.element.style.userSelect = 'none';

		this.element.setAttribute( 'draggable', false );

		this.addEventListener( 'removed', function () {

			this.traverse( function ( object ) {

				if ( object.element instanceof Element && object.element.parentNode !== null ) {

					object.element.parentNode.removeChild( object.element );

				}

			} );

		} );

	}

	copy( source, recursive ) {

		super.copy( source, recursive );

		this.element = source.element.cloneNode( true );

		return this;

	}

}

//

const _vector = new THREE.Vector3();
const _viewMatrix = new THREE.Matrix4();
const _viewProjectionMatrix = new THREE.Matrix4();
const _a = new THREE.Vector3();
const _b = new THREE.Vector3();

class CSS2DRenderer {

	constructor( parameters = {} ) {

		const _this = this;

		let _width, _height;
		let _widthHalf, _heightHalf;

		const cache = {
			objects: new WeakMap()
		};

		const domElement = parameters.element !== undefined ? parameters.element : document.createElement( 'div' );

		domElement.style.overflow = 'hidden';

		this.domElement = domElement;

		this.enableAvoid = false;	//鏄惁寮€鍚伩璁�
		this.showAvoids = new Map();

		this.getSize = function () {

			return {
				width: _width,
				height: _height
			};

		};

		this.render = function ( scene, camera ) {

			if ( scene.matrixWorldAutoUpdate === true ) scene.updateMatrixWorld();
			if ( camera.parent === null && camera.matrixWorldAutoUpdate === true ) camera.updateMatrixWorld();

			_viewMatrix.copy( camera.matrixWorldInverse );
			_viewProjectionMatrix.multiplyMatrices( camera.projectionMatrix, _viewMatrix );
			renderObject( scene, scene, camera );
			zOrder( scene );

		};

		this.setSize = function ( width, height ) {

			_width = width;
			_height = height;

			_widthHalf = _width / 2;
			_heightHalf = _height / 2;

			domElement.style.width = width + 'px';
			domElement.style.height = height + 'px';

		};

		function getParentVisible(object,parentVisible){
			if(!object.visible || !object.parent){
				parentVisible.visible = object.visible;
				return;
			}else{
				if(object.parent.visible) getParentVisible(object.parent,parentVisible);
				else{
					parentVisible.visible = false;
					return;
				}
			}
		}

		/*
		* 鍒ゆ柇闈㈤潰鏄惁鍖呭惈
		*/
		function isPoly(poly, poly1) {
			var flag = false;
			for (let i = 0; i < poly.length; i++) {
				flag = isInsidePolygon(poly[i], poly1);
				if (flag) break;
			}
			if (!flag) {
				for (let i = 0; i < poly1.length; i++) {
					flag = isInsidePolygon(poly1[i], poly);
					if (flag) break;
				}
			}
			return flag;
		}
		/*
		鍒ゆ柇鐐规槸鍚﹀湪澶氳竟褰㈠唴
		pt:[x,y]  鐐�
		poly锛歔[],[]] 闈㈠潗鏍�
		*/
		function isInsidePolygon(pt, poly) {
			for (var c = false, i = -1, l = poly.length, j = l - 1; ++i < l; j = i)
			((poly[i][0] <= pt[0] && pt[0] < poly[j][0]) || (poly[j][0] <= pt[0] && pt[0] < poly[i][0])) &&
			(pt[1] < (poly[j][1] - poly[i][1]) * (pt[0] - poly[i][0]) / (poly[j][0] - poly[i][0]) + poly[i][1]) &&
			(c = !c);
			return c;
		}

		function renderObject( object, scene, camera ) {

			if ( object.isCSS2DObject ) {

				_vector.setFromMatrixPosition( object.matrixWorld );
				_vector.applyMatrix4( _viewProjectionMatrix );

				let visible = ( object.visible === true ) && ( _vector.z >= - 1 && _vector.z <= 1 ) && ( object.layers.test( camera.layers ) === true );
				let parentVisible = {visible:visible};
				getParentVisible(object,parentVisible);
				visible = parentVisible.visible;
				
				let CheckVisible = visible;
				if(_this.enableAvoid && object.isAvoid){
					if(!_this.showAvoids.has(object.uuid)){
						_this.showAvoids.set(object.uuid,object);
					}
					let start = {x:_vector.x * _widthHalf + _widthHalf,y:- _vector.y * _heightHalf + _heightHalf};
					let userRect = object.userRect || {width:0,height:0};
					let rect = [[start.x,start.y],[start.x,start.y+userRect.height],[start.x+userRect.width,start.y+userRect.height],[start.x+userRect.width,start.y]];
					let flag = false;
					for(let [key,_obj] of _this.showAvoids){
							if(key==object.uuid || _obj.isCheckAvoidHide == "none") continue;
							start = {x:_obj.px,y:_obj.py};
							let userRect = _obj.userRect || {width:0,height:0};
							let _rect = [[start.x,start.y],[start.x,start.y+userRect.height],[start.x+userRect.width,start.y+userRect.height],[start.x+userRect.width,start.y]];
							flag = isPoly(_rect,rect);
							if(flag) break;
					}
					if(flag){
						if(object.avoidHideCallback){
							object.avoidHideCallback(object.element);
						}else{
							visible = false;
						}
						CheckVisible = false;
					}else{
						if(visible){
							if(object.avoidShowCallback){
								object.avoidShowCallback(object.element);
							}
						}
					}
				}

				object.element.style.display = ( visible === true ) ? '' : 'none';
				object.isCheckAvoidHide = ( CheckVisible === true ) ? '' : 'none';

				object.px = _vector.x * _widthHalf + _widthHalf;
				object.py = - _vector.y * _heightHalf + _heightHalf;

				if ( visible === true ) {

					object.onBeforeRender( _this, scene, camera );

					const element = object.element;
					// scale(1.5);
					let scale = 1;
					if(object.isDistance){
						let distance = object.position.distanceTo(camera.position);
						scale = 1 - distance / 500;
						if(scale>1) scale = 1;
						else if(scale<0.1) scale = 0.1;
					}
					element.style.transform = 'translate(-50%,-50%) translate(' + ( _vector.x * _widthHalf + _widthHalf ) + 'px,' + ( - _vector.y * _heightHalf + _heightHalf ) + 'px) scale('+ scale +')';

					if ( element.parentNode !== domElement ) {

						domElement.appendChild( element );

					}

					if( !object.userRect ) object.userRect = object.element.getBoundingClientRect();

					
					object.onAfterRender( _this, scene, camera );
					
				}

				const objectData = {
					distanceToCameraSquared: getDistanceToSquared( camera, object )
				};

				cache.objects.set( object, objectData );

			}

			for ( let i = 0, l = object.children.length; i < l; i ++ ) {

				renderObject( object.children[ i ], scene, camera );

			}

		}

		function getDistanceToSquared( object1, object2 ) {

			_a.setFromMatrixPosition( object1.matrixWorld );
			_b.setFromMatrixPosition( object2.matrixWorld );

			return _a.distanceToSquared( _b );

		}

		function filterAndFlatten( scene ) {

			const result = [];

			scene.traverse( function ( object ) {

				if ( object.isCSS2DObject ) result.push( object );

			} );

			return result;

		}

		function zOrder( scene ) {

			const sorted = filterAndFlatten( scene ).sort( function ( a, b ) {

				if ( a.renderOrder !== b.renderOrder ) {

					return b.renderOrder - a.renderOrder;

				}

				const distanceA = cache.objects.get( a ).distanceToCameraSquared;
				const distanceB = cache.objects.get( b ).distanceToCameraSquared;

				return distanceA - distanceB;

			} );

			const zMax = sorted.length;

			for ( let i = 0, l = sorted.length; i < l; i ++ ) {

				sorted[ i ].element.style.zIndex = zMax - i;

			}

		}

	}

}
THREE.CSS2DRenderer = CSS2DRenderer;
THREE.CSS2DObject = CSS2DObject;
