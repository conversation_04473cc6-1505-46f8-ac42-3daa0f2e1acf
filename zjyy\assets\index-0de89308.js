import{_ as e,u as a,d as s,e as l,G as t,o as r,h as o,i,l as d,w as n,H as p,I as u,E as m,J as c,K as v,L as A}from"./vendor-22dca409.js";import{_ as g,r as f,s as h,a as S}from"./index-8aea96a7.js";const k=""+new URL("loginBgV-dbb246af.mp4",import.meta.url).href,w=""+new URL("title-44ef00de.png",import.meta.url).href,y={class:"login-container"},E={class:"account"},b={class:"loginSvg"},x={class:"password"},I={class:"loginSvg"},V=e({__name:"index",setup(e){const V=a(),R=s(null),U=s(!1),C=s(null);l((()=>{C.value&&C.value.addEventListener("ended",(()=>{C.value.currentTime=C.value.duration}))}));const L=t({username:"",password:""}),Z={username:[{required:!0,message:"请输入账号",trigger:"blur"},{min:3,max:20,message:"账号长度应在 3 到 20 个字符之间",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度应在 6 到 20 个字符之间",trigger:"blur"}]},q=async()=>{if(R.value)try{U.value=!0,await R.value.validate();const e=await f.post("/system/auth/login",{username:L.username,password:L.password});0===e.code?(h(e.data.accessToken,e.data.refreshToken),m.success("登录成功"),V.push("/")):m.error(e.msg||"登录失败")}catch(e){m.error("登录失败，请检查账号密码")}finally{U.value=!1}};return(e,a)=>{const s=S,l=c,t=v,m=A,f=p;return r(),o("div",y,[i("video",{class:"login-bg-video",src:k,autoplay:"",muted:"",ref_key:"videoRef",ref:C},null,512),a[4]||(a[4]=i("div",{class:"header-container relative z-[3]"},[i("img",{class:"absolute top-[-115px] left-[50%] translate-x-[-50%] w-[1920px] h-[395px]",src:g,alt:""}),i("img",{class:"absolute top-[-46px] w-full",src:w,alt:""}),i("div",{class:"absolute w-full text-center text-[42px] font-family-youshebiaotihei tracking-[8px] leading-[90px] title"}," 中江集团全球经营管理数字化平台 ")],-1)),d(f,{style:{"background-color":"transparent",border:"none"},class:"login-box"},{default:n((()=>[d(m,{ref_key:"loginFormRef",ref:R,model:L,rules:Z,class:"login-form"},{default:n((()=>[d(t,{prop:"username"},{default:n((()=>[i("div",E,[i("div",b,[d(s,{name:"user-3-fill",color:"#fff",style:{width:"26px",height:"26px"}}),a[2]||(a[2]=u("账 号 "))]),d(l,{class:"inputAccount",modelValue:L.username,"onUpdate:modelValue":a[0]||(a[0]=e=>L.username=e),placeholder:"请输入账号","prefix-icon":"User"},null,8,["modelValue"])])])),_:1}),d(t,{prop:"password"},{default:n((()=>[i("div",x,[i("div",I,[d(s,{name:"handbag-fill",color:"#fff",style:{width:"26px",height:"26px"}}),a[3]||(a[3]=u("密 码 "))]),d(l,{class:"inputPwd",modelValue:L.password,"onUpdate:modelValue":a[1]||(a[1]=e=>L.password=e),type:"password",placeholder:"请输入密码","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])])])),_:1}),d(t,{style:{width:"100%"}},{default:n((()=>[i("div",{class:"loginBtn"},[i("img",{onClick:q,src:"data:image/png;base64,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",alt:""})])])),_:1})])),_:1},8,["model"])])),_:1})])}}},[["__scopeId","data-v-45c84ad4"]]);export{V as default};
