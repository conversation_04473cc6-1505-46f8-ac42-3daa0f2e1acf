<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <title>中江运营大屏</title>
    <!-- <link href="css/index.css" rel="stylesheet" /> -->
    <!-- <script type="text/javascript" src="http://gis.ztmapinfo.com/lib/ztmap3d-lib.js" include="jquery,ztmapgl,ztmapglthree,ztmapglthreeloader"></script> -->
    <script src="js/jquery-3.7.1.min.js"></script>

    <script src="js/three.min.js"></script>
    <script src="js/OrbitControls.js"></script>
    <script src="js/render.js"></script>
    <script src="js/CSS2DRenderer.js"></script>
    <script src="js/stats.min.js"></script>
    <script src="js/Events.js"></script>
    <script src="js/animate.js"></script>
    <!-- <script src="loader20250306/ZTMapModels-new.min.js"></script> -->
    <script src="js/turf.js"></script>
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script> -->
    <script src="js/xcsp.js"></script>
    <style>
        body,
        html {
            height: 100%;
            width: 100%;
            margin: 0;
            padding: 0;
            border: none;
            overflow: hidden;
        }
        body{
            background: linear-gradient(0deg, transparent, #afd9e7);
            background: url(img/bg-new.png) no-repeat;
            background-size: 100% 100%;
        }
        body[code="plan"]{
            background: url(img/bg-new.png) no-repeat;
            background-size: 100% 100%;
        }
        ::-webkit-scrollbar {
            width: 6px; /*滚动条宽度*/
            height: 10px; /*滚动条高度*/
        } /*定义滚动条轨道 内阴影+圆角*/
        ::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            border-radius: 10px; /*滚动条的背景区域的圆角*/
            background-color: #244e83; /*滚动条的背景颜色*/
        } /*定义滑块 内阴影+圆角*/
        ::-webkit-scrollbar-thumb {
            border-radius: 10px; /*滚动条的圆角*/
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            background-color: #4f96f4; /*滚动条的背景颜色*/
        }
        #map {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            position: relative;
        }
        .cir1{
            position: absolute;
            bottom: 10%;
            width: 50%;
            left: 25%;
            animation: showAnimate 1.55s infinite linear alternate; /* 动画名称，持续时间，无限循环 */
            opacity: 0;
            filter: blur(3px);
            display: none;
        }
        @keyframes showAnimate {
            0% { opacity:0 }
            /* 50% { opacity:1 } */
            100% { opacity:1 }
        }
        .cir2{
            position: absolute;
            bottom: 10%;
            width: 50%;
            left: 25%;
            animation: sacleAnimate 3.1s infinite linear; /* 动画名称，持续时间，无限循环 */
            opacity: 0;
            filter: blur(3px);
            display: none;
        }
        @keyframes sacleAnimate {
            0% { opacity:0.5;transform: scale(1.3); }
            /* 50% { opacity:1 } */
            100% { opacity:0;transform: scale(2); }
        }
        .countryName{
            color: #fff;
            cursor: pointer;
            font-size: 13px;
            text-shadow: 3px 3px 1px black;
            /* margin-top: -15px; */
            pointer-events: all;
            filter: drop-shadow(1px 1px 1px black);
            text-align: center;
        }
        .countryTitle_bg{
            pointer-events: all;
            margin-bottom:70px;
        }
        .countryName[code="yd"]{
            color: #fece61;
        }
        .demoBg{
            position: relative;
            left: calc(50% - 25px);
            margin-top: calc(-50% + 50px);
        }
        .demoBg_title{
            color: #fff;
            cursor: pointer;
            font-size: 13px;
            text-shadow: 3px 3px 1px black;
            pointer-events: all;
            max-width: 200px;
            text-align: center;
            background: url(img/demo_bg.png) no-repeat;
            background-size: 100% 100%;
            padding: 20px 15px;
            margin-left: 50px;
            position: relative;
            top: 8px;
        }
        .demoBg_zz{
            background: url(img/demo_bg_zz.png) no-repeat;
            background-size: 100% 100%;
            width: 50px;
            height: 50px;
        }
        .demoBg[type='yd'] .demoBg_title{
            background: url(img/demo_bg1.png) no-repeat;
            background-size: 100% 100%;
        }
        .demoBg[type='yd'] .demoBg_zz{
            background: url(img/demo_bg1_zz.png) no-repeat;
            background-size: 100% 100%;
        }
        .jdx{
            position: absolute;
            bottom: 50px;
            right: 50px;
            filter: drop-shadow(0px 0px 6px #2e72d7);
            display: none;
        }
        .countrySxt{
            width: 36px;
            position: relative;
            left: calc(50% - 18px);
        }
        .sxtImg{
            pointer-events: all;
            width: 30px;
            cursor: pointer;
            /* margin-top: -50px; */
            margin-left: calc(50% - 15px);
        }
        .sxtTitle{
            color: #fff;
            text-shadow: 3px 3px 1px black;
            font-size: 14px;
            font-weight: bold;
        }
        .sxtImg_bg{
            margin-top: -50px;
            pointer-events: all;
            cursor: pointer;
            
        }
    </style>
</head>
<body>
    <img class="cir2" src="img/cir.png" />
    <img class="cir1" src="img/cir.png" />
    <img class="jdx" src="img/jdx.jpg"/>
    <div id="map"></div>
    <script>
        
    </script>
</body>
</html>